"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mysqlaa_1 = require("@ltaq/mysqlaa");
const ResourceStore_1 = require("./server/ResourceStore");
const HttpServer_1 = require("./server/HttpServer");
const ConfigUtils_1 = require("./utils/ConfigUtils");
const DBPool_1 = require("./db/DBPool");
const fecha_1 = __importDefault(require("fecha"));
const ExternalRequestStore_1 = require("./store/ExternalRequestStore");
const KeynotePeopleAlarmStore_1 = require("./store/KeynotePeopleAlarmStore");
const KeynotePeopleStore_1 = require("./store/KeynotePeopleStore");
const ContrastTaskStore_1 = require("./store/ContrastTaskStore");
//接入pgsql
const pg = require('pg');
const poolConfig = {
    user: ConfigUtils_1.ConfigUtils.database_pg.user,
    password: ConfigUtils_1.ConfigUtils.database_pg.password,
    host: ConfigUtils_1.ConfigUtils.database_pg.host,
    port: ConfigUtils_1.ConfigUtils.database_pg.port,
    database: ConfigUtils_1.ConfigUtils.database_pg.database,
    max: 5,
    idleTimeoutMillis: 3000,
};
const pgPool = new pg.Pool(poolConfig);
pgPool.connect();
let pool = new DBPool_1.DBPool((0, mysqlaa_1.createPool)(ConfigUtils_1.ConfigUtils.database));
//实例化一次，做关联表查询统一在此类实现
let resLocal = new ResourceStore_1.ResourceStore(pool, pgPool);
let http_server = new HttpServer_1.HttpServer(resLocal, ConfigUtils_1.ConfigUtils.port, ConfigUtils_1.ConfigUtils.filesUrl);
http_server.start();
const externalRequest = new ExternalRequestStore_1.ExternalRequestStore(pool, pgPool);
const keynotePeopleAlarm = new KeynotePeopleAlarmStore_1.KeynotePeopleAlarmStore(pool, pgPool);
const keynotePeople = new KeynotePeopleStore_1.KeynotePeopleStore(pool, pgPool);
const contrastTaskStore = new ContrastTaskStore_1.ContrastTaskStore(pool, pgPool);
// dataSync();
// runSync();
async function dataSync() {
    let schedule = require("node-schedule");
    await keynotePeople.getKeynotePeopleTZ();
    schedule.scheduleJob('0 0 6 * * *', () => {
        keynotePeople.getKeynotePeopleTZ();
    });
    await keynotePeople.getKeynotePeopleQG();
    schedule.scheduleJob('0 5 6 * * *', () => {
        keynotePeople.getKeynotePeopleQG();
    });
    await keynotePeopleAlarm.getHotelAccommodationSuspects();
    schedule.scheduleJob('0 10 6 * * *', () => {
        keynotePeopleAlarm.getHotelAccommodationSuspects();
    });
}
async function runSync() {
    try {
        let contrastTask = await contrastTaskStore.searchListData('');
        for (let item of contrastTask) {
            await contrastTaskStore.startContrast(item);
        }
        //doSync2();    //人员名单库请求量过大，需要排查问题
        await doSync4();
        let schedule = require("node-schedule");
        schedule.scheduleJob('1 1 1 1 * ?', () => {
            //doSync2();
            doSync4();
        });
        await doSync3();
        schedule.scheduleJob('1 1 1 * * 1', () => {
            doSync3();
        });
        // doSync5();
        // schedule.scheduleJob('1 1 * * * *', () => {
        //     doSync5();
        // });
    }
    catch (ex) {
        console.log(ex);
    }
}
async function doSync2() {
    try {
        let num = 0;
        let dataList = await keynotePeople.searchListData('where sfzh not like "%*%"');
        if (dataList.length > 100) {
            let i = Math.ceil(dataList.length / 100);
            let list = [];
            for (let j = i; j > 0; j--) {
                list.push(dataList.slice((j - 1) * 100, j * 100));
            }
            for (i--; i >= 0; i--) {
                let res2 = await (0, KeynotePeopleStore_1.addlistLibrary)(list[i]); //重复执行函数
                await (0, ExternalRequestStore_1.wait)(1 * 1000); //延迟请求，防止频繁请求导致崩溃
            }
        }
        else {
            await (0, KeynotePeopleStore_1.addlistLibrary)(dataList);
        }
    }
    catch (e) {
        console.log("失败" + e);
    }
}
async function doSync3() {
    try {
        let nowTime = fecha_1.default.format(new Date(), 'YYYY-MM-DD 00:00:00');
        let beforeTime = fecha_1.default.format(new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000), 'YYYY-MM-DD 00:00:00');
        let inoutList = await (0, KeynotePeopleStore_1.getInout)(beforeTime, nowTime);
        if (inoutList && inoutList.length > 0) {
            for (let item of inoutList) {
                let shipRegistration = await (0, KeynotePeopleStore_1.getShipRegistration)(item.ship_name);
                if (shipRegistration) {
                    await (0, ExternalRequestStore_1.wait)(3 * 1000); //延迟请求，防止频繁请求导致崩溃
                    let shipMember = await (0, KeynotePeopleStore_1.getShipMember)(shipRegistration.ship_operation_code);
                    let inoutMemberList = await (0, KeynotePeopleStore_1.getInoutMember)(item.source_id);
                    for (let item of shipMember) {
                        if (!item.id_type.includes('身份证') || item.id_number.includes('*')) {
                            console.log("疑似离船人员-船员数据无法匹配：", item.id_number);
                        }
                        else {
                            if (!inoutMemberList.includes(item.id_number)) {
                                await keynotePeopleAlarm.addData2({
                                    id: 0,
                                    sfzh: item.id_number,
                                    name: item.name,
                                    sex: item.gender,
                                    alarm_type: '疑似离船人员',
                                    data: JSON.stringify(item),
                                    source: '定时疑似离船人员服务',
                                    other: '',
                                    create_time: fecha_1.default.format(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                                    dwd_xxrksj: '',
                                    task_name: '',
                                    task_batch: 0,
                                    task_id: ''
                                });
                            }
                        }
                    }
                }
            }
        }
    }
    catch (e) {
        console.log("失败" + e);
    }
}
async function doSync4() {
    try {
        let dataList = await keynotePeople.searchListData('where sfzh not like "%*%"');
        for (let item of dataList) {
            let resData = await (0, ExternalRequestStore_1.getCarNumber)(item.sfzh);
            if (resData && resData.data && resData.data.list && resData.data.list.length > 0) { //bz_jdchphm
                let items = resData.data.list;
                let item;
                if (resData.data.list.length > 1) {
                    let j = 0;
                    let time = new Date(0);
                    for (let i = 0; i < items.length; i++) {
                        let nowDate = new Date(items[i].fprq_rq);
                        if (nowDate > time) {
                            time = nowDate;
                            j = i;
                        }
                    }
                    item = items[j];
                }
                else {
                    item = items[0];
                }
                let plateColor = 'blue';
                switch (item.jdchpzldm) {
                    case '01':
                        plateColor = 'yellow';
                        break;
                    case '15':
                        plateColor = 'yellow';
                        break;
                    case '07':
                        plateColor = 'yellow';
                        break;
                    case '19':
                        plateColor = 'yellow';
                        break;
                    case '13':
                        plateColor = 'yellow';
                        break;
                    case '17':
                        plateColor = 'yellow';
                        break;
                    case '16':
                        plateColor = 'yellow';
                        break;
                    case '51':
                        plateColor = 'yellow';
                        break;
                    case '52':
                        plateColor = 'green';
                        break;
                    case '02':
                        plateColor = 'blue';
                        break;
                    case '08':
                        plateColor = 'blue';
                        break;
                    default:
                        plateColor = 'blue';
                        break;
                }
                let insertData = {
                    vehicleLibId: '1695018830811',
                    plateNo: item.bz_jdchphm,
                    plateColor: plateColor,
                    vehicleType: '',
                    vehicleLogo: '',
                    vehicleColor: '',
                    issueAuth: item.gajtglfzjgslmc || '',
                    ownerName: item.jdcsyrmc || '',
                    phoneNo: item.yddh || '',
                    address: item.jdczt_dmbcms || '',
                    vehicleModel: ''
                };
                await (0, ExternalRequestStore_1.addVehicle)(insertData);
                await (0, ExternalRequestStore_1.wait)(10 * 1000); //延迟请求，防止频繁请求导致崩溃
            }
        }
    }
    catch (e) {
        console.log("失败" + e);
    }
}
async function doSync5() {
    try {
        let resData = await (0, ExternalRequestStore_1.getHotel)();
        if (resData && resData.list && resData.list.length > 0) {
            let dataList = resData.list;
            for (let item of dataList) {
                if (item.sfzh && item.cl_rqgs_rz_sj) {
                    let alarmData = await keynotePeopleAlarm.searchListData(` where sfzh = '${item.sfzh}' and source = '旅馆住宿'`);
                    if (alarmData.length == 0) {
                        await keynotePeopleAlarm.addData2({
                            id: 0,
                            sfzh: item.sfzh || '',
                            name: item.xm || '',
                            sex: item.xb || '',
                            alarm_type: '涉案人员',
                            data: JSON.stringify(item),
                            source: '旅馆住宿',
                            other: '',
                            create_time: fecha_1.default.format(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                            dwd_xxrksj: item.cl_qigs_rz_sj,
                            task_name: '',
                            task_batch: 0,
                            task_id: ''
                        });
                    }
                }
            }
        }
    }
    catch (e) {
        console.log("失败" + e);
    }
}
//# sourceMappingURL=index.js.map