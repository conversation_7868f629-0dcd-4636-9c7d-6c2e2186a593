"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DBPool = void 0;
class DBPool {
    _pool;
    constructor(_pool) {
        this._pool = _pool;
    }
    get pool() {
        return this._pool;
    }
    async loadRow(sql, params) {
        return await this._pool.loadRow(sql, params);
    }
    async loadRows(sql, params) {
        return await this._pool.loadRows(sql, params);
    }
    async query(sql, values) {
        return await this._pool.query(sql, values);
    }
    async loadScalar(sql, values) {
        return await this._pool.loadScalar(sql, values);
    }
}
exports.DBPool = DBPool;
//# sourceMappingURL=DBPool.js.map