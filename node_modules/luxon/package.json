{"name": "luxon", "version": "3.6.1", "description": "Immutable date wrapper", "author": "<PERSON>", "keywords": ["date", "immutable"], "repository": "https://github.com/moment/luxon", "exports": {".": {"import": "./src/luxon.js", "require": "./build/node/luxon.js"}, "./package.json": "./package.json"}, "scripts": {"build": "babel-node tasks/buildAll.js", "build-node": "babel-node tasks/buildNode.js", "build-global": "babel-node tasks/buildGlobal.js", "jest": "jest", "test": "jest --coverage", "api-docs": "mkdir -p build && documentation build src/luxon.js -f html -o build/api-docs && sed -i.bak 's/<\\/body>/<script src=\"\\..\\/global\\/luxon.js\"><\\/script><script>console.log(\"You can try <PERSON><PERSON> right here using the `luxon` global, like `luxon.DateTime.now()`\");<\\/script><\\/body>/g' build/api-docs/index.html && rm build/api-docs/index.html.bak", "copy-site": "mkdir -p build && rsync -a docs/ build/docs && rsync -a site/ build", "site": "npm run api-docs && npm run copy-site", "format": "prettier --write 'src/**/*.js' 'test/**/*.js' 'benchmarks/*.js'", "format-check": "prettier --check 'src/**/*.js' 'test/**/*.js' 'benchmarks/*.js'", "benchmark": "node benchmarks/index.js", "codecov": "codecov", "prepack": "babel-node tasks/buildAll.js", "prepare": "husky install", "show-site": "http-server build"}, "lint-staged": {"*.{js,json}": ["prettier --write"]}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/node": "^7.18.6", "@babel/plugin-external-helpers": "^7.18.6", "@babel/preset-env": "^7.18.6", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "babel-jest": "^28.1.2", "benchmark": "latest", "codecov": "latest", "documentation": "latest", "fs-extra": "^6.0.1", "http-server": "^14.1.1", "husky": "^7.0.0", "jest": "^29.4.3", "lint-staged": "^13.2.1", "prettier": "latest", "rollup": "^2.52.7", "rollup-plugin-terser": "^7.0.2", "uglify-js": "^3.13.10"}, "main": "build/node/luxon.js", "module": "src/luxon.js", "browser": "build/cjs-browser/luxon.js", "jsdelivr": "build/global/luxon.min.js", "unpkg": "build/global/luxon.min.js", "engines": {"node": ">=12"}, "files": ["build/node/luxon.js", "build/node/luxon.js.map", "build/cjs-browser/luxon.js", "build/cjs-browser/luxon.js.map", "build/amd/luxon.js", "build/amd/luxon.js.map", "build/global/luxon.js", "build/global/luxon.js.map", "build/global/luxon.min.js", "build/global/luxon.min.js.map", "build/es6/luxon.js", "build/es6/luxon.js.map", "src"], "license": "MIT", "sideEffects": false}