on: [push, pull_request]
name: Build, Test and maybe Publish
jobs:
  test:
    name: Build & Test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [12.x, 14.x]
    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}
    - name: Cache node_modules
      id: cache-modules
      uses: actions/cache@v1
      with:
        path: node_modules
        key: ${{ matrix.node-version }}-${{ runner.OS }}-build-${{ hashFiles('package.json') }}
    - name: Build
      if: steps.cache-modules.outputs.cache-hit != 'true'
      run: npm install
    - name: Test
      run: npm_config_yes=true npx best-test@latest
  publish:
    name: Publish
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && ( github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main' )
    steps:
    - uses: actions/checkout@v2
    - name: Cache node_modules
      id: cache-modules
      uses: actions/cache@v1
      with:
        path: node_modules
        key: 12.x-${{ runner.OS }}-build-${{ hashFiles('package.json') }}
    - name: Build
      if: steps.cache-modules.outputs.cache-hit != 'true'
      run: npm install
    - name: Test
      run: npm_config_yes=true npx best-test@latest

    - name: Publish
      uses: mikeal/merge-release@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        NPM_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
