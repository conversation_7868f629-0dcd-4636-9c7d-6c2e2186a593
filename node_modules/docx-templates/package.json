{"name": "docx-templates", "version": "4.14.1", "description": "Template-based docx report creation", "main": "lib/index.js", "types": "lib/index.d.ts", "author": "<PERSON>", "license": "MIT", "keywords": ["docx", "office", "word", "ms-word", "report", "template"], "homepage": "https://github.com/guigrpa/docx-templates#readme", "bugs": {"url": "https://github.com/guigrpa/docx-templates/issues"}, "repository": {"type": "git", "url": "git+https://github.com/guigrpa/docx-templates.git"}, "scripts": {"prepare": "yarn compile", "compile": "rimraf ./lib && tsc && yarn rollup", "rollup": "rollup -c", "jest": "jest --watch --coverage", "test": "yarn lint && yarn testCovFull", "testCovFull": "yarn testCovPrepare && yarn testDev && yarn testCovReport", "testCovPrepare": "rm -rf ./coverage .nyc_output .nyc_tmp && mkdir .nyc_tmp", "testDev": "NODE_ENV=development jest --coverage && mv .nyc_output/coverage-final.json .nyc_tmp/coverage-dev.json && rm -rf .nyc_output", "testCovReport": "cp -r .nyc_tmp .nyc_output && nyc report --reporter=html --reporter=lcov --reporter=text", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint-fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\""}, "engines": {"node": ">=6"}, "dependencies": {"jszip": "^3.10.1", "sax": "1.3.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-node-resolve": "^13.1.3", "@rollup/plugin-replace": "^3.0.1", "@types/jest": "^29.5.0", "@types/node": "^20", "@types/qrcode": "1.5.0", "@types/sax": "^1.2.7", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "buffer": "^6.0.3", "coveralls": "^3.0.13", "esbuild": "^0.25.0", "eslint": "^8.37.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-prettier": "^4.2.1", "events": "^3.3.0", "jest": "^29.5.0", "mockdate": "^3.0.2", "nyc": "^15.0.1", "prettier": "^2.8.7", "qrcode": "1.5.1", "rimraf": "^3.0.2", "rollup": "^3.29.5", "rollup-plugin-dts": "^4.1.0", "rollup-plugin-esbuild": "^4.8.2", "stream-browserify": "^3.0.0", "ts-jest": "^29.1.0", "typescript": "5.4.5", "util": "^0.12.5", "vm-browserify": "^1.1.2"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "testTimeout": 10000, "testRegex": "src/.*__tests__/.*\\.(test|spec)\\.(ts|tsx)$", "coverageDirectory": ".nyc_output", "coverageReporters": ["json", "text", "html"], "collectCoverageFrom": ["src/**/*.ts", "!src/debug.ts", "!test/**", "!**/node_modules/**", "!**/__tests__/**", "!**/__mocks__/**"]}}