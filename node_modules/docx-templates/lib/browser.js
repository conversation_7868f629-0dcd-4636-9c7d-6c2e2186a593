var commonjsGlobal=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{},buffer$1={},base64Js={};base64Js.byteLength=byteLength,base64Js.toByteArray=toByteArray,base64Js.fromByteArray=fromByteArray;for(var lookup=[],revLookup=[],Arr=typeof Uint8Array!="undefined"?Uint8Array:Array,code="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i$2=0,len=code.length;i$2<len;++i$2)lookup[i$2]=code[i$2],revLookup[code.charCodeAt(i$2)]=i$2;revLookup[45]=62,revLookup[95]=63;function getLens(t){var l=t.length;if(l%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");r===-1&&(r=l);var E=r===l?0:4-r%4;return[r,E]}function byteLength(t){var l=getLens(t),r=l[0],E=l[1];return(r+E)*3/4-E}function _byteLength(t,l,r){return(l+r)*3/4-r}function toByteArray(t){var l,r=getLens(t),E=r[0],T=r[1],A=new Arr(_byteLength(t,E,T)),N=0,y=T>0?E-4:E,ue;for(ue=0;ue<y;ue+=4)l=revLookup[t.charCodeAt(ue)]<<18|revLookup[t.charCodeAt(ue+1)]<<12|revLookup[t.charCodeAt(ue+2)]<<6|revLookup[t.charCodeAt(ue+3)],A[N++]=l>>16&255,A[N++]=l>>8&255,A[N++]=l&255;return T===2&&(l=revLookup[t.charCodeAt(ue)]<<2|revLookup[t.charCodeAt(ue+1)]>>4,A[N++]=l&255),T===1&&(l=revLookup[t.charCodeAt(ue)]<<10|revLookup[t.charCodeAt(ue+1)]<<4|revLookup[t.charCodeAt(ue+2)]>>2,A[N++]=l>>8&255,A[N++]=l&255),A}function tripletToBase64(t){return lookup[t>>18&63]+lookup[t>>12&63]+lookup[t>>6&63]+lookup[t&63]}function encodeChunk(t,l,r){for(var E,T=[],A=l;A<r;A+=3)E=(t[A]<<16&16711680)+(t[A+1]<<8&65280)+(t[A+2]&255),T.push(tripletToBase64(E));return T.join("")}function fromByteArray(t){for(var l,r=t.length,E=r%3,T=[],A=16383,N=0,y=r-E;N<y;N+=A)T.push(encodeChunk(t,N,N+A>y?y:N+A));return E===1?(l=t[r-1],T.push(lookup[l>>2]+lookup[l<<4&63]+"==")):E===2&&(l=(t[r-2]<<8)+t[r-1],T.push(lookup[l>>10]+lookup[l>>4&63]+lookup[l<<2&63]+"=")),T.join("")}var ieee754={};/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */ieee754.read=function(t,l,r,E,T){var A,N,y=T*8-E-1,ue=(1<<y)-1,Z=ue>>1,M=-7,Se=r?T-1:0,ge=r?-1:1,_e=t[l+Se];for(Se+=ge,A=_e&(1<<-M)-1,_e>>=-M,M+=y;M>0;A=A*256+t[l+Se],Se+=ge,M-=8);for(N=A&(1<<-M)-1,A>>=-M,M+=E;M>0;N=N*256+t[l+Se],Se+=ge,M-=8);if(A===0)A=1-Z;else{if(A===ue)return N?NaN:(_e?-1:1)*(1/0);N=N+Math.pow(2,E),A=A-Z}return(_e?-1:1)*N*Math.pow(2,A-E)},ieee754.write=function(t,l,r,E,T,A){var N,y,ue,Z=A*8-T-1,M=(1<<Z)-1,Se=M>>1,ge=T===23?Math.pow(2,-24)-Math.pow(2,-77):0,_e=E?0:A-1,we=E?1:-1,be=l<0||l===0&&1/l<0?1:0;for(l=Math.abs(l),isNaN(l)||l===1/0?(y=isNaN(l)?1:0,N=M):(N=Math.floor(Math.log(l)/Math.LN2),l*(ue=Math.pow(2,-N))<1&&(N--,ue*=2),N+Se>=1?l+=ge/ue:l+=ge*Math.pow(2,1-Se),l*ue>=2&&(N++,ue/=2),N+Se>=M?(y=0,N=M):N+Se>=1?(y=(l*ue-1)*Math.pow(2,T),N=N+Se):(y=l*Math.pow(2,Se-1)*Math.pow(2,T),N=0));T>=8;t[r+_e]=y&255,_e+=we,y/=256,T-=8);for(N=N<<T|y,Z+=T;Z>0;t[r+_e]=N&255,_e+=we,N/=256,Z-=8);t[r+_e-we]|=be*128};/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */(function(t){const l=base64Js,r=ieee754,E=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;t.Buffer=y,t.SlowBuffer=$e,t.INSPECT_MAX_BYTES=50;const T=**********;t.kMaxLength=T,y.TYPED_ARRAY_SUPPORT=A(),!y.TYPED_ARRAY_SUPPORT&&typeof console!="undefined"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function A(){try{const O=new Uint8Array(1),v={foo:function(){return 42}};return Object.setPrototypeOf(v,Uint8Array.prototype),Object.setPrototypeOf(O,v),O.foo()===42}catch(O){return!1}}Object.defineProperty(y.prototype,"parent",{enumerable:!0,get:function(){if(y.isBuffer(this))return this.buffer}}),Object.defineProperty(y.prototype,"offset",{enumerable:!0,get:function(){if(y.isBuffer(this))return this.byteOffset}});function N(O){if(O>T)throw new RangeError('The value "'+O+'" is invalid for option "size"');const v=new Uint8Array(O);return Object.setPrototypeOf(v,y.prototype),v}function y(O,v,I){if(typeof O=="number"){if(typeof v=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return Se(O)}return ue(O,v,I)}y.poolSize=8192;function ue(O,v,I){if(typeof O=="string")return ge(O,v);if(ArrayBuffer.isView(O))return we(O);if(O==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof O);if(rt(O,ArrayBuffer)||O&&rt(O.buffer,ArrayBuffer)||typeof SharedArrayBuffer!="undefined"&&(rt(O,SharedArrayBuffer)||O&&rt(O.buffer,SharedArrayBuffer)))return be(O,v,I);if(typeof O=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');const he=O.valueOf&&O.valueOf();if(he!=null&&he!==O)return y.from(he,v,I);const ve=Te(O);if(ve)return ve;if(typeof Symbol!="undefined"&&Symbol.toPrimitive!=null&&typeof O[Symbol.toPrimitive]=="function")return y.from(O[Symbol.toPrimitive]("string"),v,I);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof O)}y.from=function(O,v,I){return ue(O,v,I)},Object.setPrototypeOf(y.prototype,Uint8Array.prototype),Object.setPrototypeOf(y,Uint8Array);function Z(O){if(typeof O!="number")throw new TypeError('"size" argument must be of type number');if(O<0)throw new RangeError('The value "'+O+'" is invalid for option "size"')}function M(O,v,I){return Z(O),O<=0?N(O):v!==void 0?typeof I=="string"?N(O).fill(v,I):N(O).fill(v):N(O)}y.alloc=function(O,v,I){return M(O,v,I)};function Se(O){return Z(O),N(O<0?0:Ie(O)|0)}y.allocUnsafe=function(O){return Se(O)},y.allocUnsafeSlow=function(O){return Se(O)};function ge(O,v){if((typeof v!="string"||v==="")&&(v="utf8"),!y.isEncoding(v))throw new TypeError("Unknown encoding: "+v);const I=De(O,v)|0;let he=N(I);const ve=he.write(O,v);return ve!==I&&(he=he.slice(0,ve)),he}function _e(O){const v=O.length<0?0:Ie(O.length)|0,I=N(v);for(let he=0;he<v;he+=1)I[he]=O[he]&255;return I}function we(O){if(rt(O,Uint8Array)){const v=new Uint8Array(O);return be(v.buffer,v.byteOffset,v.byteLength)}return _e(O)}function be(O,v,I){if(v<0||O.byteLength<v)throw new RangeError('"offset" is outside of buffer bounds');if(O.byteLength<v+(I||0))throw new RangeError('"length" is outside of buffer bounds');let he;return v===void 0&&I===void 0?he=new Uint8Array(O):I===void 0?he=new Uint8Array(O,v):he=new Uint8Array(O,v,I),Object.setPrototypeOf(he,y.prototype),he}function Te(O){if(y.isBuffer(O)){const v=Ie(O.length)|0,I=N(v);return I.length===0||O.copy(I,0,0,v),I}if(O.length!==void 0)return typeof O.length!="number"||gt(O.length)?N(0):_e(O);if(O.type==="Buffer"&&Array.isArray(O.data))return _e(O.data)}function Ie(O){if(O>=T)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+T.toString(16)+" bytes");return O|0}function $e(O){return+O!=O&&(O=0),y.alloc(+O)}y.isBuffer=function(v){return v!=null&&v._isBuffer===!0&&v!==y.prototype},y.compare=function(v,I){if(rt(v,Uint8Array)&&(v=y.from(v,v.offset,v.byteLength)),rt(I,Uint8Array)&&(I=y.from(I,I.offset,I.byteLength)),!y.isBuffer(v)||!y.isBuffer(I))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(v===I)return 0;let he=v.length,ve=I.length;for(let xe=0,Ae=Math.min(he,ve);xe<Ae;++xe)if(v[xe]!==I[xe]){he=v[xe],ve=I[xe];break}return he<ve?-1:ve<he?1:0},y.isEncoding=function(v){switch(String(v).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},y.concat=function(v,I){if(!Array.isArray(v))throw new TypeError('"list" argument must be an Array of Buffers');if(v.length===0)return y.alloc(0);let he;if(I===void 0)for(I=0,he=0;he<v.length;++he)I+=v[he].length;const ve=y.allocUnsafe(I);let xe=0;for(he=0;he<v.length;++he){let Ae=v[he];if(rt(Ae,Uint8Array))xe+Ae.length>ve.length?(y.isBuffer(Ae)||(Ae=y.from(Ae)),Ae.copy(ve,xe)):Uint8Array.prototype.set.call(ve,Ae,xe);else if(y.isBuffer(Ae))Ae.copy(ve,xe);else throw new TypeError('"list" argument must be an Array of Buffers');xe+=Ae.length}return ve};function De(O,v){if(y.isBuffer(O))return O.length;if(ArrayBuffer.isView(O)||rt(O,ArrayBuffer))return O.byteLength;if(typeof O!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof O);const I=O.length,he=arguments.length>2&&arguments[2]===!0;if(!he&&I===0)return 0;let ve=!1;for(;;)switch(v){case"ascii":case"latin1":case"binary":return I;case"utf8":case"utf-8":return Ke(O).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return I*2;case"hex":return I>>>1;case"base64":return _t(O).length;default:if(ve)return he?-1:Ke(O).length;v=(""+v).toLowerCase(),ve=!0}}y.byteLength=De;function Ce(O,v,I){let he=!1;if((v===void 0||v<0)&&(v=0),v>this.length||((I===void 0||I>this.length)&&(I=this.length),I<=0)||(I>>>=0,v>>>=0,I<=v))return"";for(O||(O="utf8");;)switch(O){case"hex":return ft(this,v,I);case"utf8":case"utf-8":return Qe(this,v,I);case"ascii":return Me(this,v,I);case"latin1":case"binary":return nt(this,v,I);case"base64":return Ve(this,v,I);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ut(this,v,I);default:if(he)throw new TypeError("Unknown encoding: "+O);O=(O+"").toLowerCase(),he=!0}}y.prototype._isBuffer=!0;function ke(O,v,I){const he=O[v];O[v]=O[I],O[I]=he}y.prototype.swap16=function(){const v=this.length;if(v%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let I=0;I<v;I+=2)ke(this,I,I+1);return this},y.prototype.swap32=function(){const v=this.length;if(v%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let I=0;I<v;I+=4)ke(this,I,I+3),ke(this,I+1,I+2);return this},y.prototype.swap64=function(){const v=this.length;if(v%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let I=0;I<v;I+=8)ke(this,I,I+7),ke(this,I+1,I+6),ke(this,I+2,I+5),ke(this,I+3,I+4);return this},y.prototype.toString=function(){const v=this.length;return v===0?"":arguments.length===0?Qe(this,0,v):Ce.apply(this,arguments)},y.prototype.toLocaleString=y.prototype.toString,y.prototype.equals=function(v){if(!y.isBuffer(v))throw new TypeError("Argument must be a Buffer");return this===v?!0:y.compare(this,v)===0},y.prototype.inspect=function(){let v="";const I=t.INSPECT_MAX_BYTES;return v=this.toString("hex",0,I).replace(/(.{2})/g,"$1 ").trim(),this.length>I&&(v+=" ... "),"<Buffer "+v+">"},E&&(y.prototype[E]=y.prototype.inspect),y.prototype.compare=function(v,I,he,ve,xe){if(rt(v,Uint8Array)&&(v=y.from(v,v.offset,v.byteLength)),!y.isBuffer(v))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof v);if(I===void 0&&(I=0),he===void 0&&(he=v?v.length:0),ve===void 0&&(ve=0),xe===void 0&&(xe=this.length),I<0||he>v.length||ve<0||xe>this.length)throw new RangeError("out of range index");if(ve>=xe&&I>=he)return 0;if(ve>=xe)return-1;if(I>=he)return 1;if(I>>>=0,he>>>=0,ve>>>=0,xe>>>=0,this===v)return 0;let Ae=xe-ve,Ue=he-I;const Ye=Math.min(Ae,Ue),We=this.slice(ve,xe),Xe=v.slice(I,he);for(let ze=0;ze<Ye;++ze)if(We[ze]!==Xe[ze]){Ae=We[ze],Ue=Xe[ze];break}return Ae<Ue?-1:Ue<Ae?1:0};function Fe(O,v,I,he,ve){if(O.length===0)return-1;if(typeof I=="string"?(he=I,I=0):I>**********?I=**********:I<-2147483648&&(I=-2147483648),I=+I,gt(I)&&(I=ve?0:O.length-1),I<0&&(I=O.length+I),I>=O.length){if(ve)return-1;I=O.length-1}else if(I<0)if(ve)I=0;else return-1;if(typeof v=="string"&&(v=y.from(v,he)),y.isBuffer(v))return v.length===0?-1:Oe(O,v,I,he,ve);if(typeof v=="number")return v=v&255,typeof Uint8Array.prototype.indexOf=="function"?ve?Uint8Array.prototype.indexOf.call(O,v,I):Uint8Array.prototype.lastIndexOf.call(O,v,I):Oe(O,[v],I,he,ve);throw new TypeError("val must be string, number or Buffer")}function Oe(O,v,I,he,ve){let xe=1,Ae=O.length,Ue=v.length;if(he!==void 0&&(he=String(he).toLowerCase(),he==="ucs2"||he==="ucs-2"||he==="utf16le"||he==="utf-16le")){if(O.length<2||v.length<2)return-1;xe=2,Ae/=2,Ue/=2,I/=2}function Ye(Xe,ze){return xe===1?Xe[ze]:Xe.readUInt16BE(ze*xe)}let We;if(ve){let Xe=-1;for(We=I;We<Ae;We++)if(Ye(O,We)===Ye(v,Xe===-1?0:We-Xe)){if(Xe===-1&&(Xe=We),We-Xe+1===Ue)return Xe*xe}else Xe!==-1&&(We-=We-Xe),Xe=-1}else for(I+Ue>Ae&&(I=Ae-Ue),We=I;We>=0;We--){let Xe=!0;for(let ze=0;ze<Ue;ze++)if(Ye(O,We+ze)!==Ye(v,ze)){Xe=!1;break}if(Xe)return We}return-1}y.prototype.includes=function(v,I,he){return this.indexOf(v,I,he)!==-1},y.prototype.indexOf=function(v,I,he){return Fe(this,v,I,he,!0)},y.prototype.lastIndexOf=function(v,I,he){return Fe(this,v,I,he,!1)};function Be(O,v,I,he){I=Number(I)||0;const ve=O.length-I;he?(he=Number(he),he>ve&&(he=ve)):he=ve;const xe=v.length;he>xe/2&&(he=xe/2);let Ae;for(Ae=0;Ae<he;++Ae){const Ue=parseInt(v.substr(Ae*2,2),16);if(gt(Ue))return Ae;O[I+Ae]=Ue}return Ae}function Ee(O,v,I,he){return st(Ke(v,O.length-I),O,I,he)}function Pe(O,v,I,he){return st(it(v),O,I,he)}function Re(O,v,I,he){return st(_t(v),O,I,he)}function Le(O,v,I,he){return st(mt(v,O.length-I),O,I,he)}y.prototype.write=function(v,I,he,ve){if(I===void 0)ve="utf8",he=this.length,I=0;else if(he===void 0&&typeof I=="string")ve=I,he=this.length,I=0;else if(isFinite(I))I=I>>>0,isFinite(he)?(he=he>>>0,ve===void 0&&(ve="utf8")):(ve=he,he=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");const xe=this.length-I;if((he===void 0||he>xe)&&(he=xe),v.length>0&&(he<0||I<0)||I>this.length)throw new RangeError("Attempt to write outside buffer bounds");ve||(ve="utf8");let Ae=!1;for(;;)switch(ve){case"hex":return Be(this,v,I,he);case"utf8":case"utf-8":return Ee(this,v,I,he);case"ascii":case"latin1":case"binary":return Pe(this,v,I,he);case"base64":return Re(this,v,I,he);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Le(this,v,I,he);default:if(Ae)throw new TypeError("Unknown encoding: "+ve);ve=(""+ve).toLowerCase(),Ae=!0}},y.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function Ve(O,v,I){return v===0&&I===O.length?l.fromByteArray(O):l.fromByteArray(O.slice(v,I))}function Qe(O,v,I){I=Math.min(O.length,I);const he=[];let ve=v;for(;ve<I;){const xe=O[ve];let Ae=null,Ue=xe>239?4:xe>223?3:xe>191?2:1;if(ve+Ue<=I){let Ye,We,Xe,ze;switch(Ue){case 1:xe<128&&(Ae=xe);break;case 2:Ye=O[ve+1],(Ye&192)===128&&(ze=(xe&31)<<6|Ye&63,ze>127&&(Ae=ze));break;case 3:Ye=O[ve+1],We=O[ve+2],(Ye&192)===128&&(We&192)===128&&(ze=(xe&15)<<12|(Ye&63)<<6|We&63,ze>2047&&(ze<55296||ze>57343)&&(Ae=ze));break;case 4:Ye=O[ve+1],We=O[ve+2],Xe=O[ve+3],(Ye&192)===128&&(We&192)===128&&(Xe&192)===128&&(ze=(xe&15)<<18|(Ye&63)<<12|(We&63)<<6|Xe&63,ze>65535&&ze<1114112&&(Ae=ze))}}Ae===null?(Ae=65533,Ue=1):Ae>65535&&(Ae-=65536,he.push(Ae>>>10&1023|55296),Ae=56320|Ae&1023),he.push(Ae),ve+=Ue}return at(he)}const tt=4096;function at(O){const v=O.length;if(v<=tt)return String.fromCharCode.apply(String,O);let I="",he=0;for(;he<v;)I+=String.fromCharCode.apply(String,O.slice(he,he+=tt));return I}function Me(O,v,I){let he="";I=Math.min(O.length,I);for(let ve=v;ve<I;++ve)he+=String.fromCharCode(O[ve]&127);return he}function nt(O,v,I){let he="";I=Math.min(O.length,I);for(let ve=v;ve<I;++ve)he+=String.fromCharCode(O[ve]);return he}function ft(O,v,I){const he=O.length;(!v||v<0)&&(v=0),(!I||I<0||I>he)&&(I=he);let ve="";for(let xe=v;xe<I;++xe)ve+=Et[O[xe]];return ve}function ut(O,v,I){const he=O.slice(v,I);let ve="";for(let xe=0;xe<he.length-1;xe+=2)ve+=String.fromCharCode(he[xe]+he[xe+1]*256);return ve}y.prototype.slice=function(v,I){const he=this.length;v=~~v,I=I===void 0?he:~~I,v<0?(v+=he,v<0&&(v=0)):v>he&&(v=he),I<0?(I+=he,I<0&&(I=0)):I>he&&(I=he),I<v&&(I=v);const ve=this.subarray(v,I);return Object.setPrototypeOf(ve,y.prototype),ve};function He(O,v,I){if(O%1!==0||O<0)throw new RangeError("offset is not uint");if(O+v>I)throw new RangeError("Trying to access beyond buffer length")}y.prototype.readUintLE=y.prototype.readUIntLE=function(v,I,he){v=v>>>0,I=I>>>0,he||He(v,I,this.length);let ve=this[v],xe=1,Ae=0;for(;++Ae<I&&(xe*=256);)ve+=this[v+Ae]*xe;return ve},y.prototype.readUintBE=y.prototype.readUIntBE=function(v,I,he){v=v>>>0,I=I>>>0,he||He(v,I,this.length);let ve=this[v+--I],xe=1;for(;I>0&&(xe*=256);)ve+=this[v+--I]*xe;return ve},y.prototype.readUint8=y.prototype.readUInt8=function(v,I){return v=v>>>0,I||He(v,1,this.length),this[v]},y.prototype.readUint16LE=y.prototype.readUInt16LE=function(v,I){return v=v>>>0,I||He(v,2,this.length),this[v]|this[v+1]<<8},y.prototype.readUint16BE=y.prototype.readUInt16BE=function(v,I){return v=v>>>0,I||He(v,2,this.length),this[v]<<8|this[v+1]},y.prototype.readUint32LE=y.prototype.readUInt32LE=function(v,I){return v=v>>>0,I||He(v,4,this.length),(this[v]|this[v+1]<<8|this[v+2]<<16)+this[v+3]*16777216},y.prototype.readUint32BE=y.prototype.readUInt32BE=function(v,I){return v=v>>>0,I||He(v,4,this.length),this[v]*16777216+(this[v+1]<<16|this[v+2]<<8|this[v+3])},y.prototype.readBigUInt64LE=lt(function(v){v=v>>>0,je(v,"offset");const I=this[v],he=this[v+7];(I===void 0||he===void 0)&&Je(v,this.length-8);const ve=I+this[++v]*2**8+this[++v]*2**16+this[++v]*2**24,xe=this[++v]+this[++v]*2**8+this[++v]*2**16+he*2**24;return BigInt(ve)+(BigInt(xe)<<BigInt(32))}),y.prototype.readBigUInt64BE=lt(function(v){v=v>>>0,je(v,"offset");const I=this[v],he=this[v+7];(I===void 0||he===void 0)&&Je(v,this.length-8);const ve=I*2**24+this[++v]*2**16+this[++v]*2**8+this[++v],xe=this[++v]*2**24+this[++v]*2**16+this[++v]*2**8+he;return(BigInt(ve)<<BigInt(32))+BigInt(xe)}),y.prototype.readIntLE=function(v,I,he){v=v>>>0,I=I>>>0,he||He(v,I,this.length);let ve=this[v],xe=1,Ae=0;for(;++Ae<I&&(xe*=256);)ve+=this[v+Ae]*xe;return xe*=128,ve>=xe&&(ve-=Math.pow(2,8*I)),ve},y.prototype.readIntBE=function(v,I,he){v=v>>>0,I=I>>>0,he||He(v,I,this.length);let ve=I,xe=1,Ae=this[v+--ve];for(;ve>0&&(xe*=256);)Ae+=this[v+--ve]*xe;return xe*=128,Ae>=xe&&(Ae-=Math.pow(2,8*I)),Ae},y.prototype.readInt8=function(v,I){return v=v>>>0,I||He(v,1,this.length),this[v]&128?(255-this[v]+1)*-1:this[v]},y.prototype.readInt16LE=function(v,I){v=v>>>0,I||He(v,2,this.length);const he=this[v]|this[v+1]<<8;return he&32768?he|4294901760:he},y.prototype.readInt16BE=function(v,I){v=v>>>0,I||He(v,2,this.length);const he=this[v+1]|this[v]<<8;return he&32768?he|4294901760:he},y.prototype.readInt32LE=function(v,I){return v=v>>>0,I||He(v,4,this.length),this[v]|this[v+1]<<8|this[v+2]<<16|this[v+3]<<24},y.prototype.readInt32BE=function(v,I){return v=v>>>0,I||He(v,4,this.length),this[v]<<24|this[v+1]<<16|this[v+2]<<8|this[v+3]},y.prototype.readBigInt64LE=lt(function(v){v=v>>>0,je(v,"offset");const I=this[v],he=this[v+7];(I===void 0||he===void 0)&&Je(v,this.length-8);const ve=this[v+4]+this[v+5]*2**8+this[v+6]*2**16+(he<<24);return(BigInt(ve)<<BigInt(32))+BigInt(I+this[++v]*2**8+this[++v]*2**16+this[++v]*2**24)}),y.prototype.readBigInt64BE=lt(function(v){v=v>>>0,je(v,"offset");const I=this[v],he=this[v+7];(I===void 0||he===void 0)&&Je(v,this.length-8);const ve=(I<<24)+this[++v]*2**16+this[++v]*2**8+this[++v];return(BigInt(ve)<<BigInt(32))+BigInt(this[++v]*2**24+this[++v]*2**16+this[++v]*2**8+he)}),y.prototype.readFloatLE=function(v,I){return v=v>>>0,I||He(v,4,this.length),r.read(this,v,!0,23,4)},y.prototype.readFloatBE=function(v,I){return v=v>>>0,I||He(v,4,this.length),r.read(this,v,!1,23,4)},y.prototype.readDoubleLE=function(v,I){return v=v>>>0,I||He(v,8,this.length),r.read(this,v,!0,52,8)},y.prototype.readDoubleBE=function(v,I){return v=v>>>0,I||He(v,8,this.length),r.read(this,v,!1,52,8)};function qe(O,v,I,he,ve,xe){if(!y.isBuffer(O))throw new TypeError('"buffer" argument must be a Buffer instance');if(v>ve||v<xe)throw new RangeError('"value" argument is out of bounds');if(I+he>O.length)throw new RangeError("Index out of range")}y.prototype.writeUintLE=y.prototype.writeUIntLE=function(v,I,he,ve){if(v=+v,I=I>>>0,he=he>>>0,!ve){const Ue=Math.pow(2,8*he)-1;qe(this,v,I,he,Ue,0)}let xe=1,Ae=0;for(this[I]=v&255;++Ae<he&&(xe*=256);)this[I+Ae]=v/xe&255;return I+he},y.prototype.writeUintBE=y.prototype.writeUIntBE=function(v,I,he,ve){if(v=+v,I=I>>>0,he=he>>>0,!ve){const Ue=Math.pow(2,8*he)-1;qe(this,v,I,he,Ue,0)}let xe=he-1,Ae=1;for(this[I+xe]=v&255;--xe>=0&&(Ae*=256);)this[I+xe]=v/Ae&255;return I+he},y.prototype.writeUint8=y.prototype.writeUInt8=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,1,255,0),this[I]=v&255,I+1},y.prototype.writeUint16LE=y.prototype.writeUInt16LE=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,2,65535,0),this[I]=v&255,this[I+1]=v>>>8,I+2},y.prototype.writeUint16BE=y.prototype.writeUInt16BE=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,2,65535,0),this[I]=v>>>8,this[I+1]=v&255,I+2},y.prototype.writeUint32LE=y.prototype.writeUInt32LE=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,4,4294967295,0),this[I+3]=v>>>24,this[I+2]=v>>>16,this[I+1]=v>>>8,this[I]=v&255,I+4},y.prototype.writeUint32BE=y.prototype.writeUInt32BE=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,4,4294967295,0),this[I]=v>>>24,this[I+1]=v>>>16,this[I+2]=v>>>8,this[I+3]=v&255,I+4};function ct(O,v,I,he,ve){Ge(v,he,ve,O,I,7);let xe=Number(v&BigInt(4294967295));O[I++]=xe,xe=xe>>8,O[I++]=xe,xe=xe>>8,O[I++]=xe,xe=xe>>8,O[I++]=xe;let Ae=Number(v>>BigInt(32)&BigInt(4294967295));return O[I++]=Ae,Ae=Ae>>8,O[I++]=Ae,Ae=Ae>>8,O[I++]=Ae,Ae=Ae>>8,O[I++]=Ae,I}function ht(O,v,I,he,ve){Ge(v,he,ve,O,I,7);let xe=Number(v&BigInt(4294967295));O[I+7]=xe,xe=xe>>8,O[I+6]=xe,xe=xe>>8,O[I+5]=xe,xe=xe>>8,O[I+4]=xe;let Ae=Number(v>>BigInt(32)&BigInt(4294967295));return O[I+3]=Ae,Ae=Ae>>8,O[I+2]=Ae,Ae=Ae>>8,O[I+1]=Ae,Ae=Ae>>8,O[I]=Ae,I+8}y.prototype.writeBigUInt64LE=lt(function(v,I=0){return ct(this,v,I,BigInt(0),BigInt("0xffffffffffffffff"))}),y.prototype.writeBigUInt64BE=lt(function(v,I=0){return ht(this,v,I,BigInt(0),BigInt("0xffffffffffffffff"))}),y.prototype.writeIntLE=function(v,I,he,ve){if(v=+v,I=I>>>0,!ve){const Ye=Math.pow(2,8*he-1);qe(this,v,I,he,Ye-1,-Ye)}let xe=0,Ae=1,Ue=0;for(this[I]=v&255;++xe<he&&(Ae*=256);)v<0&&Ue===0&&this[I+xe-1]!==0&&(Ue=1),this[I+xe]=(v/Ae>>0)-Ue&255;return I+he},y.prototype.writeIntBE=function(v,I,he,ve){if(v=+v,I=I>>>0,!ve){const Ye=Math.pow(2,8*he-1);qe(this,v,I,he,Ye-1,-Ye)}let xe=he-1,Ae=1,Ue=0;for(this[I+xe]=v&255;--xe>=0&&(Ae*=256);)v<0&&Ue===0&&this[I+xe+1]!==0&&(Ue=1),this[I+xe]=(v/Ae>>0)-Ue&255;return I+he},y.prototype.writeInt8=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,1,127,-128),v<0&&(v=255+v+1),this[I]=v&255,I+1},y.prototype.writeInt16LE=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,2,32767,-32768),this[I]=v&255,this[I+1]=v>>>8,I+2},y.prototype.writeInt16BE=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,2,32767,-32768),this[I]=v>>>8,this[I+1]=v&255,I+2},y.prototype.writeInt32LE=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,4,**********,-2147483648),this[I]=v&255,this[I+1]=v>>>8,this[I+2]=v>>>16,this[I+3]=v>>>24,I+4},y.prototype.writeInt32BE=function(v,I,he){return v=+v,I=I>>>0,he||qe(this,v,I,4,**********,-2147483648),v<0&&(v=4294967295+v+1),this[I]=v>>>24,this[I+1]=v>>>16,this[I+2]=v>>>8,this[I+3]=v&255,I+4},y.prototype.writeBigInt64LE=lt(function(v,I=0){return ct(this,v,I,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),y.prototype.writeBigInt64BE=lt(function(v,I=0){return ht(this,v,I,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function dt(O,v,I,he,ve,xe){if(I+he>O.length)throw new RangeError("Index out of range");if(I<0)throw new RangeError("Index out of range")}function pt(O,v,I,he,ve){return v=+v,I=I>>>0,ve||dt(O,v,I,4),r.write(O,v,I,he,23,4),I+4}y.prototype.writeFloatLE=function(v,I,he){return pt(this,v,I,!0,he)},y.prototype.writeFloatBE=function(v,I,he){return pt(this,v,I,!1,he)};function ce(O,v,I,he,ve){return v=+v,I=I>>>0,ve||dt(O,v,I,8),r.write(O,v,I,he,52,8),I+8}y.prototype.writeDoubleLE=function(v,I,he){return ce(this,v,I,!0,he)},y.prototype.writeDoubleBE=function(v,I,he){return ce(this,v,I,!1,he)},y.prototype.copy=function(v,I,he,ve){if(!y.isBuffer(v))throw new TypeError("argument should be a Buffer");if(he||(he=0),!ve&&ve!==0&&(ve=this.length),I>=v.length&&(I=v.length),I||(I=0),ve>0&&ve<he&&(ve=he),ve===he||v.length===0||this.length===0)return 0;if(I<0)throw new RangeError("targetStart out of bounds");if(he<0||he>=this.length)throw new RangeError("Index out of range");if(ve<0)throw new RangeError("sourceEnd out of bounds");ve>this.length&&(ve=this.length),v.length-I<ve-he&&(ve=v.length-I+he);const xe=ve-he;return this===v&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(I,he,ve):Uint8Array.prototype.set.call(v,this.subarray(he,ve),I),xe},y.prototype.fill=function(v,I,he,ve){if(typeof v=="string"){if(typeof I=="string"?(ve=I,I=0,he=this.length):typeof he=="string"&&(ve=he,he=this.length),ve!==void 0&&typeof ve!="string")throw new TypeError("encoding must be a string");if(typeof ve=="string"&&!y.isEncoding(ve))throw new TypeError("Unknown encoding: "+ve);if(v.length===1){const Ae=v.charCodeAt(0);(ve==="utf8"&&Ae<128||ve==="latin1")&&(v=Ae)}}else typeof v=="number"?v=v&255:typeof v=="boolean"&&(v=Number(v));if(I<0||this.length<I||this.length<he)throw new RangeError("Out of range index");if(he<=I)return this;I=I>>>0,he=he===void 0?this.length:he>>>0,v||(v=0);let xe;if(typeof v=="number")for(xe=I;xe<he;++xe)this[xe]=v;else{const Ae=y.isBuffer(v)?v:y.from(v,ve),Ue=Ae.length;if(Ue===0)throw new TypeError('The value "'+v+'" is invalid for argument "value"');for(xe=0;xe<he-I;++xe)this[xe+I]=Ae[xe%Ue]}return this};const k={};function Ne(O,v,I){k[O]=class extends I{constructor(){super(),Object.defineProperty(this,"message",{value:v.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${O}]`,this.stack,delete this.name}get code(){return O}set code(ve){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:ve,writable:!0})}toString(){return`${this.name} [${O}]: ${this.message}`}}}Ne("ERR_BUFFER_OUT_OF_BOUNDS",function(O){return O?`${O} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),Ne("ERR_INVALID_ARG_TYPE",function(O,v){return`The "${O}" argument must be of type number. Received type ${typeof v}`},TypeError),Ne("ERR_OUT_OF_RANGE",function(O,v,I){let he=`The value of "${O}" is out of range.`,ve=I;return Number.isInteger(I)&&Math.abs(I)>2**32?ve=ye(String(I)):typeof I=="bigint"&&(ve=String(I),(I>BigInt(2)**BigInt(32)||I<-(BigInt(2)**BigInt(32)))&&(ve=ye(ve)),ve+="n"),he+=` It must be ${v}. Received ${ve}`,he},RangeError);function ye(O){let v="",I=O.length;const he=O[0]==="-"?1:0;for(;I>=he+4;I-=3)v=`_${O.slice(I-3,I)}${v}`;return`${O.slice(0,I)}${v}`}function Ze(O,v,I){je(v,"offset"),(O[v]===void 0||O[v+I]===void 0)&&Je(v,O.length-(I+1))}function Ge(O,v,I,he,ve,xe){if(O>I||O<v){const Ae=typeof v=="bigint"?"n":"";let Ue;throw xe>3?v===0||v===BigInt(0)?Ue=`>= 0${Ae} and < 2${Ae} ** ${(xe+1)*8}${Ae}`:Ue=`>= -(2${Ae} ** ${(xe+1)*8-1}${Ae}) and < 2 ** ${(xe+1)*8-1}${Ae}`:Ue=`>= ${v}${Ae} and <= ${I}${Ae}`,new k.ERR_OUT_OF_RANGE("value",Ue,O)}Ze(he,ve,xe)}function je(O,v){if(typeof O!="number")throw new k.ERR_INVALID_ARG_TYPE(v,"number",O)}function Je(O,v,I){throw Math.floor(O)!==O?(je(O,I),new k.ERR_OUT_OF_RANGE(I||"offset","an integer",O)):v<0?new k.ERR_BUFFER_OUT_OF_BOUNDS:new k.ERR_OUT_OF_RANGE(I||"offset",`>= ${I?1:0} and <= ${v}`,O)}const et=/[^+/0-9A-Za-z-_]/g;function ot(O){if(O=O.split("=")[0],O=O.trim().replace(et,""),O.length<2)return"";for(;O.length%4!==0;)O=O+"=";return O}function Ke(O,v){v=v||1/0;let I;const he=O.length;let ve=null;const xe=[];for(let Ae=0;Ae<he;++Ae){if(I=O.charCodeAt(Ae),I>55295&&I<57344){if(!ve){if(I>56319){(v-=3)>-1&&xe.push(239,191,189);continue}else if(Ae+1===he){(v-=3)>-1&&xe.push(239,191,189);continue}ve=I;continue}if(I<56320){(v-=3)>-1&&xe.push(239,191,189),ve=I;continue}I=(ve-55296<<10|I-56320)+65536}else ve&&(v-=3)>-1&&xe.push(239,191,189);if(ve=null,I<128){if((v-=1)<0)break;xe.push(I)}else if(I<2048){if((v-=2)<0)break;xe.push(I>>6|192,I&63|128)}else if(I<65536){if((v-=3)<0)break;xe.push(I>>12|224,I>>6&63|128,I&63|128)}else if(I<1114112){if((v-=4)<0)break;xe.push(I>>18|240,I>>12&63|128,I>>6&63|128,I&63|128)}else throw new Error("Invalid code point")}return xe}function it(O){const v=[];for(let I=0;I<O.length;++I)v.push(O.charCodeAt(I)&255);return v}function mt(O,v){let I,he,ve;const xe=[];for(let Ae=0;Ae<O.length&&!((v-=2)<0);++Ae)I=O.charCodeAt(Ae),he=I>>8,ve=I%256,xe.push(ve),xe.push(he);return xe}function _t(O){return l.toByteArray(ot(O))}function st(O,v,I,he){let ve;for(ve=0;ve<he&&!(ve+I>=v.length||ve>=O.length);++ve)v[ve+I]=O[ve];return ve}function rt(O,v){return O instanceof v||O!=null&&O.constructor!=null&&O.constructor.name!=null&&O.constructor.name===v.name}function gt(O){return O!==O}const Et=function(){const O="0123456789abcdef",v=new Array(256);for(let I=0;I<16;++I){const he=I*16;for(let ve=0;ve<16;++ve)v[he+ve]=O[I]+O[ve]}return v}();function lt(O){return typeof BigInt=="undefined"?vt:O}function vt(){throw new Error("BigInt not supported")}})(buffer$1),globalThis.Buffer=buffer$1.Buffer;var utf8$5={},utils$r={},support$5={},nodestream,blob;if(support$5.base64=!0,support$5.array=!0,support$5.string=!0,support$5.arraybuffer=typeof ArrayBuffer!="undefined"&&typeof Uint8Array!="undefined",support$5.nodebuffer=typeof Buffer!="undefined",support$5.uint8array=typeof Uint8Array!="undefined",typeof ArrayBuffer=="undefined")blob=support$5.blob=!1;else{var buffer=new ArrayBuffer(0);try{blob=support$5.blob=new Blob([buffer],{type:"application/zip"}).size===0}catch(t){try{var Builder=self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder,builder=new Builder;builder.append(buffer),blob=support$5.blob=builder.getBlob("application/zip").size===0}catch(l){blob=support$5.blob=!1}}}try{nodestream=support$5.nodestream=!!require("readable-stream").Readable}catch(t){nodestream=support$5.nodestream=!1}var base64$1={},utils$q=utils$r,support$4=support$5,_keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";base64$1.encode=function(t){for(var l=[],r,E,T,A,N,y,ue,Z=0,M=t.length,Se=M,ge=utils$q.getTypeOf(t)!=="string";Z<t.length;)Se=M-Z,ge?(r=t[Z++],E=Z<M?t[Z++]:0,T=Z<M?t[Z++]:0):(r=t.charCodeAt(Z++),E=Z<M?t.charCodeAt(Z++):0,T=Z<M?t.charCodeAt(Z++):0),A=r>>2,N=(r&3)<<4|E>>4,y=Se>1?(E&15)<<2|T>>6:64,ue=Se>2?T&63:64,l.push(_keyStr.charAt(A)+_keyStr.charAt(N)+_keyStr.charAt(y)+_keyStr.charAt(ue));return l.join("")},base64$1.decode=function(t){var l,r,E,T,A,N,y,ue=0,Z=0,M="data:";if(t.substr(0,M.length)===M)throw new Error("Invalid base64 input, it looks like a data url.");t=t.replace(/[^A-Za-z0-9+/=]/g,"");var Se=t.length*3/4;if(t.charAt(t.length-1)===_keyStr.charAt(64)&&Se--,t.charAt(t.length-2)===_keyStr.charAt(64)&&Se--,Se%1!==0)throw new Error("Invalid base64 input, bad content length.");var ge;for(support$4.uint8array?ge=new Uint8Array(Se|0):ge=new Array(Se|0);ue<t.length;)T=_keyStr.indexOf(t.charAt(ue++)),A=_keyStr.indexOf(t.charAt(ue++)),N=_keyStr.indexOf(t.charAt(ue++)),y=_keyStr.indexOf(t.charAt(ue++)),l=T<<2|A>>4,r=(A&15)<<4|N>>2,E=(N&3)<<6|y,ge[Z++]=l,N!==64&&(ge[Z++]=r),y!==64&&(ge[Z++]=E);return ge};var nodejsUtils$2={isNode:typeof Buffer!="undefined",newBufferFrom:function(t,l){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(t,l);if(typeof t=="number")throw new Error('The "data" argument must not be a number');return new Buffer(t,l)},allocBuffer:function(t){if(Buffer.alloc)return Buffer.alloc(t);var l=new Buffer(t);return l.fill(0),l},isBuffer:function(t){return Buffer.isBuffer(t)},isStream:function(t){return t&&typeof t.on=="function"&&typeof t.pause=="function"&&typeof t.resume=="function"}},Mutation=commonjsGlobal.MutationObserver||commonjsGlobal.WebKitMutationObserver,scheduleDrain;if(Mutation){var called=0,observer=new Mutation(nextTick),element=commonjsGlobal.document.createTextNode("");observer.observe(element,{characterData:!0}),scheduleDrain=function(){element.data=called=++called%2}}else if(!commonjsGlobal.setImmediate&&typeof commonjsGlobal.MessageChannel!="undefined"){var channel=new commonjsGlobal.MessageChannel;channel.port1.onmessage=nextTick,scheduleDrain=function(){channel.port2.postMessage(0)}}else"document"in commonjsGlobal&&"onreadystatechange"in commonjsGlobal.document.createElement("script")?scheduleDrain=function(){var t=commonjsGlobal.document.createElement("script");t.onreadystatechange=function(){nextTick(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},commonjsGlobal.document.documentElement.appendChild(t)}:scheduleDrain=function(){setTimeout(nextTick,0)};var draining,queue=[];function nextTick(){draining=!0;for(var t,l,r=queue.length;r;){for(l=queue,queue=[],t=-1;++t<r;)l[t]();r=queue.length}draining=!1}var lib$2=immediate$1;function immediate$1(t){queue.push(t)===1&&!draining&&scheduleDrain()}var immediate=lib$2;function INTERNAL(){}var handlers={},REJECTED=["REJECTED"],FULFILLED=["FULFILLED"],PENDING=["PENDING"],lib$1=Promise$1;function Promise$1(t){if(typeof t!="function")throw new TypeError("resolver must be a function");this.state=PENDING,this.queue=[],this.outcome=void 0,t!==INTERNAL&&safelyResolveThenable(this,t)}Promise$1.prototype.finally=function(t){if(typeof t!="function")return this;var l=this.constructor;return this.then(r,E);function r(T){function A(){return T}return l.resolve(t()).then(A)}function E(T){function A(){throw T}return l.resolve(t()).then(A)}},Promise$1.prototype.catch=function(t){return this.then(null,t)},Promise$1.prototype.then=function(t,l){if(typeof t!="function"&&this.state===FULFILLED||typeof l!="function"&&this.state===REJECTED)return this;var r=new this.constructor(INTERNAL);if(this.state!==PENDING){var E=this.state===FULFILLED?t:l;unwrap(r,E,this.outcome)}else this.queue.push(new QueueItem(r,t,l));return r};function QueueItem(t,l,r){this.promise=t,typeof l=="function"&&(this.onFulfilled=l,this.callFulfilled=this.otherCallFulfilled),typeof r=="function"&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}QueueItem.prototype.callFulfilled=function(t){handlers.resolve(this.promise,t)},QueueItem.prototype.otherCallFulfilled=function(t){unwrap(this.promise,this.onFulfilled,t)},QueueItem.prototype.callRejected=function(t){handlers.reject(this.promise,t)},QueueItem.prototype.otherCallRejected=function(t){unwrap(this.promise,this.onRejected,t)};function unwrap(t,l,r){immediate(function(){var E;try{E=l(r)}catch(T){return handlers.reject(t,T)}E===t?handlers.reject(t,new TypeError("Cannot resolve promise with itself")):handlers.resolve(t,E)})}handlers.resolve=function(t,l){var r=tryCatch(getThen,l);if(r.status==="error")return handlers.reject(t,r.value);var E=r.value;if(E)safelyResolveThenable(t,E);else{t.state=FULFILLED,t.outcome=l;for(var T=-1,A=t.queue.length;++T<A;)t.queue[T].callFulfilled(l)}return t},handlers.reject=function(t,l){t.state=REJECTED,t.outcome=l;for(var r=-1,E=t.queue.length;++r<E;)t.queue[r].callRejected(l);return t};function getThen(t){var l=t&&t.then;if(t&&(typeof t=="object"||typeof t=="function")&&typeof l=="function")return function(){l.apply(t,arguments)}}function safelyResolveThenable(t,l){var r=!1;function E(y){r||(r=!0,handlers.reject(t,y))}function T(y){r||(r=!0,handlers.resolve(t,y))}function A(){l(T,E)}var N=tryCatch(A);N.status==="error"&&E(N.value)}function tryCatch(t,l){var r={};try{r.value=t(l),r.status="success"}catch(E){r.status="error",r.value=E}return r}Promise$1.resolve=resolve;function resolve(t){return t instanceof this?t:handlers.resolve(new this(INTERNAL),t)}Promise$1.reject=reject;function reject(t){var l=new this(INTERNAL);return handlers.reject(l,t)}Promise$1.all=all;function all(t){var l=this;if(Object.prototype.toString.call(t)!=="[object Array]")return this.reject(new TypeError("must be an array"));var r=t.length,E=!1;if(!r)return this.resolve([]);for(var T=new Array(r),A=0,N=-1,y=new this(INTERNAL);++N<r;)ue(t[N],N);return y;function ue(Z,M){l.resolve(Z).then(Se,function(ge){E||(E=!0,handlers.reject(y,ge))});function Se(ge){T[M]=ge,++A===r&&!E&&(E=!0,handlers.resolve(y,T))}}}Promise$1.race=race;function race(t){var l=this;if(Object.prototype.toString.call(t)!=="[object Array]")return this.reject(new TypeError("must be an array"));var r=t.length,E=!1;if(!r)return this.resolve([]);for(var T=-1,A=new this(INTERNAL);++T<r;)N(t[T]);return A;function N(y){l.resolve(y).then(function(ue){E||(E=!0,handlers.resolve(A,ue))},function(ue){E||(E=!0,handlers.reject(A,ue))})}}var ES6Promise=null;typeof Promise!="undefined"?ES6Promise=Promise:ES6Promise=lib$1;var external$3={Promise:ES6Promise};(function(t,l){if(t.setImmediate)return;var r=1,E={},T=!1,A=t.document,N;function y($e){typeof $e!="function"&&($e=new Function(""+$e));for(var De=new Array(arguments.length-1),Ce=0;Ce<De.length;Ce++)De[Ce]=arguments[Ce+1];var ke={callback:$e,args:De};return E[r]=ke,N(r),r++}function ue($e){delete E[$e]}function Z($e){var De=$e.callback,Ce=$e.args;switch(Ce.length){case 0:De();break;case 1:De(Ce[0]);break;case 2:De(Ce[0],Ce[1]);break;case 3:De(Ce[0],Ce[1],Ce[2]);break;default:De.apply(l,Ce);break}}function M($e){if(T)setTimeout(M,0,$e);else{var De=E[$e];if(De){T=!0;try{Z(De)}finally{ue($e),T=!1}}}}function Se(){N=function($e){setTimeout(function(){M($e)})}}function ge(){if(t.postMessage&&!t.importScripts){var $e=!0,De=t.onmessage;return t.onmessage=function(){$e=!1},t.postMessage("","*"),t.onmessage=De,$e}}function _e(){var $e="setImmediate$"+Math.random()+"$",De=function(Ce){Ce.source===t&&typeof Ce.data=="string"&&Ce.data.indexOf($e)===0&&M(+Ce.data.slice($e.length))};t.addEventListener?t.addEventListener("message",De,!1):t.attachEvent("onmessage",De),N=function(Ce){t.postMessage($e+Ce,"*")}}function we(){var $e=new MessageChannel;$e.port1.onmessage=function(De){var Ce=De.data;M(Ce)},N=function(De){$e.port2.postMessage(De)}}function be(){var $e=A.documentElement;N=function(De){var Ce=A.createElement("script");Ce.onreadystatechange=function(){M(De),Ce.onreadystatechange=null,$e.removeChild(Ce),Ce=null},$e.appendChild(Ce)}}function Te(){N=function($e){setTimeout(M,0,$e)}}var Ie=Object.getPrototypeOf&&Object.getPrototypeOf(t);Ie=Ie&&Ie.setTimeout?Ie:t,{}.toString.call(t.undefined)==="[object undefined]"?Se():ge()?_e():t.MessageChannel?we():A&&"onreadystatechange"in A.createElement("script")?be():Te(),Ie.setImmediate=y,Ie.clearImmediate=ue})(typeof self=="undefined"?commonjsGlobal:self),function(t){var l=support$5,r=base64$1,E=nodejsUtils$2,T=external$3;function A(ge){var _e=null;return l.uint8array?_e=new Uint8Array(ge.length):_e=new Array(ge.length),y(ge,_e)}t.newBlob=function(ge,_e){t.checkSupport("blob");try{return new Blob([ge],{type:_e})}catch(Te){try{var we=self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder,be=new we;return be.append(ge),be.getBlob(_e)}catch(Ie){throw new Error("Bug : can't construct the Blob.")}}};function N(ge){return ge}function y(ge,_e){for(var we=0;we<ge.length;++we)_e[we]=ge.charCodeAt(we)&255;return _e}var ue={stringifyByChunk:function(ge,_e,we){var be=[],Te=0,Ie=ge.length;if(Ie<=we)return String.fromCharCode.apply(null,ge);for(;Te<Ie;)_e==="array"||_e==="nodebuffer"?be.push(String.fromCharCode.apply(null,ge.slice(Te,Math.min(Te+we,Ie)))):be.push(String.fromCharCode.apply(null,ge.subarray(Te,Math.min(Te+we,Ie)))),Te+=we;return be.join("")},stringifyByChar:function(ge){for(var _e="",we=0;we<ge.length;we++)_e+=String.fromCharCode(ge[we]);return _e},applyCanBeUsed:{uint8array:function(){try{return l.uint8array&&String.fromCharCode.apply(null,new Uint8Array(1)).length===1}catch(ge){return!1}}(),nodebuffer:function(){try{return l.nodebuffer&&String.fromCharCode.apply(null,E.allocBuffer(1)).length===1}catch(ge){return!1}}()}};function Z(ge){var _e=65536,we=t.getTypeOf(ge),be=!0;if(we==="uint8array"?be=ue.applyCanBeUsed.uint8array:we==="nodebuffer"&&(be=ue.applyCanBeUsed.nodebuffer),be)for(;_e>1;)try{return ue.stringifyByChunk(ge,we,_e)}catch(Te){_e=Math.floor(_e/2)}return ue.stringifyByChar(ge)}t.applyFromCharCode=Z;function M(ge,_e){for(var we=0;we<ge.length;we++)_e[we]=ge[we];return _e}var Se={};Se.string={string:N,array:function(ge){return y(ge,new Array(ge.length))},arraybuffer:function(ge){return Se.string.uint8array(ge).buffer},uint8array:function(ge){return y(ge,new Uint8Array(ge.length))},nodebuffer:function(ge){return y(ge,E.allocBuffer(ge.length))}},Se.array={string:Z,array:N,arraybuffer:function(ge){return new Uint8Array(ge).buffer},uint8array:function(ge){return new Uint8Array(ge)},nodebuffer:function(ge){return E.newBufferFrom(ge)}},Se.arraybuffer={string:function(ge){return Z(new Uint8Array(ge))},array:function(ge){return M(new Uint8Array(ge),new Array(ge.byteLength))},arraybuffer:N,uint8array:function(ge){return new Uint8Array(ge)},nodebuffer:function(ge){return E.newBufferFrom(new Uint8Array(ge))}},Se.uint8array={string:Z,array:function(ge){return M(ge,new Array(ge.length))},arraybuffer:function(ge){return ge.buffer},uint8array:N,nodebuffer:function(ge){return E.newBufferFrom(ge)}},Se.nodebuffer={string:Z,array:function(ge){return M(ge,new Array(ge.length))},arraybuffer:function(ge){return Se.nodebuffer.uint8array(ge).buffer},uint8array:function(ge){return M(ge,new Uint8Array(ge.length))},nodebuffer:N},t.transformTo=function(ge,_e){if(_e||(_e=""),!ge)return _e;t.checkSupport(ge);var we=t.getTypeOf(_e),be=Se[we][ge](_e);return be},t.resolve=function(ge){for(var _e=ge.split("/"),we=[],be=0;be<_e.length;be++){var Te=_e[be];Te==="."||Te===""&&be!==0&&be!==_e.length-1||(Te===".."?we.pop():we.push(Te))}return we.join("/")},t.getTypeOf=function(ge){if(typeof ge=="string")return"string";if(Object.prototype.toString.call(ge)==="[object Array]")return"array";if(l.nodebuffer&&E.isBuffer(ge))return"nodebuffer";if(l.uint8array&&ge instanceof Uint8Array)return"uint8array";if(l.arraybuffer&&ge instanceof ArrayBuffer)return"arraybuffer"},t.checkSupport=function(ge){var _e=l[ge.toLowerCase()];if(!_e)throw new Error(ge+" is not supported by this platform")},t.MAX_VALUE_16BITS=65535,t.MAX_VALUE_32BITS=-1,t.pretty=function(ge){var _e="",we,be;for(be=0;be<(ge||"").length;be++)we=ge.charCodeAt(be),_e+="\\x"+(we<16?"0":"")+we.toString(16).toUpperCase();return _e},t.delay=function(ge,_e,we){setImmediate(function(){ge.apply(we||null,_e||[])})},t.inherits=function(ge,_e){var we=function(){};we.prototype=_e.prototype,ge.prototype=new we},t.extend=function(){var ge={},_e,we;for(_e=0;_e<arguments.length;_e++)for(we in arguments[_e])Object.prototype.hasOwnProperty.call(arguments[_e],we)&&typeof ge[we]=="undefined"&&(ge[we]=arguments[_e][we]);return ge},t.prepareContent=function(ge,_e,we,be,Te){var Ie=T.Promise.resolve(_e).then(function($e){var De=l.blob&&($e instanceof Blob||["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call($e))!==-1);return De&&typeof FileReader!="undefined"?new T.Promise(function(Ce,ke){var Fe=new FileReader;Fe.onload=function(Oe){Ce(Oe.target.result)},Fe.onerror=function(Oe){ke(Oe.target.error)},Fe.readAsArrayBuffer($e)}):$e});return Ie.then(function($e){var De=t.getTypeOf($e);return De?(De==="arraybuffer"?$e=t.transformTo("uint8array",$e):De==="string"&&(Te?$e=r.decode($e):we&&be!==!0&&($e=A($e))),$e):T.Promise.reject(new Error("Can't read the data of '"+ge+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}}(utils$r);function GenericWorker$b(t){this.name=t||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}GenericWorker$b.prototype={push:function(t){this.emit("data",t)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(t){this.emit("error",t)}return!0},error:function(t){return this.isFinished?!1:(this.isPaused?this.generatedError=t:(this.isFinished=!0,this.emit("error",t),this.previous&&this.previous.error(t),this.cleanUp()),!0)},on:function(t,l){return this._listeners[t].push(l),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(t,l){if(this._listeners[t])for(var r=0;r<this._listeners[t].length;r++)this._listeners[t][r].call(this,l)},pipe:function(t){return t.registerPrevious(this)},registerPrevious:function(t){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=t.streamInfo,this.mergeStreamInfo(),this.previous=t;var l=this;return t.on("data",function(r){l.processChunk(r)}),t.on("end",function(){l.end()}),t.on("error",function(r){l.error(r)}),this},pause:function(){return this.isPaused||this.isFinished?!1:(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;this.isPaused=!1;var t=!1;return this.generatedError&&(this.error(this.generatedError),t=!0),this.previous&&this.previous.resume(),!t},flush:function(){},processChunk:function(t){this.push(t)},withStreamInfo:function(t,l){return this.extraStreamInfo[t]=l,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var t in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,t)&&(this.streamInfo[t]=this.extraStreamInfo[t])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var t="Worker "+this.name;return this.previous?this.previous+" -> "+t:t}};var GenericWorker_1=GenericWorker$b;(function(t){for(var l=utils$r,r=support$5,E=nodejsUtils$2,T=GenericWorker_1,A=new Array(256),N=0;N<256;N++)A[N]=N>=252?6:N>=248?5:N>=240?4:N>=224?3:N>=192?2:1;A[254]=A[254]=1;var y=function(ge){var _e,we,be,Te,Ie,$e=ge.length,De=0;for(Te=0;Te<$e;Te++)we=ge.charCodeAt(Te),(we&64512)===55296&&Te+1<$e&&(be=ge.charCodeAt(Te+1),(be&64512)===56320&&(we=65536+(we-55296<<10)+(be-56320),Te++)),De+=we<128?1:we<2048?2:we<65536?3:4;for(r.uint8array?_e=new Uint8Array(De):_e=new Array(De),Ie=0,Te=0;Ie<De;Te++)we=ge.charCodeAt(Te),(we&64512)===55296&&Te+1<$e&&(be=ge.charCodeAt(Te+1),(be&64512)===56320&&(we=65536+(we-55296<<10)+(be-56320),Te++)),we<128?_e[Ie++]=we:we<2048?(_e[Ie++]=192|we>>>6,_e[Ie++]=128|we&63):we<65536?(_e[Ie++]=224|we>>>12,_e[Ie++]=128|we>>>6&63,_e[Ie++]=128|we&63):(_e[Ie++]=240|we>>>18,_e[Ie++]=128|we>>>12&63,_e[Ie++]=128|we>>>6&63,_e[Ie++]=128|we&63);return _e},ue=function(ge,_e){var we;for(_e=_e||ge.length,_e>ge.length&&(_e=ge.length),we=_e-1;we>=0&&(ge[we]&192)===128;)we--;return we<0||we===0?_e:we+A[ge[we]]>_e?we:_e},Z=function(ge){var _e,we,be,Te,Ie=ge.length,$e=new Array(Ie*2);for(we=0,_e=0;_e<Ie;){if(be=ge[_e++],be<128){$e[we++]=be;continue}if(Te=A[be],Te>4){$e[we++]=65533,_e+=Te-1;continue}for(be&=Te===2?31:Te===3?15:7;Te>1&&_e<Ie;)be=be<<6|ge[_e++]&63,Te--;if(Te>1){$e[we++]=65533;continue}be<65536?$e[we++]=be:(be-=65536,$e[we++]=55296|be>>10&1023,$e[we++]=56320|be&1023)}return $e.length!==we&&($e.subarray?$e=$e.subarray(0,we):$e.length=we),l.applyFromCharCode($e)};t.utf8encode=function(_e){return r.nodebuffer?E.newBufferFrom(_e,"utf-8"):y(_e)},t.utf8decode=function(_e){return r.nodebuffer?l.transformTo("nodebuffer",_e).toString("utf-8"):(_e=l.transformTo(r.uint8array?"uint8array":"array",_e),Z(_e))};function M(){T.call(this,"utf-8 decode"),this.leftOver=null}l.inherits(M,T),M.prototype.processChunk=function(ge){var _e=l.transformTo(r.uint8array?"uint8array":"array",ge.data);if(this.leftOver&&this.leftOver.length){if(r.uint8array){var we=_e;_e=new Uint8Array(we.length+this.leftOver.length),_e.set(this.leftOver,0),_e.set(we,this.leftOver.length)}else _e=this.leftOver.concat(_e);this.leftOver=null}var be=ue(_e),Te=_e;be!==_e.length&&(r.uint8array?(Te=_e.subarray(0,be),this.leftOver=_e.subarray(be,_e.length)):(Te=_e.slice(0,be),this.leftOver=_e.slice(be,_e.length))),this.push({data:t.utf8decode(Te),meta:ge.meta})},M.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:t.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},t.Utf8DecodeWorker=M;function Se(){T.call(this,"utf-8 encode")}l.inherits(Se,T),Se.prototype.processChunk=function(ge){this.push({data:t.utf8encode(ge.data),meta:ge.meta})},t.Utf8EncodeWorker=Se})(utf8$5);var GenericWorker$a=GenericWorker_1,utils$p=utils$r;function ConvertWorker$1(t){GenericWorker$a.call(this,"ConvertWorker to "+t),this.destType=t}utils$p.inherits(ConvertWorker$1,GenericWorker$a),ConvertWorker$1.prototype.processChunk=function(t){this.push({data:utils$p.transformTo(this.destType,t.data),meta:t.meta})};var ConvertWorker_1=ConvertWorker$1,utils$o=utils$r,ConvertWorker=ConvertWorker_1,GenericWorker$9=GenericWorker_1,base64=base64$1,support$3=support$5,external$2=external$3,NodejsStreamOutputAdapter=null;if(support$3.nodestream)try{NodejsStreamOutputAdapter=require("../nodejs/NodejsStreamOutputAdapter")}catch(t){}function transformZipOutput(t,l,r){switch(t){case"blob":return utils$o.newBlob(utils$o.transformTo("arraybuffer",l),r);case"base64":return base64.encode(l);default:return utils$o.transformTo(t,l)}}function concat(t,l){var r,E=0,T=null,A=0;for(r=0;r<l.length;r++)A+=l[r].length;switch(t){case"string":return l.join("");case"array":return Array.prototype.concat.apply([],l);case"uint8array":for(T=new Uint8Array(A),r=0;r<l.length;r++)T.set(l[r],E),E+=l[r].length;return T;case"nodebuffer":return Buffer.concat(l);default:throw new Error("concat : unsupported type '"+t+"'")}}function accumulate(t,l){return new external$2.Promise(function(r,E){var T=[],A=t._internalType,N=t._outputType,y=t._mimeType;t.on("data",function(ue,Z){T.push(ue),l&&l(Z)}).on("error",function(ue){T=[],E(ue)}).on("end",function(){try{var ue=transformZipOutput(N,concat(A,T),y);r(ue)}catch(Z){E(Z)}T=[]}).resume()})}function StreamHelper$2(t,l,r){var E=l;switch(l){case"blob":case"arraybuffer":E="uint8array";break;case"base64":E="string";break}try{this._internalType=E,this._outputType=l,this._mimeType=r,utils$o.checkSupport(E),this._worker=t.pipe(new ConvertWorker(E)),t.lock()}catch(T){this._worker=new GenericWorker$9("error"),this._worker.error(T)}}StreamHelper$2.prototype={accumulate:function(t){return accumulate(this,t)},on:function(t,l){var r=this;return t==="data"?this._worker.on(t,function(E){l.call(r,E.data,E.meta)}):this._worker.on(t,function(){utils$o.delay(l,arguments,r)}),this},resume:function(){return utils$o.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(t){if(utils$o.checkSupport("nodestream"),this._outputType!=="nodebuffer")throw new Error(this._outputType+" is not supported by this method");return new NodejsStreamOutputAdapter(this,{objectMode:this._outputType!=="nodebuffer"},t)}};var StreamHelper_1=StreamHelper$2,defaults$1={};defaults$1.base64=!1,defaults$1.binary=!1,defaults$1.dir=!1,defaults$1.createFolders=!0,defaults$1.date=null,defaults$1.compression=null,defaults$1.compressionOptions=null,defaults$1.comment=null,defaults$1.unixPermissions=null,defaults$1.dosPermissions=null;var utils$n=utils$r,GenericWorker$8=GenericWorker_1,DEFAULT_BLOCK_SIZE=16*1024;function DataWorker$2(t){GenericWorker$8.call(this,"DataWorker");var l=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,t.then(function(r){l.dataIsReady=!0,l.data=r,l.max=r&&r.length||0,l.type=utils$n.getTypeOf(r),l.isPaused||l._tickAndRepeat()},function(r){l.error(r)})}utils$n.inherits(DataWorker$2,GenericWorker$8),DataWorker$2.prototype.cleanUp=function(){GenericWorker$8.prototype.cleanUp.call(this),this.data=null},DataWorker$2.prototype.resume=function(){return GenericWorker$8.prototype.resume.call(this)?(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,utils$n.delay(this._tickAndRepeat,[],this)),!0):!1},DataWorker$2.prototype._tickAndRepeat=function(){this._tickScheduled=!1,!(this.isPaused||this.isFinished)&&(this._tick(),this.isFinished||(utils$n.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},DataWorker$2.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var t=DEFAULT_BLOCK_SIZE,l=null,r=Math.min(this.max,this.index+t);if(this.index>=this.max)return this.end();switch(this.type){case"string":l=this.data.substring(this.index,r);break;case"uint8array":l=this.data.subarray(this.index,r);break;case"array":case"nodebuffer":l=this.data.slice(this.index,r);break}return this.index=r,this.push({data:l,meta:{percent:this.max?this.index/this.max*100:0}})};var DataWorker_1=DataWorker$2,utils$m=utils$r;function makeTable$1(){for(var t,l=[],r=0;r<256;r++){t=r;for(var E=0;E<8;E++)t=t&1?3988292384^t>>>1:t>>>1;l[r]=t}return l}var crcTable$1=makeTable$1();function crc32$5(t,l,r,E){var T=crcTable$1,A=E+r;t=t^-1;for(var N=E;N<A;N++)t=t>>>8^T[(t^l[N])&255];return t^-1}function crc32str(t,l,r,E){var T=crcTable$1,A=E+r;t=t^-1;for(var N=E;N<A;N++)t=t>>>8^T[(t^l.charCodeAt(N))&255];return t^-1}var crc32_1$1=function(l,r){if(typeof l=="undefined"||!l.length)return 0;var E=utils$m.getTypeOf(l)!=="string";return E?crc32$5(r|0,l,l.length,0):crc32str(r|0,l,l.length,0)},GenericWorker$7=GenericWorker_1,crc32$4=crc32_1$1,utils$l=utils$r;function Crc32Probe$2(){GenericWorker$7.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}utils$l.inherits(Crc32Probe$2,GenericWorker$7),Crc32Probe$2.prototype.processChunk=function(t){this.streamInfo.crc32=crc32$4(t.data,this.streamInfo.crc32||0),this.push(t)};var Crc32Probe_1=Crc32Probe$2,utils$k=utils$r,GenericWorker$6=GenericWorker_1;function DataLengthProbe$1(t){GenericWorker$6.call(this,"DataLengthProbe for "+t),this.propName=t,this.withStreamInfo(t,0)}utils$k.inherits(DataLengthProbe$1,GenericWorker$6),DataLengthProbe$1.prototype.processChunk=function(t){if(t){var l=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=l+t.data.length}GenericWorker$6.prototype.processChunk.call(this,t)};var DataLengthProbe_1=DataLengthProbe$1,external$1=external$3,DataWorker$1=DataWorker_1,Crc32Probe$1=Crc32Probe_1,DataLengthProbe=DataLengthProbe_1;function CompressedObject$3(t,l,r,E,T){this.compressedSize=t,this.uncompressedSize=l,this.crc32=r,this.compression=E,this.compressedContent=T}CompressedObject$3.prototype={getContentWorker:function(){var t=new DataWorker$1(external$1.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new DataLengthProbe("data_length")),l=this;return t.on("end",function(){if(this.streamInfo.data_length!==l.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")}),t},getCompressedWorker:function(){return new DataWorker$1(external$1.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},CompressedObject$3.createWorkerFrom=function(t,l,r){return t.pipe(new Crc32Probe$1).pipe(new DataLengthProbe("uncompressedSize")).pipe(l.compressWorker(r)).pipe(new DataLengthProbe("compressedSize")).withStreamInfo("compression",l)};var compressedObject=CompressedObject$3,StreamHelper$1=StreamHelper_1,DataWorker=DataWorker_1,utf8$4=utf8$5,CompressedObject$2=compressedObject,GenericWorker$5=GenericWorker_1,ZipObject$1=function(t,l,r){this.name=t,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=l,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}};ZipObject$1.prototype={internalStream:function(t){var l=null,r="string";try{if(!t)throw new Error("No output type specified.");r=t.toLowerCase();var E=r==="string"||r==="text";(r==="binarystring"||r==="text")&&(r="string"),l=this._decompressWorker();var T=!this._dataBinary;T&&!E&&(l=l.pipe(new utf8$4.Utf8EncodeWorker)),!T&&E&&(l=l.pipe(new utf8$4.Utf8DecodeWorker))}catch(A){l=new GenericWorker$5("error"),l.error(A)}return new StreamHelper$1(l,r,"")},async:function(t,l){return this.internalStream(t).accumulate(l)},nodeStream:function(t,l){return this.internalStream(t||"nodebuffer").toNodejsStream(l)},_compressWorker:function(t,l){if(this._data instanceof CompressedObject$2&&this._data.compression.magic===t.magic)return this._data.getCompressedWorker();var r=this._decompressWorker();return this._dataBinary||(r=r.pipe(new utf8$4.Utf8EncodeWorker)),CompressedObject$2.createWorkerFrom(r,t,l)},_decompressWorker:function(){return this._data instanceof CompressedObject$2?this._data.getContentWorker():this._data instanceof GenericWorker$5?this._data:new DataWorker(this._data)}};for(var removedMethods=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],removedFn=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},i$1=0;i$1<removedMethods.length;i$1++)ZipObject$1.prototype[removedMethods[i$1]]=removedFn;var zipObject=ZipObject$1,generate$1={},compressions$2={},flate={},common={};(function(t){var l=typeof Uint8Array!="undefined"&&typeof Uint16Array!="undefined"&&typeof Int32Array!="undefined";function r(A,N){return Object.prototype.hasOwnProperty.call(A,N)}t.assign=function(A){for(var N=Array.prototype.slice.call(arguments,1);N.length;){var y=N.shift();if(y){if(typeof y!="object")throw new TypeError(y+"must be non-object");for(var ue in y)r(y,ue)&&(A[ue]=y[ue])}}return A},t.shrinkBuf=function(A,N){return A.length===N?A:A.subarray?A.subarray(0,N):(A.length=N,A)};var E={arraySet:function(A,N,y,ue,Z){if(N.subarray&&A.subarray){A.set(N.subarray(y,y+ue),Z);return}for(var M=0;M<ue;M++)A[Z+M]=N[y+M]},flattenChunks:function(A){var N,y,ue,Z,M,Se;for(ue=0,N=0,y=A.length;N<y;N++)ue+=A[N].length;for(Se=new Uint8Array(ue),Z=0,N=0,y=A.length;N<y;N++)M=A[N],Se.set(M,Z),Z+=M.length;return Se}},T={arraySet:function(A,N,y,ue,Z){for(var M=0;M<ue;M++)A[Z+M]=N[y+M]},flattenChunks:function(A){return[].concat.apply([],A)}};t.setTyped=function(A){A?(t.Buf8=Uint8Array,t.Buf16=Uint16Array,t.Buf32=Int32Array,t.assign(t,E)):(t.Buf8=Array,t.Buf16=Array,t.Buf32=Array,t.assign(t,T))},t.setTyped(l)})(common);var deflate$4={},deflate$3={},trees$1={},utils$j=common,Z_FIXED$1=4,Z_BINARY=0,Z_TEXT=1,Z_UNKNOWN$1=2;function zero$1(t){for(var l=t.length;--l>=0;)t[l]=0}var STORED_BLOCK=0,STATIC_TREES=1,DYN_TREES=2,MIN_MATCH$1=3,MAX_MATCH$1=258,LENGTH_CODES$1=29,LITERALS$1=256,L_CODES$1=LITERALS$1+1+LENGTH_CODES$1,D_CODES$1=30,BL_CODES$1=19,HEAP_SIZE$1=2*L_CODES$1+1,MAX_BITS$1=15,Buf_size=16,MAX_BL_BITS=7,END_BLOCK=256,REP_3_6=16,REPZ_3_10=17,REPZ_11_138=18,extra_lbits=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],extra_dbits=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],extra_blbits=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],bl_order=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],DIST_CODE_LEN=512,static_ltree=new Array((L_CODES$1+2)*2);zero$1(static_ltree);var static_dtree=new Array(D_CODES$1*2);zero$1(static_dtree);var _dist_code=new Array(DIST_CODE_LEN);zero$1(_dist_code);var _length_code=new Array(MAX_MATCH$1-MIN_MATCH$1+1);zero$1(_length_code);var base_length=new Array(LENGTH_CODES$1);zero$1(base_length);var base_dist=new Array(D_CODES$1);zero$1(base_dist);function StaticTreeDesc(t,l,r,E,T){this.static_tree=t,this.extra_bits=l,this.extra_base=r,this.elems=E,this.max_length=T,this.has_stree=t&&t.length}var static_l_desc,static_d_desc,static_bl_desc;function TreeDesc(t,l){this.dyn_tree=t,this.max_code=0,this.stat_desc=l}function d_code(t){return t<256?_dist_code[t]:_dist_code[256+(t>>>7)]}function put_short(t,l){t.pending_buf[t.pending++]=l&255,t.pending_buf[t.pending++]=l>>>8&255}function send_bits(t,l,r){t.bi_valid>Buf_size-r?(t.bi_buf|=l<<t.bi_valid&65535,put_short(t,t.bi_buf),t.bi_buf=l>>Buf_size-t.bi_valid,t.bi_valid+=r-Buf_size):(t.bi_buf|=l<<t.bi_valid&65535,t.bi_valid+=r)}function send_code(t,l,r){send_bits(t,r[l*2],r[l*2+1])}function bi_reverse(t,l){var r=0;do r|=t&1,t>>>=1,r<<=1;while(--l>0);return r>>>1}function bi_flush(t){t.bi_valid===16?(put_short(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=t.bi_buf&255,t.bi_buf>>=8,t.bi_valid-=8)}function gen_bitlen(t,l){var r=l.dyn_tree,E=l.max_code,T=l.stat_desc.static_tree,A=l.stat_desc.has_stree,N=l.stat_desc.extra_bits,y=l.stat_desc.extra_base,ue=l.stat_desc.max_length,Z,M,Se,ge,_e,we,be=0;for(ge=0;ge<=MAX_BITS$1;ge++)t.bl_count[ge]=0;for(r[t.heap[t.heap_max]*2+1]=0,Z=t.heap_max+1;Z<HEAP_SIZE$1;Z++)M=t.heap[Z],ge=r[r[M*2+1]*2+1]+1,ge>ue&&(ge=ue,be++),r[M*2+1]=ge,!(M>E)&&(t.bl_count[ge]++,_e=0,M>=y&&(_e=N[M-y]),we=r[M*2],t.opt_len+=we*(ge+_e),A&&(t.static_len+=we*(T[M*2+1]+_e)));if(be!==0){do{for(ge=ue-1;t.bl_count[ge]===0;)ge--;t.bl_count[ge]--,t.bl_count[ge+1]+=2,t.bl_count[ue]--,be-=2}while(be>0);for(ge=ue;ge!==0;ge--)for(M=t.bl_count[ge];M!==0;)Se=t.heap[--Z],!(Se>E)&&(r[Se*2+1]!==ge&&(t.opt_len+=(ge-r[Se*2+1])*r[Se*2],r[Se*2+1]=ge),M--)}}function gen_codes(t,l,r){var E=new Array(MAX_BITS$1+1),T=0,A,N;for(A=1;A<=MAX_BITS$1;A++)E[A]=T=T+r[A-1]<<1;for(N=0;N<=l;N++){var y=t[N*2+1];y!==0&&(t[N*2]=bi_reverse(E[y]++,y))}}function tr_static_init(){var t,l,r,E,T,A=new Array(MAX_BITS$1+1);for(r=0,E=0;E<LENGTH_CODES$1-1;E++)for(base_length[E]=r,t=0;t<1<<extra_lbits[E];t++)_length_code[r++]=E;for(_length_code[r-1]=E,T=0,E=0;E<16;E++)for(base_dist[E]=T,t=0;t<1<<extra_dbits[E];t++)_dist_code[T++]=E;for(T>>=7;E<D_CODES$1;E++)for(base_dist[E]=T<<7,t=0;t<1<<extra_dbits[E]-7;t++)_dist_code[256+T++]=E;for(l=0;l<=MAX_BITS$1;l++)A[l]=0;for(t=0;t<=143;)static_ltree[t*2+1]=8,t++,A[8]++;for(;t<=255;)static_ltree[t*2+1]=9,t++,A[9]++;for(;t<=279;)static_ltree[t*2+1]=7,t++,A[7]++;for(;t<=287;)static_ltree[t*2+1]=8,t++,A[8]++;for(gen_codes(static_ltree,L_CODES$1+1,A),t=0;t<D_CODES$1;t++)static_dtree[t*2+1]=5,static_dtree[t*2]=bi_reverse(t,5);static_l_desc=new StaticTreeDesc(static_ltree,extra_lbits,LITERALS$1+1,L_CODES$1,MAX_BITS$1),static_d_desc=new StaticTreeDesc(static_dtree,extra_dbits,0,D_CODES$1,MAX_BITS$1),static_bl_desc=new StaticTreeDesc(new Array(0),extra_blbits,0,BL_CODES$1,MAX_BL_BITS)}function init_block(t){var l;for(l=0;l<L_CODES$1;l++)t.dyn_ltree[l*2]=0;for(l=0;l<D_CODES$1;l++)t.dyn_dtree[l*2]=0;for(l=0;l<BL_CODES$1;l++)t.bl_tree[l*2]=0;t.dyn_ltree[END_BLOCK*2]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function bi_windup(t){t.bi_valid>8?put_short(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function copy_block(t,l,r,E){bi_windup(t),E&&(put_short(t,r),put_short(t,~r)),utils$j.arraySet(t.pending_buf,t.window,l,r,t.pending),t.pending+=r}function smaller(t,l,r,E){var T=l*2,A=r*2;return t[T]<t[A]||t[T]===t[A]&&E[l]<=E[r]}function pqdownheap(t,l,r){for(var E=t.heap[r],T=r<<1;T<=t.heap_len&&(T<t.heap_len&&smaller(l,t.heap[T+1],t.heap[T],t.depth)&&T++,!smaller(l,E,t.heap[T],t.depth));)t.heap[r]=t.heap[T],r=T,T<<=1;t.heap[r]=E}function compress_block(t,l,r){var E,T,A=0,N,y;if(t.last_lit!==0)do E=t.pending_buf[t.d_buf+A*2]<<8|t.pending_buf[t.d_buf+A*2+1],T=t.pending_buf[t.l_buf+A],A++,E===0?send_code(t,T,l):(N=_length_code[T],send_code(t,N+LITERALS$1+1,l),y=extra_lbits[N],y!==0&&(T-=base_length[N],send_bits(t,T,y)),E--,N=d_code(E),send_code(t,N,r),y=extra_dbits[N],y!==0&&(E-=base_dist[N],send_bits(t,E,y)));while(A<t.last_lit);send_code(t,END_BLOCK,l)}function build_tree(t,l){var r=l.dyn_tree,E=l.stat_desc.static_tree,T=l.stat_desc.has_stree,A=l.stat_desc.elems,N,y,ue=-1,Z;for(t.heap_len=0,t.heap_max=HEAP_SIZE$1,N=0;N<A;N++)r[N*2]!==0?(t.heap[++t.heap_len]=ue=N,t.depth[N]=0):r[N*2+1]=0;for(;t.heap_len<2;)Z=t.heap[++t.heap_len]=ue<2?++ue:0,r[Z*2]=1,t.depth[Z]=0,t.opt_len--,T&&(t.static_len-=E[Z*2+1]);for(l.max_code=ue,N=t.heap_len>>1;N>=1;N--)pqdownheap(t,r,N);Z=A;do N=t.heap[1],t.heap[1]=t.heap[t.heap_len--],pqdownheap(t,r,1),y=t.heap[1],t.heap[--t.heap_max]=N,t.heap[--t.heap_max]=y,r[Z*2]=r[N*2]+r[y*2],t.depth[Z]=(t.depth[N]>=t.depth[y]?t.depth[N]:t.depth[y])+1,r[N*2+1]=r[y*2+1]=Z,t.heap[1]=Z++,pqdownheap(t,r,1);while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],gen_bitlen(t,l),gen_codes(r,ue,t.bl_count)}function scan_tree(t,l,r){var E,T=-1,A,N=l[0*2+1],y=0,ue=7,Z=4;for(N===0&&(ue=138,Z=3),l[(r+1)*2+1]=65535,E=0;E<=r;E++)A=N,N=l[(E+1)*2+1],!(++y<ue&&A===N)&&(y<Z?t.bl_tree[A*2]+=y:A!==0?(A!==T&&t.bl_tree[A*2]++,t.bl_tree[REP_3_6*2]++):y<=10?t.bl_tree[REPZ_3_10*2]++:t.bl_tree[REPZ_11_138*2]++,y=0,T=A,N===0?(ue=138,Z=3):A===N?(ue=6,Z=3):(ue=7,Z=4))}function send_tree(t,l,r){var E,T=-1,A,N=l[0*2+1],y=0,ue=7,Z=4;for(N===0&&(ue=138,Z=3),E=0;E<=r;E++)if(A=N,N=l[(E+1)*2+1],!(++y<ue&&A===N)){if(y<Z)do send_code(t,A,t.bl_tree);while(--y!==0);else A!==0?(A!==T&&(send_code(t,A,t.bl_tree),y--),send_code(t,REP_3_6,t.bl_tree),send_bits(t,y-3,2)):y<=10?(send_code(t,REPZ_3_10,t.bl_tree),send_bits(t,y-3,3)):(send_code(t,REPZ_11_138,t.bl_tree),send_bits(t,y-11,7));y=0,T=A,N===0?(ue=138,Z=3):A===N?(ue=6,Z=3):(ue=7,Z=4)}}function build_bl_tree(t){var l;for(scan_tree(t,t.dyn_ltree,t.l_desc.max_code),scan_tree(t,t.dyn_dtree,t.d_desc.max_code),build_tree(t,t.bl_desc),l=BL_CODES$1-1;l>=3&&t.bl_tree[bl_order[l]*2+1]===0;l--);return t.opt_len+=3*(l+1)+5+5+4,l}function send_all_trees(t,l,r,E){var T;for(send_bits(t,l-257,5),send_bits(t,r-1,5),send_bits(t,E-4,4),T=0;T<E;T++)send_bits(t,t.bl_tree[bl_order[T]*2+1],3);send_tree(t,t.dyn_ltree,l-1),send_tree(t,t.dyn_dtree,r-1)}function detect_data_type(t){var l=4093624447,r;for(r=0;r<=31;r++,l>>>=1)if(l&1&&t.dyn_ltree[r*2]!==0)return Z_BINARY;if(t.dyn_ltree[9*2]!==0||t.dyn_ltree[10*2]!==0||t.dyn_ltree[13*2]!==0)return Z_TEXT;for(r=32;r<LITERALS$1;r++)if(t.dyn_ltree[r*2]!==0)return Z_TEXT;return Z_BINARY}var static_init_done=!1;function _tr_init(t){static_init_done||(tr_static_init(),static_init_done=!0),t.l_desc=new TreeDesc(t.dyn_ltree,static_l_desc),t.d_desc=new TreeDesc(t.dyn_dtree,static_d_desc),t.bl_desc=new TreeDesc(t.bl_tree,static_bl_desc),t.bi_buf=0,t.bi_valid=0,init_block(t)}function _tr_stored_block(t,l,r,E){send_bits(t,(STORED_BLOCK<<1)+(E?1:0),3),copy_block(t,l,r,!0)}function _tr_align(t){send_bits(t,STATIC_TREES<<1,3),send_code(t,END_BLOCK,static_ltree),bi_flush(t)}function _tr_flush_block(t,l,r,E){var T,A,N=0;t.level>0?(t.strm.data_type===Z_UNKNOWN$1&&(t.strm.data_type=detect_data_type(t)),build_tree(t,t.l_desc),build_tree(t,t.d_desc),N=build_bl_tree(t),T=t.opt_len+3+7>>>3,A=t.static_len+3+7>>>3,A<=T&&(T=A)):T=A=r+5,r+4<=T&&l!==-1?_tr_stored_block(t,l,r,E):t.strategy===Z_FIXED$1||A===T?(send_bits(t,(STATIC_TREES<<1)+(E?1:0),3),compress_block(t,static_ltree,static_dtree)):(send_bits(t,(DYN_TREES<<1)+(E?1:0),3),send_all_trees(t,t.l_desc.max_code+1,t.d_desc.max_code+1,N+1),compress_block(t,t.dyn_ltree,t.dyn_dtree)),init_block(t),E&&bi_windup(t)}function _tr_tally(t,l,r){return t.pending_buf[t.d_buf+t.last_lit*2]=l>>>8&255,t.pending_buf[t.d_buf+t.last_lit*2+1]=l&255,t.pending_buf[t.l_buf+t.last_lit]=r&255,t.last_lit++,l===0?t.dyn_ltree[r*2]++:(t.matches++,l--,t.dyn_ltree[(_length_code[r]+LITERALS$1+1)*2]++,t.dyn_dtree[d_code(l)*2]++),t.last_lit===t.lit_bufsize-1}trees$1._tr_init=_tr_init,trees$1._tr_stored_block=_tr_stored_block,trees$1._tr_flush_block=_tr_flush_block,trees$1._tr_tally=_tr_tally,trees$1._tr_align=_tr_align;function adler32$2(t,l,r,E){for(var T=t&65535|0,A=t>>>16&65535|0,N=0;r!==0;){N=r>2e3?2e3:r,r-=N;do T=T+l[E++]|0,A=A+T|0;while(--N);T%=65521,A%=65521}return T|A<<16|0}var adler32_1=adler32$2;function makeTable(){for(var t,l=[],r=0;r<256;r++){t=r;for(var E=0;E<8;E++)t=t&1?3988292384^t>>>1:t>>>1;l[r]=t}return l}var crcTable=makeTable();function crc32$3(t,l,r,E){var T=crcTable,A=E+r;t^=-1;for(var N=E;N<A;N++)t=t>>>8^T[(t^l[N])&255];return t^-1}var crc32_1=crc32$3,messages={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},utils$i=common,trees=trees$1,adler32$1=adler32_1,crc32$2=crc32_1,msg$2=messages,Z_NO_FLUSH$1=0,Z_PARTIAL_FLUSH=1,Z_FULL_FLUSH=3,Z_FINISH$2=4,Z_BLOCK$1=5,Z_OK$2=0,Z_STREAM_END$2=1,Z_STREAM_ERROR$1=-2,Z_DATA_ERROR$1=-3,Z_BUF_ERROR$1=-5,Z_DEFAULT_COMPRESSION$1=-1,Z_FILTERED=1,Z_HUFFMAN_ONLY=2,Z_RLE=3,Z_FIXED=4,Z_DEFAULT_STRATEGY$1=0,Z_UNKNOWN=2,Z_DEFLATED$2=8,MAX_MEM_LEVEL=9,MAX_WBITS$1=15,DEF_MEM_LEVEL=8,LENGTH_CODES=29,LITERALS=256,L_CODES=LITERALS+1+LENGTH_CODES,D_CODES=30,BL_CODES=19,HEAP_SIZE=2*L_CODES+1,MAX_BITS=15,MIN_MATCH=3,MAX_MATCH=258,MIN_LOOKAHEAD=MAX_MATCH+MIN_MATCH+1,PRESET_DICT=32,INIT_STATE=42,EXTRA_STATE=69,NAME_STATE=73,COMMENT_STATE=91,HCRC_STATE=103,BUSY_STATE=113,FINISH_STATE=666,BS_NEED_MORE=1,BS_BLOCK_DONE=2,BS_FINISH_STARTED=3,BS_FINISH_DONE=4,OS_CODE=3;function err(t,l){return t.msg=msg$2[l],l}function rank(t){return(t<<1)-(t>4?9:0)}function zero(t){for(var l=t.length;--l>=0;)t[l]=0}function flush_pending(t){var l=t.state,r=l.pending;r>t.avail_out&&(r=t.avail_out),r!==0&&(utils$i.arraySet(t.output,l.pending_buf,l.pending_out,r,t.next_out),t.next_out+=r,l.pending_out+=r,t.total_out+=r,t.avail_out-=r,l.pending-=r,l.pending===0&&(l.pending_out=0))}function flush_block_only(t,l){trees._tr_flush_block(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,l),t.block_start=t.strstart,flush_pending(t.strm)}function put_byte(t,l){t.pending_buf[t.pending++]=l}function putShortMSB(t,l){t.pending_buf[t.pending++]=l>>>8&255,t.pending_buf[t.pending++]=l&255}function read_buf(t,l,r,E){var T=t.avail_in;return T>E&&(T=E),T===0?0:(t.avail_in-=T,utils$i.arraySet(l,t.input,t.next_in,T,r),t.state.wrap===1?t.adler=adler32$1(t.adler,l,T,r):t.state.wrap===2&&(t.adler=crc32$2(t.adler,l,T,r)),t.next_in+=T,t.total_in+=T,T)}function longest_match(t,l){var r=t.max_chain_length,E=t.strstart,T,A,N=t.prev_length,y=t.nice_match,ue=t.strstart>t.w_size-MIN_LOOKAHEAD?t.strstart-(t.w_size-MIN_LOOKAHEAD):0,Z=t.window,M=t.w_mask,Se=t.prev,ge=t.strstart+MAX_MATCH,_e=Z[E+N-1],we=Z[E+N];t.prev_length>=t.good_match&&(r>>=2),y>t.lookahead&&(y=t.lookahead);do if(T=l,!(Z[T+N]!==we||Z[T+N-1]!==_e||Z[T]!==Z[E]||Z[++T]!==Z[E+1])){E+=2,T++;do;while(Z[++E]===Z[++T]&&Z[++E]===Z[++T]&&Z[++E]===Z[++T]&&Z[++E]===Z[++T]&&Z[++E]===Z[++T]&&Z[++E]===Z[++T]&&Z[++E]===Z[++T]&&Z[++E]===Z[++T]&&E<ge);if(A=MAX_MATCH-(ge-E),E=ge-MAX_MATCH,A>N){if(t.match_start=l,N=A,A>=y)break;_e=Z[E+N-1],we=Z[E+N]}}while((l=Se[l&M])>ue&&--r!==0);return N<=t.lookahead?N:t.lookahead}function fill_window(t){var l=t.w_size,r,E,T,A,N;do{if(A=t.window_size-t.lookahead-t.strstart,t.strstart>=l+(l-MIN_LOOKAHEAD)){utils$i.arraySet(t.window,t.window,l,l,0),t.match_start-=l,t.strstart-=l,t.block_start-=l,E=t.hash_size,r=E;do T=t.head[--r],t.head[r]=T>=l?T-l:0;while(--E);E=l,r=E;do T=t.prev[--r],t.prev[r]=T>=l?T-l:0;while(--E);A+=l}if(t.strm.avail_in===0)break;if(E=read_buf(t.strm,t.window,t.strstart+t.lookahead,A),t.lookahead+=E,t.lookahead+t.insert>=MIN_MATCH)for(N=t.strstart-t.insert,t.ins_h=t.window[N],t.ins_h=(t.ins_h<<t.hash_shift^t.window[N+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[N+MIN_MATCH-1])&t.hash_mask,t.prev[N&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=N,N++,t.insert--,!(t.lookahead+t.insert<MIN_MATCH)););}while(t.lookahead<MIN_LOOKAHEAD&&t.strm.avail_in!==0)}function deflate_stored(t,l){var r=65535;for(r>t.pending_buf_size-5&&(r=t.pending_buf_size-5);;){if(t.lookahead<=1){if(fill_window(t),t.lookahead===0&&l===Z_NO_FLUSH$1)return BS_NEED_MORE;if(t.lookahead===0)break}t.strstart+=t.lookahead,t.lookahead=0;var E=t.block_start+r;if((t.strstart===0||t.strstart>=E)&&(t.lookahead=t.strstart-E,t.strstart=E,flush_block_only(t,!1),t.strm.avail_out===0)||t.strstart-t.block_start>=t.w_size-MIN_LOOKAHEAD&&(flush_block_only(t,!1),t.strm.avail_out===0))return BS_NEED_MORE}return t.insert=0,l===Z_FINISH$2?(flush_block_only(t,!0),t.strm.avail_out===0?BS_FINISH_STARTED:BS_FINISH_DONE):(t.strstart>t.block_start&&(flush_block_only(t,!1),t.strm.avail_out===0),BS_NEED_MORE)}function deflate_fast(t,l){for(var r,E;;){if(t.lookahead<MIN_LOOKAHEAD){if(fill_window(t),t.lookahead<MIN_LOOKAHEAD&&l===Z_NO_FLUSH$1)return BS_NEED_MORE;if(t.lookahead===0)break}if(r=0,t.lookahead>=MIN_MATCH&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+MIN_MATCH-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),r!==0&&t.strstart-r<=t.w_size-MIN_LOOKAHEAD&&(t.match_length=longest_match(t,r)),t.match_length>=MIN_MATCH)if(E=trees._tr_tally(t,t.strstart-t.match_start,t.match_length-MIN_MATCH),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=MIN_MATCH){t.match_length--;do t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+MIN_MATCH-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart;while(--t.match_length!==0);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else E=trees._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(E&&(flush_block_only(t,!1),t.strm.avail_out===0))return BS_NEED_MORE}return t.insert=t.strstart<MIN_MATCH-1?t.strstart:MIN_MATCH-1,l===Z_FINISH$2?(flush_block_only(t,!0),t.strm.avail_out===0?BS_FINISH_STARTED:BS_FINISH_DONE):t.last_lit&&(flush_block_only(t,!1),t.strm.avail_out===0)?BS_NEED_MORE:BS_BLOCK_DONE}function deflate_slow(t,l){for(var r,E,T;;){if(t.lookahead<MIN_LOOKAHEAD){if(fill_window(t),t.lookahead<MIN_LOOKAHEAD&&l===Z_NO_FLUSH$1)return BS_NEED_MORE;if(t.lookahead===0)break}if(r=0,t.lookahead>=MIN_MATCH&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+MIN_MATCH-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=MIN_MATCH-1,r!==0&&t.prev_length<t.max_lazy_match&&t.strstart-r<=t.w_size-MIN_LOOKAHEAD&&(t.match_length=longest_match(t,r),t.match_length<=5&&(t.strategy===Z_FILTERED||t.match_length===MIN_MATCH&&t.strstart-t.match_start>4096)&&(t.match_length=MIN_MATCH-1)),t.prev_length>=MIN_MATCH&&t.match_length<=t.prev_length){T=t.strstart+t.lookahead-MIN_MATCH,E=trees._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-MIN_MATCH),t.lookahead-=t.prev_length-1,t.prev_length-=2;do++t.strstart<=T&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+MIN_MATCH-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart);while(--t.prev_length!==0);if(t.match_available=0,t.match_length=MIN_MATCH-1,t.strstart++,E&&(flush_block_only(t,!1),t.strm.avail_out===0))return BS_NEED_MORE}else if(t.match_available){if(E=trees._tr_tally(t,0,t.window[t.strstart-1]),E&&flush_block_only(t,!1),t.strstart++,t.lookahead--,t.strm.avail_out===0)return BS_NEED_MORE}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(E=trees._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<MIN_MATCH-1?t.strstart:MIN_MATCH-1,l===Z_FINISH$2?(flush_block_only(t,!0),t.strm.avail_out===0?BS_FINISH_STARTED:BS_FINISH_DONE):t.last_lit&&(flush_block_only(t,!1),t.strm.avail_out===0)?BS_NEED_MORE:BS_BLOCK_DONE}function deflate_rle(t,l){for(var r,E,T,A,N=t.window;;){if(t.lookahead<=MAX_MATCH){if(fill_window(t),t.lookahead<=MAX_MATCH&&l===Z_NO_FLUSH$1)return BS_NEED_MORE;if(t.lookahead===0)break}if(t.match_length=0,t.lookahead>=MIN_MATCH&&t.strstart>0&&(T=t.strstart-1,E=N[T],E===N[++T]&&E===N[++T]&&E===N[++T])){A=t.strstart+MAX_MATCH;do;while(E===N[++T]&&E===N[++T]&&E===N[++T]&&E===N[++T]&&E===N[++T]&&E===N[++T]&&E===N[++T]&&E===N[++T]&&T<A);t.match_length=MAX_MATCH-(A-T),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=MIN_MATCH?(r=trees._tr_tally(t,1,t.match_length-MIN_MATCH),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(r=trees._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),r&&(flush_block_only(t,!1),t.strm.avail_out===0))return BS_NEED_MORE}return t.insert=0,l===Z_FINISH$2?(flush_block_only(t,!0),t.strm.avail_out===0?BS_FINISH_STARTED:BS_FINISH_DONE):t.last_lit&&(flush_block_only(t,!1),t.strm.avail_out===0)?BS_NEED_MORE:BS_BLOCK_DONE}function deflate_huff(t,l){for(var r;;){if(t.lookahead===0&&(fill_window(t),t.lookahead===0)){if(l===Z_NO_FLUSH$1)return BS_NEED_MORE;break}if(t.match_length=0,r=trees._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,r&&(flush_block_only(t,!1),t.strm.avail_out===0))return BS_NEED_MORE}return t.insert=0,l===Z_FINISH$2?(flush_block_only(t,!0),t.strm.avail_out===0?BS_FINISH_STARTED:BS_FINISH_DONE):t.last_lit&&(flush_block_only(t,!1),t.strm.avail_out===0)?BS_NEED_MORE:BS_BLOCK_DONE}function Config(t,l,r,E,T){this.good_length=t,this.max_lazy=l,this.nice_length=r,this.max_chain=E,this.func=T}var configuration_table;configuration_table=[new Config(0,0,0,0,deflate_stored),new Config(4,4,8,4,deflate_fast),new Config(4,5,16,8,deflate_fast),new Config(4,6,32,32,deflate_fast),new Config(4,4,16,16,deflate_slow),new Config(8,16,32,32,deflate_slow),new Config(8,16,128,128,deflate_slow),new Config(8,32,128,256,deflate_slow),new Config(32,128,258,1024,deflate_slow),new Config(32,258,258,4096,deflate_slow)];function lm_init(t){t.window_size=2*t.w_size,zero(t.head),t.max_lazy_match=configuration_table[t.level].max_lazy,t.good_match=configuration_table[t.level].good_length,t.nice_match=configuration_table[t.level].nice_length,t.max_chain_length=configuration_table[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=MIN_MATCH-1,t.match_available=0,t.ins_h=0}function DeflateState(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Z_DEFLATED$2,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new utils$i.Buf16(HEAP_SIZE*2),this.dyn_dtree=new utils$i.Buf16((2*D_CODES+1)*2),this.bl_tree=new utils$i.Buf16((2*BL_CODES+1)*2),zero(this.dyn_ltree),zero(this.dyn_dtree),zero(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new utils$i.Buf16(MAX_BITS+1),this.heap=new utils$i.Buf16(2*L_CODES+1),zero(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new utils$i.Buf16(2*L_CODES+1),zero(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function deflateResetKeep(t){var l;return!t||!t.state?err(t,Z_STREAM_ERROR$1):(t.total_in=t.total_out=0,t.data_type=Z_UNKNOWN,l=t.state,l.pending=0,l.pending_out=0,l.wrap<0&&(l.wrap=-l.wrap),l.status=l.wrap?INIT_STATE:BUSY_STATE,t.adler=l.wrap===2?0:1,l.last_flush=Z_NO_FLUSH$1,trees._tr_init(l),Z_OK$2)}function deflateReset(t){var l=deflateResetKeep(t);return l===Z_OK$2&&lm_init(t.state),l}function deflateSetHeader(t,l){return!t||!t.state||t.state.wrap!==2?Z_STREAM_ERROR$1:(t.state.gzhead=l,Z_OK$2)}function deflateInit2(t,l,r,E,T,A){if(!t)return Z_STREAM_ERROR$1;var N=1;if(l===Z_DEFAULT_COMPRESSION$1&&(l=6),E<0?(N=0,E=-E):E>15&&(N=2,E-=16),T<1||T>MAX_MEM_LEVEL||r!==Z_DEFLATED$2||E<8||E>15||l<0||l>9||A<0||A>Z_FIXED)return err(t,Z_STREAM_ERROR$1);E===8&&(E=9);var y=new DeflateState;return t.state=y,y.strm=t,y.wrap=N,y.gzhead=null,y.w_bits=E,y.w_size=1<<y.w_bits,y.w_mask=y.w_size-1,y.hash_bits=T+7,y.hash_size=1<<y.hash_bits,y.hash_mask=y.hash_size-1,y.hash_shift=~~((y.hash_bits+MIN_MATCH-1)/MIN_MATCH),y.window=new utils$i.Buf8(y.w_size*2),y.head=new utils$i.Buf16(y.hash_size),y.prev=new utils$i.Buf16(y.w_size),y.lit_bufsize=1<<T+6,y.pending_buf_size=y.lit_bufsize*4,y.pending_buf=new utils$i.Buf8(y.pending_buf_size),y.d_buf=1*y.lit_bufsize,y.l_buf=3*y.lit_bufsize,y.level=l,y.strategy=A,y.method=r,deflateReset(t)}function deflateInit(t,l){return deflateInit2(t,l,Z_DEFLATED$2,MAX_WBITS$1,DEF_MEM_LEVEL,Z_DEFAULT_STRATEGY$1)}function deflate$2(t,l){var r,E,T,A;if(!t||!t.state||l>Z_BLOCK$1||l<0)return t?err(t,Z_STREAM_ERROR$1):Z_STREAM_ERROR$1;if(E=t.state,!t.output||!t.input&&t.avail_in!==0||E.status===FINISH_STATE&&l!==Z_FINISH$2)return err(t,t.avail_out===0?Z_BUF_ERROR$1:Z_STREAM_ERROR$1);if(E.strm=t,r=E.last_flush,E.last_flush=l,E.status===INIT_STATE)if(E.wrap===2)t.adler=0,put_byte(E,31),put_byte(E,139),put_byte(E,8),E.gzhead?(put_byte(E,(E.gzhead.text?1:0)+(E.gzhead.hcrc?2:0)+(E.gzhead.extra?4:0)+(E.gzhead.name?8:0)+(E.gzhead.comment?16:0)),put_byte(E,E.gzhead.time&255),put_byte(E,E.gzhead.time>>8&255),put_byte(E,E.gzhead.time>>16&255),put_byte(E,E.gzhead.time>>24&255),put_byte(E,E.level===9?2:E.strategy>=Z_HUFFMAN_ONLY||E.level<2?4:0),put_byte(E,E.gzhead.os&255),E.gzhead.extra&&E.gzhead.extra.length&&(put_byte(E,E.gzhead.extra.length&255),put_byte(E,E.gzhead.extra.length>>8&255)),E.gzhead.hcrc&&(t.adler=crc32$2(t.adler,E.pending_buf,E.pending,0)),E.gzindex=0,E.status=EXTRA_STATE):(put_byte(E,0),put_byte(E,0),put_byte(E,0),put_byte(E,0),put_byte(E,0),put_byte(E,E.level===9?2:E.strategy>=Z_HUFFMAN_ONLY||E.level<2?4:0),put_byte(E,OS_CODE),E.status=BUSY_STATE);else{var N=Z_DEFLATED$2+(E.w_bits-8<<4)<<8,y=-1;E.strategy>=Z_HUFFMAN_ONLY||E.level<2?y=0:E.level<6?y=1:E.level===6?y=2:y=3,N|=y<<6,E.strstart!==0&&(N|=PRESET_DICT),N+=31-N%31,E.status=BUSY_STATE,putShortMSB(E,N),E.strstart!==0&&(putShortMSB(E,t.adler>>>16),putShortMSB(E,t.adler&65535)),t.adler=1}if(E.status===EXTRA_STATE)if(E.gzhead.extra){for(T=E.pending;E.gzindex<(E.gzhead.extra.length&65535)&&!(E.pending===E.pending_buf_size&&(E.gzhead.hcrc&&E.pending>T&&(t.adler=crc32$2(t.adler,E.pending_buf,E.pending-T,T)),flush_pending(t),T=E.pending,E.pending===E.pending_buf_size));)put_byte(E,E.gzhead.extra[E.gzindex]&255),E.gzindex++;E.gzhead.hcrc&&E.pending>T&&(t.adler=crc32$2(t.adler,E.pending_buf,E.pending-T,T)),E.gzindex===E.gzhead.extra.length&&(E.gzindex=0,E.status=NAME_STATE)}else E.status=NAME_STATE;if(E.status===NAME_STATE)if(E.gzhead.name){T=E.pending;do{if(E.pending===E.pending_buf_size&&(E.gzhead.hcrc&&E.pending>T&&(t.adler=crc32$2(t.adler,E.pending_buf,E.pending-T,T)),flush_pending(t),T=E.pending,E.pending===E.pending_buf_size)){A=1;break}E.gzindex<E.gzhead.name.length?A=E.gzhead.name.charCodeAt(E.gzindex++)&255:A=0,put_byte(E,A)}while(A!==0);E.gzhead.hcrc&&E.pending>T&&(t.adler=crc32$2(t.adler,E.pending_buf,E.pending-T,T)),A===0&&(E.gzindex=0,E.status=COMMENT_STATE)}else E.status=COMMENT_STATE;if(E.status===COMMENT_STATE)if(E.gzhead.comment){T=E.pending;do{if(E.pending===E.pending_buf_size&&(E.gzhead.hcrc&&E.pending>T&&(t.adler=crc32$2(t.adler,E.pending_buf,E.pending-T,T)),flush_pending(t),T=E.pending,E.pending===E.pending_buf_size)){A=1;break}E.gzindex<E.gzhead.comment.length?A=E.gzhead.comment.charCodeAt(E.gzindex++)&255:A=0,put_byte(E,A)}while(A!==0);E.gzhead.hcrc&&E.pending>T&&(t.adler=crc32$2(t.adler,E.pending_buf,E.pending-T,T)),A===0&&(E.status=HCRC_STATE)}else E.status=HCRC_STATE;if(E.status===HCRC_STATE&&(E.gzhead.hcrc?(E.pending+2>E.pending_buf_size&&flush_pending(t),E.pending+2<=E.pending_buf_size&&(put_byte(E,t.adler&255),put_byte(E,t.adler>>8&255),t.adler=0,E.status=BUSY_STATE)):E.status=BUSY_STATE),E.pending!==0){if(flush_pending(t),t.avail_out===0)return E.last_flush=-1,Z_OK$2}else if(t.avail_in===0&&rank(l)<=rank(r)&&l!==Z_FINISH$2)return err(t,Z_BUF_ERROR$1);if(E.status===FINISH_STATE&&t.avail_in!==0)return err(t,Z_BUF_ERROR$1);if(t.avail_in!==0||E.lookahead!==0||l!==Z_NO_FLUSH$1&&E.status!==FINISH_STATE){var ue=E.strategy===Z_HUFFMAN_ONLY?deflate_huff(E,l):E.strategy===Z_RLE?deflate_rle(E,l):configuration_table[E.level].func(E,l);if((ue===BS_FINISH_STARTED||ue===BS_FINISH_DONE)&&(E.status=FINISH_STATE),ue===BS_NEED_MORE||ue===BS_FINISH_STARTED)return t.avail_out===0&&(E.last_flush=-1),Z_OK$2;if(ue===BS_BLOCK_DONE&&(l===Z_PARTIAL_FLUSH?trees._tr_align(E):l!==Z_BLOCK$1&&(trees._tr_stored_block(E,0,0,!1),l===Z_FULL_FLUSH&&(zero(E.head),E.lookahead===0&&(E.strstart=0,E.block_start=0,E.insert=0))),flush_pending(t),t.avail_out===0))return E.last_flush=-1,Z_OK$2}return l!==Z_FINISH$2?Z_OK$2:E.wrap<=0?Z_STREAM_END$2:(E.wrap===2?(put_byte(E,t.adler&255),put_byte(E,t.adler>>8&255),put_byte(E,t.adler>>16&255),put_byte(E,t.adler>>24&255),put_byte(E,t.total_in&255),put_byte(E,t.total_in>>8&255),put_byte(E,t.total_in>>16&255),put_byte(E,t.total_in>>24&255)):(putShortMSB(E,t.adler>>>16),putShortMSB(E,t.adler&65535)),flush_pending(t),E.wrap>0&&(E.wrap=-E.wrap),E.pending!==0?Z_OK$2:Z_STREAM_END$2)}function deflateEnd(t){var l;return!t||!t.state?Z_STREAM_ERROR$1:(l=t.state.status,l!==INIT_STATE&&l!==EXTRA_STATE&&l!==NAME_STATE&&l!==COMMENT_STATE&&l!==HCRC_STATE&&l!==BUSY_STATE&&l!==FINISH_STATE?err(t,Z_STREAM_ERROR$1):(t.state=null,l===BUSY_STATE?err(t,Z_DATA_ERROR$1):Z_OK$2))}function deflateSetDictionary(t,l){var r=l.length,E,T,A,N,y,ue,Z,M;if(!t||!t.state||(E=t.state,N=E.wrap,N===2||N===1&&E.status!==INIT_STATE||E.lookahead))return Z_STREAM_ERROR$1;for(N===1&&(t.adler=adler32$1(t.adler,l,r,0)),E.wrap=0,r>=E.w_size&&(N===0&&(zero(E.head),E.strstart=0,E.block_start=0,E.insert=0),M=new utils$i.Buf8(E.w_size),utils$i.arraySet(M,l,r-E.w_size,E.w_size,0),l=M,r=E.w_size),y=t.avail_in,ue=t.next_in,Z=t.input,t.avail_in=r,t.next_in=0,t.input=l,fill_window(E);E.lookahead>=MIN_MATCH;){T=E.strstart,A=E.lookahead-(MIN_MATCH-1);do E.ins_h=(E.ins_h<<E.hash_shift^E.window[T+MIN_MATCH-1])&E.hash_mask,E.prev[T&E.w_mask]=E.head[E.ins_h],E.head[E.ins_h]=T,T++;while(--A);E.strstart=T,E.lookahead=MIN_MATCH-1,fill_window(E)}return E.strstart+=E.lookahead,E.block_start=E.strstart,E.insert=E.lookahead,E.lookahead=0,E.match_length=E.prev_length=MIN_MATCH-1,E.match_available=0,t.next_in=ue,t.input=Z,t.avail_in=y,E.wrap=N,Z_OK$2}deflate$3.deflateInit=deflateInit,deflate$3.deflateInit2=deflateInit2,deflate$3.deflateReset=deflateReset,deflate$3.deflateResetKeep=deflateResetKeep,deflate$3.deflateSetHeader=deflateSetHeader,deflate$3.deflate=deflate$2,deflate$3.deflateEnd=deflateEnd,deflate$3.deflateSetDictionary=deflateSetDictionary,deflate$3.deflateInfo="pako deflate (from Nodeca project)";var strings$2={},utils$h=common,STR_APPLY_OK=!0,STR_APPLY_UIA_OK=!0;try{String.fromCharCode.apply(null,[0])}catch(t){STR_APPLY_OK=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){STR_APPLY_UIA_OK=!1}for(var _utf8len=new utils$h.Buf8(256),q=0;q<256;q++)_utf8len[q]=q>=252?6:q>=248?5:q>=240?4:q>=224?3:q>=192?2:1;_utf8len[254]=_utf8len[254]=1,strings$2.string2buf=function(t){var l,r,E,T,A,N=t.length,y=0;for(T=0;T<N;T++)r=t.charCodeAt(T),(r&64512)===55296&&T+1<N&&(E=t.charCodeAt(T+1),(E&64512)===56320&&(r=65536+(r-55296<<10)+(E-56320),T++)),y+=r<128?1:r<2048?2:r<65536?3:4;for(l=new utils$h.Buf8(y),A=0,T=0;A<y;T++)r=t.charCodeAt(T),(r&64512)===55296&&T+1<N&&(E=t.charCodeAt(T+1),(E&64512)===56320&&(r=65536+(r-55296<<10)+(E-56320),T++)),r<128?l[A++]=r:r<2048?(l[A++]=192|r>>>6,l[A++]=128|r&63):r<65536?(l[A++]=224|r>>>12,l[A++]=128|r>>>6&63,l[A++]=128|r&63):(l[A++]=240|r>>>18,l[A++]=128|r>>>12&63,l[A++]=128|r>>>6&63,l[A++]=128|r&63);return l};function buf2binstring(t,l){if(l<65534&&(t.subarray&&STR_APPLY_UIA_OK||!t.subarray&&STR_APPLY_OK))return String.fromCharCode.apply(null,utils$h.shrinkBuf(t,l));for(var r="",E=0;E<l;E++)r+=String.fromCharCode(t[E]);return r}strings$2.buf2binstring=function(t){return buf2binstring(t,t.length)},strings$2.binstring2buf=function(t){for(var l=new utils$h.Buf8(t.length),r=0,E=l.length;r<E;r++)l[r]=t.charCodeAt(r);return l},strings$2.buf2string=function(t,l){var r,E,T,A,N=l||t.length,y=new Array(N*2);for(E=0,r=0;r<N;){if(T=t[r++],T<128){y[E++]=T;continue}if(A=_utf8len[T],A>4){y[E++]=65533,r+=A-1;continue}for(T&=A===2?31:A===3?15:7;A>1&&r<N;)T=T<<6|t[r++]&63,A--;if(A>1){y[E++]=65533;continue}T<65536?y[E++]=T:(T-=65536,y[E++]=55296|T>>10&1023,y[E++]=56320|T&1023)}return buf2binstring(y,E)},strings$2.utf8border=function(t,l){var r;for(l=l||t.length,l>t.length&&(l=t.length),r=l-1;r>=0&&(t[r]&192)===128;)r--;return r<0||r===0?l:r+_utf8len[t[r]]>l?r:l};function ZStream$2(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}var zstream=ZStream$2,zlib_deflate=deflate$3,utils$g=common,strings$1=strings$2,msg$1=messages,ZStream$1=zstream,toString$1=Object.prototype.toString,Z_NO_FLUSH=0,Z_FINISH$1=4,Z_OK$1=0,Z_STREAM_END$1=1,Z_SYNC_FLUSH=2,Z_DEFAULT_COMPRESSION=-1,Z_DEFAULT_STRATEGY=0,Z_DEFLATED$1=8;function Deflate(t){if(!(this instanceof Deflate))return new Deflate(t);this.options=utils$g.assign({level:Z_DEFAULT_COMPRESSION,method:Z_DEFLATED$1,chunkSize:16384,windowBits:15,memLevel:8,strategy:Z_DEFAULT_STRATEGY,to:""},t||{});var l=this.options;l.raw&&l.windowBits>0?l.windowBits=-l.windowBits:l.gzip&&l.windowBits>0&&l.windowBits<16&&(l.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new ZStream$1,this.strm.avail_out=0;var r=zlib_deflate.deflateInit2(this.strm,l.level,l.method,l.windowBits,l.memLevel,l.strategy);if(r!==Z_OK$1)throw new Error(msg$1[r]);if(l.header&&zlib_deflate.deflateSetHeader(this.strm,l.header),l.dictionary){var E;if(typeof l.dictionary=="string"?E=strings$1.string2buf(l.dictionary):toString$1.call(l.dictionary)==="[object ArrayBuffer]"?E=new Uint8Array(l.dictionary):E=l.dictionary,r=zlib_deflate.deflateSetDictionary(this.strm,E),r!==Z_OK$1)throw new Error(msg$1[r]);this._dict_set=!0}}Deflate.prototype.push=function(t,l){var r=this.strm,E=this.options.chunkSize,T,A;if(this.ended)return!1;A=l===~~l?l:l===!0?Z_FINISH$1:Z_NO_FLUSH,typeof t=="string"?r.input=strings$1.string2buf(t):toString$1.call(t)==="[object ArrayBuffer]"?r.input=new Uint8Array(t):r.input=t,r.next_in=0,r.avail_in=r.input.length;do{if(r.avail_out===0&&(r.output=new utils$g.Buf8(E),r.next_out=0,r.avail_out=E),T=zlib_deflate.deflate(r,A),T!==Z_STREAM_END$1&&T!==Z_OK$1)return this.onEnd(T),this.ended=!0,!1;(r.avail_out===0||r.avail_in===0&&(A===Z_FINISH$1||A===Z_SYNC_FLUSH))&&(this.options.to==="string"?this.onData(strings$1.buf2binstring(utils$g.shrinkBuf(r.output,r.next_out))):this.onData(utils$g.shrinkBuf(r.output,r.next_out)))}while((r.avail_in>0||r.avail_out===0)&&T!==Z_STREAM_END$1);return A===Z_FINISH$1?(T=zlib_deflate.deflateEnd(this.strm),this.onEnd(T),this.ended=!0,T===Z_OK$1):(A===Z_SYNC_FLUSH&&(this.onEnd(Z_OK$1),r.avail_out=0),!0)},Deflate.prototype.onData=function(t){this.chunks.push(t)},Deflate.prototype.onEnd=function(t){t===Z_OK$1&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=utils$g.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};function deflate$1(t,l){var r=new Deflate(l);if(r.push(t,!0),r.err)throw r.msg||msg$1[r.err];return r.result}function deflateRaw(t,l){return l=l||{},l.raw=!0,deflate$1(t,l)}function gzip(t,l){return l=l||{},l.gzip=!0,deflate$1(t,l)}deflate$4.Deflate=Deflate,deflate$4.deflate=deflate$1,deflate$4.deflateRaw=deflateRaw,deflate$4.gzip=gzip;var inflate$4={},inflate$3={},BAD$1=30,TYPE$1=12,inffast=function(l,r){var E,T,A,N,y,ue,Z,M,Se,ge,_e,we,be,Te,Ie,$e,De,Ce,ke,Fe,Oe,Be,Ee,Pe,Re;E=l.state,T=l.next_in,Pe=l.input,A=T+(l.avail_in-5),N=l.next_out,Re=l.output,y=N-(r-l.avail_out),ue=N+(l.avail_out-257),Z=E.dmax,M=E.wsize,Se=E.whave,ge=E.wnext,_e=E.window,we=E.hold,be=E.bits,Te=E.lencode,Ie=E.distcode,$e=(1<<E.lenbits)-1,De=(1<<E.distbits)-1;e:do{be<15&&(we+=Pe[T++]<<be,be+=8,we+=Pe[T++]<<be,be+=8),Ce=Te[we&$e];t:for(;;){if(ke=Ce>>>24,we>>>=ke,be-=ke,ke=Ce>>>16&255,ke===0)Re[N++]=Ce&65535;else if(ke&16){Fe=Ce&65535,ke&=15,ke&&(be<ke&&(we+=Pe[T++]<<be,be+=8),Fe+=we&(1<<ke)-1,we>>>=ke,be-=ke),be<15&&(we+=Pe[T++]<<be,be+=8,we+=Pe[T++]<<be,be+=8),Ce=Ie[we&De];r:for(;;){if(ke=Ce>>>24,we>>>=ke,be-=ke,ke=Ce>>>16&255,ke&16){if(Oe=Ce&65535,ke&=15,be<ke&&(we+=Pe[T++]<<be,be+=8,be<ke&&(we+=Pe[T++]<<be,be+=8)),Oe+=we&(1<<ke)-1,Oe>Z){l.msg="invalid distance too far back",E.mode=BAD$1;break e}if(we>>>=ke,be-=ke,ke=N-y,Oe>ke){if(ke=Oe-ke,ke>Se&&E.sane){l.msg="invalid distance too far back",E.mode=BAD$1;break e}if(Be=0,Ee=_e,ge===0){if(Be+=M-ke,ke<Fe){Fe-=ke;do Re[N++]=_e[Be++];while(--ke);Be=N-Oe,Ee=Re}}else if(ge<ke){if(Be+=M+ge-ke,ke-=ge,ke<Fe){Fe-=ke;do Re[N++]=_e[Be++];while(--ke);if(Be=0,ge<Fe){ke=ge,Fe-=ke;do Re[N++]=_e[Be++];while(--ke);Be=N-Oe,Ee=Re}}}else if(Be+=ge-ke,ke<Fe){Fe-=ke;do Re[N++]=_e[Be++];while(--ke);Be=N-Oe,Ee=Re}for(;Fe>2;)Re[N++]=Ee[Be++],Re[N++]=Ee[Be++],Re[N++]=Ee[Be++],Fe-=3;Fe&&(Re[N++]=Ee[Be++],Fe>1&&(Re[N++]=Ee[Be++]))}else{Be=N-Oe;do Re[N++]=Re[Be++],Re[N++]=Re[Be++],Re[N++]=Re[Be++],Fe-=3;while(Fe>2);Fe&&(Re[N++]=Re[Be++],Fe>1&&(Re[N++]=Re[Be++]))}}else if((ke&64)===0){Ce=Ie[(Ce&65535)+(we&(1<<ke)-1)];continue r}else{l.msg="invalid distance code",E.mode=BAD$1;break e}break}}else if((ke&64)===0){Ce=Te[(Ce&65535)+(we&(1<<ke)-1)];continue t}else if(ke&32){E.mode=TYPE$1;break e}else{l.msg="invalid literal/length code",E.mode=BAD$1;break e}break}}while(T<A&&N<ue);Fe=be>>3,T-=Fe,be-=Fe<<3,we&=(1<<be)-1,l.next_in=T,l.next_out=N,l.avail_in=T<A?5+(A-T):5-(T-A),l.avail_out=N<ue?257+(ue-N):257-(N-ue),E.hold=we,E.bits=be},utils$f=common,MAXBITS=15,ENOUGH_LENS$1=852,ENOUGH_DISTS$1=592,CODES$1=0,LENS$1=1,DISTS$1=2,lbase=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],lext=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],dbase=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],dext=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64],inftrees=function(l,r,E,T,A,N,y,ue){var Z=ue.bits,M=0,Se=0,ge=0,_e=0,we=0,be=0,Te=0,Ie=0,$e=0,De=0,Ce,ke,Fe,Oe,Be,Ee=null,Pe=0,Re,Le=new utils$f.Buf16(MAXBITS+1),Ve=new utils$f.Buf16(MAXBITS+1),Qe=null,tt=0,at,Me,nt;for(M=0;M<=MAXBITS;M++)Le[M]=0;for(Se=0;Se<T;Se++)Le[r[E+Se]]++;for(we=Z,_e=MAXBITS;_e>=1&&Le[_e]===0;_e--);if(we>_e&&(we=_e),_e===0)return A[N++]=1<<24|64<<16|0,A[N++]=1<<24|64<<16|0,ue.bits=1,0;for(ge=1;ge<_e&&Le[ge]===0;ge++);for(we<ge&&(we=ge),Ie=1,M=1;M<=MAXBITS;M++)if(Ie<<=1,Ie-=Le[M],Ie<0)return-1;if(Ie>0&&(l===CODES$1||_e!==1))return-1;for(Ve[1]=0,M=1;M<MAXBITS;M++)Ve[M+1]=Ve[M]+Le[M];for(Se=0;Se<T;Se++)r[E+Se]!==0&&(y[Ve[r[E+Se]]++]=Se);if(l===CODES$1?(Ee=Qe=y,Re=19):l===LENS$1?(Ee=lbase,Pe-=257,Qe=lext,tt-=257,Re=256):(Ee=dbase,Qe=dext,Re=-1),De=0,Se=0,M=ge,Be=N,be=we,Te=0,Fe=-1,$e=1<<we,Oe=$e-1,l===LENS$1&&$e>ENOUGH_LENS$1||l===DISTS$1&&$e>ENOUGH_DISTS$1)return 1;for(;;){at=M-Te,y[Se]<Re?(Me=0,nt=y[Se]):y[Se]>Re?(Me=Qe[tt+y[Se]],nt=Ee[Pe+y[Se]]):(Me=96,nt=0),Ce=1<<M-Te,ke=1<<be,ge=ke;do ke-=Ce,A[Be+(De>>Te)+ke]=at<<24|Me<<16|nt|0;while(ke!==0);for(Ce=1<<M-1;De&Ce;)Ce>>=1;if(Ce!==0?(De&=Ce-1,De+=Ce):De=0,Se++,--Le[M]===0){if(M===_e)break;M=r[E+y[Se]]}if(M>we&&(De&Oe)!==Fe){for(Te===0&&(Te=we),Be+=ge,be=M-Te,Ie=1<<be;be+Te<_e&&(Ie-=Le[be+Te],!(Ie<=0));)be++,Ie<<=1;if($e+=1<<be,l===LENS$1&&$e>ENOUGH_LENS$1||l===DISTS$1&&$e>ENOUGH_DISTS$1)return 1;Fe=De&Oe,A[Fe]=we<<24|be<<16|Be-N|0}}return De!==0&&(A[Be+De]=M-Te<<24|64<<16|0),ue.bits=we,0},utils$e=common,adler32=adler32_1,crc32$1=crc32_1,inflate_fast=inffast,inflate_table=inftrees,CODES=0,LENS=1,DISTS=2,Z_FINISH=4,Z_BLOCK=5,Z_TREES=6,Z_OK=0,Z_STREAM_END=1,Z_NEED_DICT=2,Z_STREAM_ERROR=-2,Z_DATA_ERROR=-3,Z_MEM_ERROR=-4,Z_BUF_ERROR=-5,Z_DEFLATED=8,HEAD=1,FLAGS=2,TIME=3,OS=4,EXLEN=5,EXTRA=6,NAME=7,COMMENT=8,HCRC=9,DICTID=10,DICT=11,TYPE=12,TYPEDO=13,STORED=14,COPY_=15,COPY=16,TABLE=17,LENLENS=18,CODELENS=19,LEN_=20,LEN=21,LENEXT=22,DIST=23,DISTEXT=24,MATCH=25,LIT=26,CHECK=27,LENGTH=28,DONE=29,BAD=30,MEM=31,SYNC=32,ENOUGH_LENS=852,ENOUGH_DISTS=592,MAX_WBITS=15,DEF_WBITS=MAX_WBITS;function zswap32(t){return(t>>>24&255)+(t>>>8&65280)+((t&65280)<<8)+((t&255)<<24)}function InflateState(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new utils$e.Buf16(320),this.work=new utils$e.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function inflateResetKeep(t){var l;return!t||!t.state?Z_STREAM_ERROR:(l=t.state,t.total_in=t.total_out=l.total=0,t.msg="",l.wrap&&(t.adler=l.wrap&1),l.mode=HEAD,l.last=0,l.havedict=0,l.dmax=32768,l.head=null,l.hold=0,l.bits=0,l.lencode=l.lendyn=new utils$e.Buf32(ENOUGH_LENS),l.distcode=l.distdyn=new utils$e.Buf32(ENOUGH_DISTS),l.sane=1,l.back=-1,Z_OK)}function inflateReset(t){var l;return!t||!t.state?Z_STREAM_ERROR:(l=t.state,l.wsize=0,l.whave=0,l.wnext=0,inflateResetKeep(t))}function inflateReset2(t,l){var r,E;return!t||!t.state||(E=t.state,l<0?(r=0,l=-l):(r=(l>>4)+1,l<48&&(l&=15)),l&&(l<8||l>15))?Z_STREAM_ERROR:(E.window!==null&&E.wbits!==l&&(E.window=null),E.wrap=r,E.wbits=l,inflateReset(t))}function inflateInit2(t,l){var r,E;return t?(E=new InflateState,t.state=E,E.window=null,r=inflateReset2(t,l),r!==Z_OK&&(t.state=null),r):Z_STREAM_ERROR}function inflateInit(t){return inflateInit2(t,DEF_WBITS)}var virgin=!0,lenfix,distfix;function fixedtables(t){if(virgin){var l;for(lenfix=new utils$e.Buf32(512),distfix=new utils$e.Buf32(32),l=0;l<144;)t.lens[l++]=8;for(;l<256;)t.lens[l++]=9;for(;l<280;)t.lens[l++]=7;for(;l<288;)t.lens[l++]=8;for(inflate_table(LENS,t.lens,0,288,lenfix,0,t.work,{bits:9}),l=0;l<32;)t.lens[l++]=5;inflate_table(DISTS,t.lens,0,32,distfix,0,t.work,{bits:5}),virgin=!1}t.lencode=lenfix,t.lenbits=9,t.distcode=distfix,t.distbits=5}function updatewindow(t,l,r,E){var T,A=t.state;return A.window===null&&(A.wsize=1<<A.wbits,A.wnext=0,A.whave=0,A.window=new utils$e.Buf8(A.wsize)),E>=A.wsize?(utils$e.arraySet(A.window,l,r-A.wsize,A.wsize,0),A.wnext=0,A.whave=A.wsize):(T=A.wsize-A.wnext,T>E&&(T=E),utils$e.arraySet(A.window,l,r-E,T,A.wnext),E-=T,E?(utils$e.arraySet(A.window,l,r-E,E,0),A.wnext=E,A.whave=A.wsize):(A.wnext+=T,A.wnext===A.wsize&&(A.wnext=0),A.whave<A.wsize&&(A.whave+=T))),0}function inflate$2(t,l){var r,E,T,A,N,y,ue,Z,M,Se,ge,_e,we,be,Te=0,Ie,$e,De,Ce,ke,Fe,Oe,Be,Ee=new utils$e.Buf8(4),Pe,Re,Le=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&t.avail_in!==0)return Z_STREAM_ERROR;r=t.state,r.mode===TYPE&&(r.mode=TYPEDO),N=t.next_out,T=t.output,ue=t.avail_out,A=t.next_in,E=t.input,y=t.avail_in,Z=r.hold,M=r.bits,Se=y,ge=ue,Be=Z_OK;e:for(;;)switch(r.mode){case HEAD:if(r.wrap===0){r.mode=TYPEDO;break}for(;M<16;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if(r.wrap&2&&Z===35615){r.check=0,Ee[0]=Z&255,Ee[1]=Z>>>8&255,r.check=crc32$1(r.check,Ee,2,0),Z=0,M=0,r.mode=FLAGS;break}if(r.flags=0,r.head&&(r.head.done=!1),!(r.wrap&1)||(((Z&255)<<8)+(Z>>8))%31){t.msg="incorrect header check",r.mode=BAD;break}if((Z&15)!==Z_DEFLATED){t.msg="unknown compression method",r.mode=BAD;break}if(Z>>>=4,M-=4,Oe=(Z&15)+8,r.wbits===0)r.wbits=Oe;else if(Oe>r.wbits){t.msg="invalid window size",r.mode=BAD;break}r.dmax=1<<Oe,t.adler=r.check=1,r.mode=Z&512?DICTID:TYPE,Z=0,M=0;break;case FLAGS:for(;M<16;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if(r.flags=Z,(r.flags&255)!==Z_DEFLATED){t.msg="unknown compression method",r.mode=BAD;break}if(r.flags&57344){t.msg="unknown header flags set",r.mode=BAD;break}r.head&&(r.head.text=Z>>8&1),r.flags&512&&(Ee[0]=Z&255,Ee[1]=Z>>>8&255,r.check=crc32$1(r.check,Ee,2,0)),Z=0,M=0,r.mode=TIME;case TIME:for(;M<32;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}r.head&&(r.head.time=Z),r.flags&512&&(Ee[0]=Z&255,Ee[1]=Z>>>8&255,Ee[2]=Z>>>16&255,Ee[3]=Z>>>24&255,r.check=crc32$1(r.check,Ee,4,0)),Z=0,M=0,r.mode=OS;case OS:for(;M<16;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}r.head&&(r.head.xflags=Z&255,r.head.os=Z>>8),r.flags&512&&(Ee[0]=Z&255,Ee[1]=Z>>>8&255,r.check=crc32$1(r.check,Ee,2,0)),Z=0,M=0,r.mode=EXLEN;case EXLEN:if(r.flags&1024){for(;M<16;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}r.length=Z,r.head&&(r.head.extra_len=Z),r.flags&512&&(Ee[0]=Z&255,Ee[1]=Z>>>8&255,r.check=crc32$1(r.check,Ee,2,0)),Z=0,M=0}else r.head&&(r.head.extra=null);r.mode=EXTRA;case EXTRA:if(r.flags&1024&&(_e=r.length,_e>y&&(_e=y),_e&&(r.head&&(Oe=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),utils$e.arraySet(r.head.extra,E,A,_e,Oe)),r.flags&512&&(r.check=crc32$1(r.check,E,_e,A)),y-=_e,A+=_e,r.length-=_e),r.length))break e;r.length=0,r.mode=NAME;case NAME:if(r.flags&2048){if(y===0)break e;_e=0;do Oe=E[A+_e++],r.head&&Oe&&r.length<65536&&(r.head.name+=String.fromCharCode(Oe));while(Oe&&_e<y);if(r.flags&512&&(r.check=crc32$1(r.check,E,_e,A)),y-=_e,A+=_e,Oe)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=COMMENT;case COMMENT:if(r.flags&4096){if(y===0)break e;_e=0;do Oe=E[A+_e++],r.head&&Oe&&r.length<65536&&(r.head.comment+=String.fromCharCode(Oe));while(Oe&&_e<y);if(r.flags&512&&(r.check=crc32$1(r.check,E,_e,A)),y-=_e,A+=_e,Oe)break e}else r.head&&(r.head.comment=null);r.mode=HCRC;case HCRC:if(r.flags&512){for(;M<16;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if(Z!==(r.check&65535)){t.msg="header crc mismatch",r.mode=BAD;break}Z=0,M=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),t.adler=r.check=0,r.mode=TYPE;break;case DICTID:for(;M<32;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}t.adler=r.check=zswap32(Z),Z=0,M=0,r.mode=DICT;case DICT:if(r.havedict===0)return t.next_out=N,t.avail_out=ue,t.next_in=A,t.avail_in=y,r.hold=Z,r.bits=M,Z_NEED_DICT;t.adler=r.check=1,r.mode=TYPE;case TYPE:if(l===Z_BLOCK||l===Z_TREES)break e;case TYPEDO:if(r.last){Z>>>=M&7,M-=M&7,r.mode=CHECK;break}for(;M<3;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}switch(r.last=Z&1,Z>>>=1,M-=1,Z&3){case 0:r.mode=STORED;break;case 1:if(fixedtables(r),r.mode=LEN_,l===Z_TREES){Z>>>=2,M-=2;break e}break;case 2:r.mode=TABLE;break;case 3:t.msg="invalid block type",r.mode=BAD}Z>>>=2,M-=2;break;case STORED:for(Z>>>=M&7,M-=M&7;M<32;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if((Z&65535)!==(Z>>>16^65535)){t.msg="invalid stored block lengths",r.mode=BAD;break}if(r.length=Z&65535,Z=0,M=0,r.mode=COPY_,l===Z_TREES)break e;case COPY_:r.mode=COPY;case COPY:if(_e=r.length,_e){if(_e>y&&(_e=y),_e>ue&&(_e=ue),_e===0)break e;utils$e.arraySet(T,E,A,_e,N),y-=_e,A+=_e,ue-=_e,N+=_e,r.length-=_e;break}r.mode=TYPE;break;case TABLE:for(;M<14;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if(r.nlen=(Z&31)+257,Z>>>=5,M-=5,r.ndist=(Z&31)+1,Z>>>=5,M-=5,r.ncode=(Z&15)+4,Z>>>=4,M-=4,r.nlen>286||r.ndist>30){t.msg="too many length or distance symbols",r.mode=BAD;break}r.have=0,r.mode=LENLENS;case LENLENS:for(;r.have<r.ncode;){for(;M<3;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}r.lens[Le[r.have++]]=Z&7,Z>>>=3,M-=3}for(;r.have<19;)r.lens[Le[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,Pe={bits:r.lenbits},Be=inflate_table(CODES,r.lens,0,19,r.lencode,0,r.work,Pe),r.lenbits=Pe.bits,Be){t.msg="invalid code lengths set",r.mode=BAD;break}r.have=0,r.mode=CODELENS;case CODELENS:for(;r.have<r.nlen+r.ndist;){for(;Te=r.lencode[Z&(1<<r.lenbits)-1],Ie=Te>>>24,$e=Te>>>16&255,De=Te&65535,!(Ie<=M);){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if(De<16)Z>>>=Ie,M-=Ie,r.lens[r.have++]=De;else{if(De===16){for(Re=Ie+2;M<Re;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if(Z>>>=Ie,M-=Ie,r.have===0){t.msg="invalid bit length repeat",r.mode=BAD;break}Oe=r.lens[r.have-1],_e=3+(Z&3),Z>>>=2,M-=2}else if(De===17){for(Re=Ie+3;M<Re;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}Z>>>=Ie,M-=Ie,Oe=0,_e=3+(Z&7),Z>>>=3,M-=3}else{for(Re=Ie+7;M<Re;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}Z>>>=Ie,M-=Ie,Oe=0,_e=11+(Z&127),Z>>>=7,M-=7}if(r.have+_e>r.nlen+r.ndist){t.msg="invalid bit length repeat",r.mode=BAD;break}for(;_e--;)r.lens[r.have++]=Oe}}if(r.mode===BAD)break;if(r.lens[256]===0){t.msg="invalid code -- missing end-of-block",r.mode=BAD;break}if(r.lenbits=9,Pe={bits:r.lenbits},Be=inflate_table(LENS,r.lens,0,r.nlen,r.lencode,0,r.work,Pe),r.lenbits=Pe.bits,Be){t.msg="invalid literal/lengths set",r.mode=BAD;break}if(r.distbits=6,r.distcode=r.distdyn,Pe={bits:r.distbits},Be=inflate_table(DISTS,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,Pe),r.distbits=Pe.bits,Be){t.msg="invalid distances set",r.mode=BAD;break}if(r.mode=LEN_,l===Z_TREES)break e;case LEN_:r.mode=LEN;case LEN:if(y>=6&&ue>=258){t.next_out=N,t.avail_out=ue,t.next_in=A,t.avail_in=y,r.hold=Z,r.bits=M,inflate_fast(t,ge),N=t.next_out,T=t.output,ue=t.avail_out,A=t.next_in,E=t.input,y=t.avail_in,Z=r.hold,M=r.bits,r.mode===TYPE&&(r.back=-1);break}for(r.back=0;Te=r.lencode[Z&(1<<r.lenbits)-1],Ie=Te>>>24,$e=Te>>>16&255,De=Te&65535,!(Ie<=M);){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if($e&&($e&240)===0){for(Ce=Ie,ke=$e,Fe=De;Te=r.lencode[Fe+((Z&(1<<Ce+ke)-1)>>Ce)],Ie=Te>>>24,$e=Te>>>16&255,De=Te&65535,!(Ce+Ie<=M);){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}Z>>>=Ce,M-=Ce,r.back+=Ce}if(Z>>>=Ie,M-=Ie,r.back+=Ie,r.length=De,$e===0){r.mode=LIT;break}if($e&32){r.back=-1,r.mode=TYPE;break}if($e&64){t.msg="invalid literal/length code",r.mode=BAD;break}r.extra=$e&15,r.mode=LENEXT;case LENEXT:if(r.extra){for(Re=r.extra;M<Re;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}r.length+=Z&(1<<r.extra)-1,Z>>>=r.extra,M-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=DIST;case DIST:for(;Te=r.distcode[Z&(1<<r.distbits)-1],Ie=Te>>>24,$e=Te>>>16&255,De=Te&65535,!(Ie<=M);){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if(($e&240)===0){for(Ce=Ie,ke=$e,Fe=De;Te=r.distcode[Fe+((Z&(1<<Ce+ke)-1)>>Ce)],Ie=Te>>>24,$e=Te>>>16&255,De=Te&65535,!(Ce+Ie<=M);){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}Z>>>=Ce,M-=Ce,r.back+=Ce}if(Z>>>=Ie,M-=Ie,r.back+=Ie,$e&64){t.msg="invalid distance code",r.mode=BAD;break}r.offset=De,r.extra=$e&15,r.mode=DISTEXT;case DISTEXT:if(r.extra){for(Re=r.extra;M<Re;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}r.offset+=Z&(1<<r.extra)-1,Z>>>=r.extra,M-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){t.msg="invalid distance too far back",r.mode=BAD;break}r.mode=MATCH;case MATCH:if(ue===0)break e;if(_e=ge-ue,r.offset>_e){if(_e=r.offset-_e,_e>r.whave&&r.sane){t.msg="invalid distance too far back",r.mode=BAD;break}_e>r.wnext?(_e-=r.wnext,we=r.wsize-_e):we=r.wnext-_e,_e>r.length&&(_e=r.length),be=r.window}else be=T,we=N-r.offset,_e=r.length;_e>ue&&(_e=ue),ue-=_e,r.length-=_e;do T[N++]=be[we++];while(--_e);r.length===0&&(r.mode=LEN);break;case LIT:if(ue===0)break e;T[N++]=r.length,ue--,r.mode=LEN;break;case CHECK:if(r.wrap){for(;M<32;){if(y===0)break e;y--,Z|=E[A++]<<M,M+=8}if(ge-=ue,t.total_out+=ge,r.total+=ge,ge&&(t.adler=r.check=r.flags?crc32$1(r.check,T,ge,N-ge):adler32(r.check,T,ge,N-ge)),ge=ue,(r.flags?Z:zswap32(Z))!==r.check){t.msg="incorrect data check",r.mode=BAD;break}Z=0,M=0}r.mode=LENGTH;case LENGTH:if(r.wrap&&r.flags){for(;M<32;){if(y===0)break e;y--,Z+=E[A++]<<M,M+=8}if(Z!==(r.total&4294967295)){t.msg="incorrect length check",r.mode=BAD;break}Z=0,M=0}r.mode=DONE;case DONE:Be=Z_STREAM_END;break e;case BAD:Be=Z_DATA_ERROR;break e;case MEM:return Z_MEM_ERROR;case SYNC:default:return Z_STREAM_ERROR}return t.next_out=N,t.avail_out=ue,t.next_in=A,t.avail_in=y,r.hold=Z,r.bits=M,(r.wsize||ge!==t.avail_out&&r.mode<BAD&&(r.mode<CHECK||l!==Z_FINISH))&&updatewindow(t,t.output,t.next_out,ge-t.avail_out),Se-=t.avail_in,ge-=t.avail_out,t.total_in+=Se,t.total_out+=ge,r.total+=ge,r.wrap&&ge&&(t.adler=r.check=r.flags?crc32$1(r.check,T,ge,t.next_out-ge):adler32(r.check,T,ge,t.next_out-ge)),t.data_type=r.bits+(r.last?64:0)+(r.mode===TYPE?128:0)+(r.mode===LEN_||r.mode===COPY_?256:0),(Se===0&&ge===0||l===Z_FINISH)&&Be===Z_OK&&(Be=Z_BUF_ERROR),Be}function inflateEnd(t){if(!t||!t.state)return Z_STREAM_ERROR;var l=t.state;return l.window&&(l.window=null),t.state=null,Z_OK}function inflateGetHeader(t,l){var r;return!t||!t.state||(r=t.state,(r.wrap&2)===0)?Z_STREAM_ERROR:(r.head=l,l.done=!1,Z_OK)}function inflateSetDictionary(t,l){var r=l.length,E,T,A;return!t||!t.state||(E=t.state,E.wrap!==0&&E.mode!==DICT)?Z_STREAM_ERROR:E.mode===DICT&&(T=1,T=adler32(T,l,r,0),T!==E.check)?Z_DATA_ERROR:(A=updatewindow(t,l,r,r),A?(E.mode=MEM,Z_MEM_ERROR):(E.havedict=1,Z_OK))}inflate$3.inflateReset=inflateReset,inflate$3.inflateReset2=inflateReset2,inflate$3.inflateResetKeep=inflateResetKeep,inflate$3.inflateInit=inflateInit,inflate$3.inflateInit2=inflateInit2,inflate$3.inflate=inflate$2,inflate$3.inflateEnd=inflateEnd,inflate$3.inflateGetHeader=inflateGetHeader,inflate$3.inflateSetDictionary=inflateSetDictionary,inflate$3.inflateInfo="pako inflate (from Nodeca project)";var constants$1={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};function GZheader$1(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}var gzheader=GZheader$1,zlib_inflate=inflate$3,utils$d=common,strings=strings$2,c$3=constants$1,msg=messages,ZStream=zstream,GZheader=gzheader,toString=Object.prototype.toString;function Inflate(t){if(!(this instanceof Inflate))return new Inflate(t);this.options=utils$d.assign({chunkSize:16384,windowBits:0,to:""},t||{});var l=this.options;l.raw&&l.windowBits>=0&&l.windowBits<16&&(l.windowBits=-l.windowBits,l.windowBits===0&&(l.windowBits=-15)),l.windowBits>=0&&l.windowBits<16&&!(t&&t.windowBits)&&(l.windowBits+=32),l.windowBits>15&&l.windowBits<48&&(l.windowBits&15)===0&&(l.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new ZStream,this.strm.avail_out=0;var r=zlib_inflate.inflateInit2(this.strm,l.windowBits);if(r!==c$3.Z_OK)throw new Error(msg[r]);if(this.header=new GZheader,zlib_inflate.inflateGetHeader(this.strm,this.header),l.dictionary&&(typeof l.dictionary=="string"?l.dictionary=strings.string2buf(l.dictionary):toString.call(l.dictionary)==="[object ArrayBuffer]"&&(l.dictionary=new Uint8Array(l.dictionary)),l.raw&&(r=zlib_inflate.inflateSetDictionary(this.strm,l.dictionary),r!==c$3.Z_OK)))throw new Error(msg[r])}Inflate.prototype.push=function(t,l){var r=this.strm,E=this.options.chunkSize,T=this.options.dictionary,A,N,y,ue,Z,M=!1;if(this.ended)return!1;N=l===~~l?l:l===!0?c$3.Z_FINISH:c$3.Z_NO_FLUSH,typeof t=="string"?r.input=strings.binstring2buf(t):toString.call(t)==="[object ArrayBuffer]"?r.input=new Uint8Array(t):r.input=t,r.next_in=0,r.avail_in=r.input.length;do{if(r.avail_out===0&&(r.output=new utils$d.Buf8(E),r.next_out=0,r.avail_out=E),A=zlib_inflate.inflate(r,c$3.Z_NO_FLUSH),A===c$3.Z_NEED_DICT&&T&&(A=zlib_inflate.inflateSetDictionary(this.strm,T)),A===c$3.Z_BUF_ERROR&&M===!0&&(A=c$3.Z_OK,M=!1),A!==c$3.Z_STREAM_END&&A!==c$3.Z_OK)return this.onEnd(A),this.ended=!0,!1;r.next_out&&(r.avail_out===0||A===c$3.Z_STREAM_END||r.avail_in===0&&(N===c$3.Z_FINISH||N===c$3.Z_SYNC_FLUSH))&&(this.options.to==="string"?(y=strings.utf8border(r.output,r.next_out),ue=r.next_out-y,Z=strings.buf2string(r.output,y),r.next_out=ue,r.avail_out=E-ue,ue&&utils$d.arraySet(r.output,r.output,y,ue,0),this.onData(Z)):this.onData(utils$d.shrinkBuf(r.output,r.next_out))),r.avail_in===0&&r.avail_out===0&&(M=!0)}while((r.avail_in>0||r.avail_out===0)&&A!==c$3.Z_STREAM_END);return A===c$3.Z_STREAM_END&&(N=c$3.Z_FINISH),N===c$3.Z_FINISH?(A=zlib_inflate.inflateEnd(this.strm),this.onEnd(A),this.ended=!0,A===c$3.Z_OK):(N===c$3.Z_SYNC_FLUSH&&(this.onEnd(c$3.Z_OK),r.avail_out=0),!0)},Inflate.prototype.onData=function(t){this.chunks.push(t)},Inflate.prototype.onEnd=function(t){t===c$3.Z_OK&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=utils$d.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};function inflate$1(t,l){var r=new Inflate(l);if(r.push(t,!0),r.err)throw r.msg||msg[r.err];return r.result}function inflateRaw(t,l){return l=l||{},l.raw=!0,inflate$1(t,l)}inflate$4.Inflate=Inflate,inflate$4.inflate=inflate$1,inflate$4.inflateRaw=inflateRaw,inflate$4.ungzip=inflate$1;var assign=common.assign,deflate=deflate$4,inflate=inflate$4,constants=constants$1,pako$1={};assign(pako$1,deflate,inflate,constants);var pako_1=pako$1,USE_TYPEDARRAY=typeof Uint8Array!="undefined"&&typeof Uint16Array!="undefined"&&typeof Uint32Array!="undefined",pako=pako_1,utils$c=utils$r,GenericWorker$4=GenericWorker_1,ARRAY_TYPE=USE_TYPEDARRAY?"uint8array":"array";flate.magic="\b\0";function FlateWorker(t,l){GenericWorker$4.call(this,"FlateWorker/"+t),this._pako=null,this._pakoAction=t,this._pakoOptions=l,this.meta={}}utils$c.inherits(FlateWorker,GenericWorker$4),FlateWorker.prototype.processChunk=function(t){this.meta=t.meta,this._pako===null&&this._createPako(),this._pako.push(utils$c.transformTo(ARRAY_TYPE,t.data),!1)},FlateWorker.prototype.flush=function(){GenericWorker$4.prototype.flush.call(this),this._pako===null&&this._createPako(),this._pako.push([],!0)},FlateWorker.prototype.cleanUp=function(){GenericWorker$4.prototype.cleanUp.call(this),this._pako=null},FlateWorker.prototype._createPako=function(){this._pako=new pako[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var t=this;this._pako.onData=function(l){t.push({data:l,meta:t.meta})}},flate.compressWorker=function(t){return new FlateWorker("Deflate",t)},flate.uncompressWorker=function(){return new FlateWorker("Inflate",{})};var GenericWorker$3=GenericWorker_1;compressions$2.STORE={magic:"\0\0",compressWorker:function(){return new GenericWorker$3("STORE compression")},uncompressWorker:function(){return new GenericWorker$3("STORE decompression")}},compressions$2.DEFLATE=flate;var signature$1={};signature$1.LOCAL_FILE_HEADER="PK",signature$1.CENTRAL_FILE_HEADER="PK",signature$1.CENTRAL_DIRECTORY_END="PK",signature$1.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK\x07",signature$1.ZIP64_CENTRAL_DIRECTORY_END="PK",signature$1.DATA_DESCRIPTOR="PK\x07\b";var utils$b=utils$r,GenericWorker$2=GenericWorker_1,utf8$3=utf8$5,crc32=crc32_1$1,signature=signature$1,decToHex=function(t,l){var r="",E;for(E=0;E<l;E++)r+=String.fromCharCode(t&255),t=t>>>8;return r},generateUnixExternalFileAttr=function(t,l){var r=t;return t||(r=l?16893:33204),(r&65535)<<16},generateDosExternalFileAttr=function(t){return(t||0)&63},generateZipParts=function(t,l,r,E,T,A){var N=t.file,y=t.compression,ue=A!==utf8$3.utf8encode,Z=utils$b.transformTo("string",A(N.name)),M=utils$b.transformTo("string",utf8$3.utf8encode(N.name)),Se=N.comment,ge=utils$b.transformTo("string",A(Se)),_e=utils$b.transformTo("string",utf8$3.utf8encode(Se)),we=M.length!==N.name.length,be=_e.length!==Se.length,Te,Ie,$e="",De="",Ce="",ke=N.dir,Fe=N.date,Oe={crc32:0,compressedSize:0,uncompressedSize:0};(!l||r)&&(Oe.crc32=t.crc32,Oe.compressedSize=t.compressedSize,Oe.uncompressedSize=t.uncompressedSize);var Be=0;l&&(Be|=8),!ue&&(we||be)&&(Be|=2048);var Ee=0,Pe=0;ke&&(Ee|=16),T==="UNIX"?(Pe=798,Ee|=generateUnixExternalFileAttr(N.unixPermissions,ke)):(Pe=20,Ee|=generateDosExternalFileAttr(N.dosPermissions)),Te=Fe.getUTCHours(),Te=Te<<6,Te=Te|Fe.getUTCMinutes(),Te=Te<<5,Te=Te|Fe.getUTCSeconds()/2,Ie=Fe.getUTCFullYear()-1980,Ie=Ie<<4,Ie=Ie|Fe.getUTCMonth()+1,Ie=Ie<<5,Ie=Ie|Fe.getUTCDate(),we&&(De=decToHex(1,1)+decToHex(crc32(Z),4)+M,$e+="up"+decToHex(De.length,2)+De),be&&(Ce=decToHex(1,1)+decToHex(crc32(ge),4)+_e,$e+="uc"+decToHex(Ce.length,2)+Ce);var Re="";Re+=`
\0`,Re+=decToHex(Be,2),Re+=y.magic,Re+=decToHex(Te,2),Re+=decToHex(Ie,2),Re+=decToHex(Oe.crc32,4),Re+=decToHex(Oe.compressedSize,4),Re+=decToHex(Oe.uncompressedSize,4),Re+=decToHex(Z.length,2),Re+=decToHex($e.length,2);var Le=signature.LOCAL_FILE_HEADER+Re+Z+$e,Ve=signature.CENTRAL_FILE_HEADER+decToHex(Pe,2)+Re+decToHex(ge.length,2)+"\0\0\0\0"+decToHex(Ee,4)+decToHex(E,4)+Z+$e+ge;return{fileRecord:Le,dirRecord:Ve}},generateCentralDirectoryEnd=function(t,l,r,E,T){var A="",N=utils$b.transformTo("string",T(E));return A=signature.CENTRAL_DIRECTORY_END+"\0\0\0\0"+decToHex(t,2)+decToHex(t,2)+decToHex(l,4)+decToHex(r,4)+decToHex(N.length,2)+N,A},generateDataDescriptors=function(t){var l="";return l=signature.DATA_DESCRIPTOR+decToHex(t.crc32,4)+decToHex(t.compressedSize,4)+decToHex(t.uncompressedSize,4),l};function ZipFileWorker$1(t,l,r,E){GenericWorker$2.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=l,this.zipPlatform=r,this.encodeFileName=E,this.streamFiles=t,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}utils$b.inherits(ZipFileWorker$1,GenericWorker$2),ZipFileWorker$1.prototype.push=function(t){var l=t.meta.percent||0,r=this.entriesCount,E=this._sources.length;this.accumulate?this.contentBuffer.push(t):(this.bytesWritten+=t.data.length,GenericWorker$2.prototype.push.call(this,{data:t.data,meta:{currentFile:this.currentFile,percent:r?(l+100*(r-E-1))/r:100}}))},ZipFileWorker$1.prototype.openedSource=function(t){this.currentSourceOffset=this.bytesWritten,this.currentFile=t.file.name;var l=this.streamFiles&&!t.file.dir;if(l){var r=generateZipParts(t,l,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},ZipFileWorker$1.prototype.closedSource=function(t){this.accumulate=!1;var l=this.streamFiles&&!t.file.dir,r=generateZipParts(t,l,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(r.dirRecord),l)this.push({data:generateDataDescriptors(t),meta:{percent:100}});else for(this.push({data:r.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},ZipFileWorker$1.prototype.flush=function(){for(var t=this.bytesWritten,l=0;l<this.dirRecords.length;l++)this.push({data:this.dirRecords[l],meta:{percent:100}});var r=this.bytesWritten-t,E=generateCentralDirectoryEnd(this.dirRecords.length,r,t,this.zipComment,this.encodeFileName);this.push({data:E,meta:{percent:100}})},ZipFileWorker$1.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},ZipFileWorker$1.prototype.registerPrevious=function(t){this._sources.push(t);var l=this;return t.on("data",function(r){l.processChunk(r)}),t.on("end",function(){l.closedSource(l.previous.streamInfo),l._sources.length?l.prepareNextSource():l.end()}),t.on("error",function(r){l.error(r)}),this},ZipFileWorker$1.prototype.resume=function(){if(!GenericWorker$2.prototype.resume.call(this))return!1;if(!this.previous&&this._sources.length)return this.prepareNextSource(),!0;if(!this.previous&&!this._sources.length&&!this.generatedError)return this.end(),!0},ZipFileWorker$1.prototype.error=function(t){var l=this._sources;if(!GenericWorker$2.prototype.error.call(this,t))return!1;for(var r=0;r<l.length;r++)try{l[r].error(t)}catch(E){}return!0},ZipFileWorker$1.prototype.lock=function(){GenericWorker$2.prototype.lock.call(this);for(var t=this._sources,l=0;l<t.length;l++)t[l].lock()};var ZipFileWorker_1=ZipFileWorker$1,compressions$1=compressions$2,ZipFileWorker=ZipFileWorker_1,getCompression=function(t,l){var r=t||l,E=compressions$1[r];if(!E)throw new Error(r+" is not a valid compression method !");return E};generate$1.generateWorker=function(t,l,r){var E=new ZipFileWorker(l.streamFiles,r,l.platform,l.encodeFileName),T=0;try{t.forEach(function(A,N){T++;var y=getCompression(N.options.compression,l.compression),ue=N.options.compressionOptions||l.compressionOptions||{},Z=N.dir,M=N.date;N._compressWorker(y,ue).withStreamInfo("file",{name:A,dir:Z,date:M,comment:N.comment||"",unixPermissions:N.unixPermissions,dosPermissions:N.dosPermissions}).pipe(E)}),E.entriesCount=T}catch(A){E.error(A)}return E};var utils$a=utils$r,GenericWorker$1=GenericWorker_1;function NodejsStreamInputAdapter$1(t,l){GenericWorker$1.call(this,"Nodejs stream input adapter for "+t),this._upstreamEnded=!1,this._bindStream(l)}utils$a.inherits(NodejsStreamInputAdapter$1,GenericWorker$1),NodejsStreamInputAdapter$1.prototype._bindStream=function(t){var l=this;this._stream=t,t.pause(),t.on("data",function(r){l.push({data:r,meta:{percent:0}})}).on("error",function(r){l.isPaused?this.generatedError=r:l.error(r)}).on("end",function(){l.isPaused?l._upstreamEnded=!0:l.end()})},NodejsStreamInputAdapter$1.prototype.pause=function(){return GenericWorker$1.prototype.pause.call(this)?(this._stream.pause(),!0):!1},NodejsStreamInputAdapter$1.prototype.resume=function(){return GenericWorker$1.prototype.resume.call(this)?(this._upstreamEnded?this.end():this._stream.resume(),!0):!1};var NodejsStreamInputAdapter_1=NodejsStreamInputAdapter$1,utf8$2=utf8$5,utils$9=utils$r,GenericWorker=GenericWorker_1,StreamHelper=StreamHelper_1,defaults=defaults$1,CompressedObject$1=compressedObject,ZipObject=zipObject,generate=generate$1,nodejsUtils$1=nodejsUtils$2,NodejsStreamInputAdapter=NodejsStreamInputAdapter_1,fileAdd=function(t,l,r){var E=utils$9.getTypeOf(l),T,A=utils$9.extend(r||{},defaults);A.date=A.date||new Date,A.compression!==null&&(A.compression=A.compression.toUpperCase()),typeof A.unixPermissions=="string"&&(A.unixPermissions=parseInt(A.unixPermissions,8)),A.unixPermissions&&A.unixPermissions&16384&&(A.dir=!0),A.dosPermissions&&A.dosPermissions&16&&(A.dir=!0),A.dir&&(t=forceTrailingSlash(t)),A.createFolders&&(T=parentFolder(t))&&folderAdd.call(this,T,!0);var N=E==="string"&&A.binary===!1&&A.base64===!1;(!r||typeof r.binary=="undefined")&&(A.binary=!N);var y=l instanceof CompressedObject$1&&l.uncompressedSize===0;(y||A.dir||!l||l.length===0)&&(A.base64=!1,A.binary=!0,l="",A.compression="STORE",E="string");var ue=null;l instanceof CompressedObject$1||l instanceof GenericWorker?ue=l:nodejsUtils$1.isNode&&nodejsUtils$1.isStream(l)?ue=new NodejsStreamInputAdapter(t,l):ue=utils$9.prepareContent(t,l,A.binary,A.optimizedBinaryString,A.base64);var Z=new ZipObject(t,ue,A);this.files[t]=Z},parentFolder=function(t){t.slice(-1)==="/"&&(t=t.substring(0,t.length-1));var l=t.lastIndexOf("/");return l>0?t.substring(0,l):""},forceTrailingSlash=function(t){return t.slice(-1)!=="/"&&(t+="/"),t},folderAdd=function(t,l){return l=typeof l!="undefined"?l:defaults.createFolders,t=forceTrailingSlash(t),this.files[t]||fileAdd.call(this,t,null,{dir:!0,createFolders:l}),this.files[t]};function isRegExp(t){return Object.prototype.toString.call(t)==="[object RegExp]"}var out={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(t){var l,r,E;for(l in this.files)E=this.files[l],r=l.slice(this.root.length,l.length),r&&l.slice(0,this.root.length)===this.root&&t(r,E)},filter:function(t){var l=[];return this.forEach(function(r,E){t(r,E)&&l.push(E)}),l},file:function(t,l,r){if(arguments.length===1)if(isRegExp(t)){var E=t;return this.filter(function(A,N){return!N.dir&&E.test(A)})}else{var T=this.files[this.root+t];return T&&!T.dir?T:null}else t=this.root+t,fileAdd.call(this,t,l,r);return this},folder:function(t){if(!t)return this;if(isRegExp(t))return this.filter(function(T,A){return A.dir&&t.test(T)});var l=this.root+t,r=folderAdd.call(this,l),E=this.clone();return E.root=r.name,E},remove:function(t){t=this.root+t;var l=this.files[t];if(l||(t.slice(-1)!=="/"&&(t+="/"),l=this.files[t]),l&&!l.dir)delete this.files[t];else for(var r=this.filter(function(T,A){return A.name.slice(0,t.length)===t}),E=0;E<r.length;E++)delete this.files[r[E].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(t){var l,r={};try{if(r=utils$9.extend(t||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:utf8$2.utf8encode}),r.type=r.type.toLowerCase(),r.compression=r.compression.toUpperCase(),r.type==="binarystring"&&(r.type="string"),!r.type)throw new Error("No output type specified.");utils$9.checkSupport(r.type),(r.platform==="darwin"||r.platform==="freebsd"||r.platform==="linux"||r.platform==="sunos")&&(r.platform="UNIX"),r.platform==="win32"&&(r.platform="DOS");var E=r.comment||this.comment||"";l=generate.generateWorker(this,r,E)}catch(T){l=new GenericWorker("error"),l.error(T)}return new StreamHelper(l,r.type||"string",r.mimeType)},generateAsync:function(t,l){return this.generateInternalStream(t).accumulate(l)},generateNodeStream:function(t,l){return t=t||{},t.type||(t.type="nodebuffer"),this.generateInternalStream(t).toNodejsStream(l)}},object=out,utils$8=utils$r;function DataReader$2(t){this.data=t,this.length=t.length,this.index=0,this.zero=0}DataReader$2.prototype={checkOffset:function(t){this.checkIndex(this.index+t)},checkIndex:function(t){if(this.length<this.zero+t||t<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+t+"). Corrupted zip ?")},setIndex:function(t){this.checkIndex(t),this.index=t},skip:function(t){this.setIndex(this.index+t)},byteAt:function(){},readInt:function(t){var l=0,r;for(this.checkOffset(t),r=this.index+t-1;r>=this.index;r--)l=(l<<8)+this.byteAt(r);return this.index+=t,l},readString:function(t){return utils$8.transformTo("string",this.readData(t))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var t=this.readInt(4);return new Date(Date.UTC((t>>25&127)+1980,(t>>21&15)-1,t>>16&31,t>>11&31,t>>5&63,(t&31)<<1))}};var DataReader_1=DataReader$2,DataReader$1=DataReader_1,utils$7=utils$r;function ArrayReader$2(t){DataReader$1.call(this,t);for(var l=0;l<this.data.length;l++)t[l]=t[l]&255}utils$7.inherits(ArrayReader$2,DataReader$1),ArrayReader$2.prototype.byteAt=function(t){return this.data[this.zero+t]},ArrayReader$2.prototype.lastIndexOfSignature=function(t){for(var l=t.charCodeAt(0),r=t.charCodeAt(1),E=t.charCodeAt(2),T=t.charCodeAt(3),A=this.length-4;A>=0;--A)if(this.data[A]===l&&this.data[A+1]===r&&this.data[A+2]===E&&this.data[A+3]===T)return A-this.zero;return-1},ArrayReader$2.prototype.readAndCheckSignature=function(t){var l=t.charCodeAt(0),r=t.charCodeAt(1),E=t.charCodeAt(2),T=t.charCodeAt(3),A=this.readData(4);return l===A[0]&&r===A[1]&&E===A[2]&&T===A[3]},ArrayReader$2.prototype.readData=function(t){if(this.checkOffset(t),t===0)return[];var l=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,l};var ArrayReader_1=ArrayReader$2,DataReader=DataReader_1,utils$6=utils$r;function StringReader$1(t){DataReader.call(this,t)}utils$6.inherits(StringReader$1,DataReader),StringReader$1.prototype.byteAt=function(t){return this.data.charCodeAt(this.zero+t)},StringReader$1.prototype.lastIndexOfSignature=function(t){return this.data.lastIndexOf(t)-this.zero},StringReader$1.prototype.readAndCheckSignature=function(t){var l=this.readData(4);return t===l},StringReader$1.prototype.readData=function(t){this.checkOffset(t);var l=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,l};var StringReader_1=StringReader$1,ArrayReader$1=ArrayReader_1,utils$5=utils$r;function Uint8ArrayReader$2(t){ArrayReader$1.call(this,t)}utils$5.inherits(Uint8ArrayReader$2,ArrayReader$1),Uint8ArrayReader$2.prototype.readData=function(t){if(this.checkOffset(t),t===0)return new Uint8Array(0);var l=this.data.subarray(this.zero+this.index,this.zero+this.index+t);return this.index+=t,l};var Uint8ArrayReader_1=Uint8ArrayReader$2,Uint8ArrayReader$1=Uint8ArrayReader_1,utils$4=utils$r;function NodeBufferReader$1(t){Uint8ArrayReader$1.call(this,t)}utils$4.inherits(NodeBufferReader$1,Uint8ArrayReader$1),NodeBufferReader$1.prototype.readData=function(t){this.checkOffset(t);var l=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,l};var NodeBufferReader_1=NodeBufferReader$1,utils$3=utils$r,support$2=support$5,ArrayReader=ArrayReader_1,StringReader=StringReader_1,NodeBufferReader=NodeBufferReader_1,Uint8ArrayReader=Uint8ArrayReader_1,readerFor$2=function(t){var l=utils$3.getTypeOf(t);return utils$3.checkSupport(l),l==="string"&&!support$2.uint8array?new StringReader(t):l==="nodebuffer"?new NodeBufferReader(t):support$2.uint8array?new Uint8ArrayReader(utils$3.transformTo("uint8array",t)):new ArrayReader(utils$3.transformTo("array",t))},readerFor$1=readerFor$2,utils$2=utils$r,CompressedObject=compressedObject,crc32fn=crc32_1$1,utf8$1=utf8$5,compressions=compressions$2,support$1=support$5,MADE_BY_DOS=0,MADE_BY_UNIX=3,findCompression=function(t){for(var l in compressions)if(Object.prototype.hasOwnProperty.call(compressions,l)&&compressions[l].magic===t)return compressions[l];return null};function ZipEntry$1(t,l){this.options=t,this.loadOptions=l}ZipEntry$1.prototype={isEncrypted:function(){return(this.bitFlag&1)===1},useUTF8:function(){return(this.bitFlag&2048)===2048},readLocalPart:function(t){var l,r;if(t.skip(22),this.fileNameLength=t.readInt(2),r=t.readInt(2),this.fileName=t.readData(this.fileNameLength),t.skip(r),this.compressedSize===-1||this.uncompressedSize===-1)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(l=findCompression(this.compressionMethod),l===null)throw new Error("Corrupted zip : compression "+utils$2.pretty(this.compressionMethod)+" unknown (inner file : "+utils$2.transformTo("string",this.fileName)+")");this.decompressed=new CompressedObject(this.compressedSize,this.uncompressedSize,this.crc32,l,t.readData(this.compressedSize))},readCentralPart:function(t){this.versionMadeBy=t.readInt(2),t.skip(2),this.bitFlag=t.readInt(2),this.compressionMethod=t.readString(2),this.date=t.readDate(),this.crc32=t.readInt(4),this.compressedSize=t.readInt(4),this.uncompressedSize=t.readInt(4);var l=t.readInt(2);if(this.extraFieldsLength=t.readInt(2),this.fileCommentLength=t.readInt(2),this.diskNumberStart=t.readInt(2),this.internalFileAttributes=t.readInt(2),this.externalFileAttributes=t.readInt(4),this.localHeaderOffset=t.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");t.skip(l),this.readExtraFields(t),this.parseZIP64ExtraField(t),this.fileComment=t.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var t=this.versionMadeBy>>8;this.dir=!!(this.externalFileAttributes&16),t===MADE_BY_DOS&&(this.dosPermissions=this.externalFileAttributes&63),t===MADE_BY_UNIX&&(this.unixPermissions=this.externalFileAttributes>>16&65535),!this.dir&&this.fileNameStr.slice(-1)==="/"&&(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var t=readerFor$1(this.extraFields[1].value);this.uncompressedSize===utils$2.MAX_VALUE_32BITS&&(this.uncompressedSize=t.readInt(8)),this.compressedSize===utils$2.MAX_VALUE_32BITS&&(this.compressedSize=t.readInt(8)),this.localHeaderOffset===utils$2.MAX_VALUE_32BITS&&(this.localHeaderOffset=t.readInt(8)),this.diskNumberStart===utils$2.MAX_VALUE_32BITS&&(this.diskNumberStart=t.readInt(4))}},readExtraFields:function(t){var l=t.index+this.extraFieldsLength,r,E,T;for(this.extraFields||(this.extraFields={});t.index+4<l;)r=t.readInt(2),E=t.readInt(2),T=t.readData(E),this.extraFields[r]={id:r,length:E,value:T};t.setIndex(l)},handleUTF8:function(){var t=support$1.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=utf8$1.utf8decode(this.fileName),this.fileCommentStr=utf8$1.utf8decode(this.fileComment);else{var l=this.findExtraFieldUnicodePath();if(l!==null)this.fileNameStr=l;else{var r=utils$2.transformTo(t,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var E=this.findExtraFieldUnicodeComment();if(E!==null)this.fileCommentStr=E;else{var T=utils$2.transformTo(t,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(T)}}},findExtraFieldUnicodePath:function(){var t=this.extraFields[28789];if(t){var l=readerFor$1(t.value);return l.readInt(1)!==1||crc32fn(this.fileName)!==l.readInt(4)?null:utf8$1.utf8decode(l.readData(t.length-5))}return null},findExtraFieldUnicodeComment:function(){var t=this.extraFields[25461];if(t){var l=readerFor$1(t.value);return l.readInt(1)!==1||crc32fn(this.fileComment)!==l.readInt(4)?null:utf8$1.utf8decode(l.readData(t.length-5))}return null}};var zipEntry=ZipEntry$1,readerFor=readerFor$2,utils$1=utils$r,sig=signature$1,ZipEntry=zipEntry,support=support$5;function ZipEntries$1(t){this.files=[],this.loadOptions=t}ZipEntries$1.prototype={checkSignature:function(t){if(!this.reader.readAndCheckSignature(t)){this.reader.index-=4;var l=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+utils$1.pretty(l)+", expected "+utils$1.pretty(t)+")")}},isSignature:function(t,l){var r=this.reader.index;this.reader.setIndex(t);var E=this.reader.readString(4),T=E===l;return this.reader.setIndex(r),T},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var t=this.reader.readData(this.zipCommentLength),l=support.uint8array?"uint8array":"array",r=utils$1.transformTo(l,t);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var t=this.zip64EndOfCentralSize-44,l=0,r,E,T;l<t;)r=this.reader.readInt(2),E=this.reader.readInt(4),T=this.reader.readData(E),this.zip64ExtensibleData[r]={id:r,length:E,value:T}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),this.disksCount>1)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var t,l;for(t=0;t<this.files.length;t++)l=this.files[t],this.reader.setIndex(l.localHeaderOffset),this.checkSignature(sig.LOCAL_FILE_HEADER),l.readLocalPart(this.reader),l.handleUTF8(),l.processAttributes()},readCentralDir:function(){var t;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(sig.CENTRAL_FILE_HEADER);)t=new ZipEntry({zip64:this.zip64},this.loadOptions),t.readCentralPart(this.reader),this.files.push(t);if(this.centralDirRecords!==this.files.length&&this.centralDirRecords!==0&&this.files.length===0)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var t=this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);if(t<0){var l=!this.isSignature(0,sig.LOCAL_FILE_HEADER);throw l?new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html"):new Error("Corrupted zip: can't find end of central directory")}this.reader.setIndex(t);var r=t;if(this.checkSignature(sig.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===utils$1.MAX_VALUE_16BITS||this.diskWithCentralDirStart===utils$1.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===utils$1.MAX_VALUE_16BITS||this.centralDirRecords===utils$1.MAX_VALUE_16BITS||this.centralDirSize===utils$1.MAX_VALUE_32BITS||this.centralDirOffset===utils$1.MAX_VALUE_32BITS){if(this.zip64=!0,t=this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR),t<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(t),this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,sig.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var E=this.centralDirOffset+this.centralDirSize;this.zip64&&(E+=20,E+=12+this.zip64EndOfCentralSize);var T=r-E;if(T>0)this.isSignature(r,sig.CENTRAL_FILE_HEADER)||(this.reader.zero=T);else if(T<0)throw new Error("Corrupted zip: missing "+Math.abs(T)+" bytes.")},prepareReader:function(t){this.reader=readerFor(t)},load:function(t){this.prepareReader(t),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}};var zipEntries=ZipEntries$1,utils=utils$r,external=external$3,utf8=utf8$5,ZipEntries=zipEntries,Crc32Probe=Crc32Probe_1,nodejsUtils=nodejsUtils$2;function checkEntryCRC32(t){return new external.Promise(function(l,r){var E=t.decompressed.getContentWorker().pipe(new Crc32Probe);E.on("error",function(T){r(T)}).on("end",function(){E.streamInfo.crc32!==t.decompressed.crc32?r(new Error("Corrupted zip : CRC32 mismatch")):l()}).resume()})}var load=function(t,l){var r=this;return l=utils.extend(l||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:utf8.utf8decode}),nodejsUtils.isNode&&nodejsUtils.isStream(t)?external.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):utils.prepareContent("the loaded zip file",t,!0,l.optimizedBinaryString,l.base64).then(function(E){var T=new ZipEntries(l);return T.load(E),T}).then(function(T){var A=[external.Promise.resolve(T)],N=T.files;if(l.checkCRC32)for(var y=0;y<N.length;y++)A.push(checkEntryCRC32(N[y]));return external.Promise.all(A)}).then(function(T){for(var A=T.shift(),N=A.files,y=0;y<N.length;y++){var ue=N[y],Z=ue.fileNameStr,M=utils.resolve(ue.fileNameStr);r.file(M,ue.decompressed,{binary:!0,optimizedBinaryString:!0,date:ue.date,dir:ue.dir,comment:ue.fileCommentStr.length?ue.fileCommentStr:null,unixPermissions:ue.unixPermissions,dosPermissions:ue.dosPermissions,createFolders:l.createFolders}),ue.dir||(r.file(M).unsafeOriginalName=Z)}return A.zipComment.length&&(r.comment=A.zipComment),r})};function JSZip(){if(!(this instanceof JSZip))return new JSZip;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var t=new JSZip;for(var l in this)typeof this[l]!="function"&&(t[l]=this[l]);return t}}JSZip.prototype=object,JSZip.prototype.loadAsync=load,JSZip.support=support$5,JSZip.defaults=defaults$1,JSZip.version="3.10.1",JSZip.loadAsync=function(t,l){return new JSZip().loadAsync(t,l)},JSZip.external=external$3;var lib=JSZip;const n$1=t=>lib.loadAsync(t),s$1=(t,l)=>{const r=t.file(l);return r?r.async("text"):null},o=(t,l,r)=>t.file(l,r,{binary:!1}),p$1=t=>t.generateAsync({type:"uint8array",compression:"DEFLATE",compressionOptions:{level:1}});var sax={},string_decoder={},safeBuffer={exports:{}};/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */(function(t,l){var r=buffer$1,E=r.Buffer;function T(N,y){for(var ue in N)y[ue]=N[ue]}E.from&&E.alloc&&E.allocUnsafe&&E.allocUnsafeSlow?t.exports=r:(T(r,l),l.Buffer=A);function A(N,y,ue){return E(N,y,ue)}A.prototype=Object.create(E.prototype),T(E,A),A.from=function(N,y,ue){if(typeof N=="number")throw new TypeError("Argument must not be a number");return E(N,y,ue)},A.alloc=function(N,y,ue){if(typeof N!="number")throw new TypeError("Argument must be a number");var Z=E(N);return y!==void 0?typeof ue=="string"?Z.fill(y,ue):Z.fill(y):Z.fill(0),Z},A.allocUnsafe=function(N){if(typeof N!="number")throw new TypeError("Argument must be a number");return E(N)},A.allocUnsafeSlow=function(N){if(typeof N!="number")throw new TypeError("Argument must be a number");return r.SlowBuffer(N)}})(safeBuffer,safeBuffer.exports);var Buffer$1=safeBuffer.exports.Buffer,isEncoding=Buffer$1.isEncoding||function(t){switch(t=""+t,t&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function _normalizeEncoding(t){if(!t)return"utf8";for(var l;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(l)return;t=(""+t).toLowerCase(),l=!0}}function normalizeEncoding(t){var l=_normalizeEncoding(t);if(typeof l!="string"&&(Buffer$1.isEncoding===isEncoding||!isEncoding(t)))throw new Error("Unknown encoding: "+t);return l||t}string_decoder.StringDecoder=StringDecoder;function StringDecoder(t){this.encoding=normalizeEncoding(t);var l;switch(this.encoding){case"utf16le":this.text=utf16Text,this.end=utf16End,l=4;break;case"utf8":this.fillLast=utf8FillLast,l=4;break;case"base64":this.text=base64Text,this.end=base64End,l=3;break;default:this.write=simpleWrite,this.end=simpleEnd;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=Buffer$1.allocUnsafe(l)}StringDecoder.prototype.write=function(t){if(t.length===0)return"";var l,r;if(this.lastNeed){if(l=this.fillLast(t),l===void 0)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<t.length?l?l+this.text(t,r):this.text(t,r):l||""},StringDecoder.prototype.end=utf8End,StringDecoder.prototype.text=utf8Text,StringDecoder.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length};function utf8CheckByte(t){return t<=127?0:t>>5===6?2:t>>4===14?3:t>>3===30?4:t>>6===2?-1:-2}function utf8CheckIncomplete(t,l,r){var E=l.length-1;if(E<r)return 0;var T=utf8CheckByte(l[E]);return T>=0?(T>0&&(t.lastNeed=T-1),T):--E<r||T===-2?0:(T=utf8CheckByte(l[E]),T>=0?(T>0&&(t.lastNeed=T-2),T):--E<r||T===-2?0:(T=utf8CheckByte(l[E]),T>=0?(T>0&&(T===2?T=0:t.lastNeed=T-3),T):0))}function utf8CheckExtraBytes(t,l,r){if((l[0]&192)!==128)return t.lastNeed=0,"\uFFFD";if(t.lastNeed>1&&l.length>1){if((l[1]&192)!==128)return t.lastNeed=1,"\uFFFD";if(t.lastNeed>2&&l.length>2&&(l[2]&192)!==128)return t.lastNeed=2,"\uFFFD"}}function utf8FillLast(t){var l=this.lastTotal-this.lastNeed,r=utf8CheckExtraBytes(this,t);if(r!==void 0)return r;if(this.lastNeed<=t.length)return t.copy(this.lastChar,l,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,l,0,t.length),this.lastNeed-=t.length}function utf8Text(t,l){var r=utf8CheckIncomplete(this,t,l);if(!this.lastNeed)return t.toString("utf8",l);this.lastTotal=r;var E=t.length-(r-this.lastNeed);return t.copy(this.lastChar,0,E),t.toString("utf8",l,E)}function utf8End(t){var l=t&&t.length?this.write(t):"";return this.lastNeed?l+"\uFFFD":l}function utf16Text(t,l){if((t.length-l)%2===0){var r=t.toString("utf16le",l);if(r){var E=r.charCodeAt(r.length-1);if(E>=55296&&E<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",l,t.length-1)}function utf16End(t){var l=t&&t.length?this.write(t):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return l+this.lastChar.toString("utf16le",0,r)}return l}function base64Text(t,l){var r=(t.length-l)%3;return r===0?t.toString("base64",l):(this.lastNeed=3-r,this.lastTotal=3,r===1?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",l,t.length-r))}function base64End(t){var l=t&&t.length?this.write(t):"";return this.lastNeed?l+this.lastChar.toString("base64",0,3-this.lastNeed):l}function simpleWrite(t){return t.toString(this.encoding)}function simpleEnd(t){return t&&t.length?this.write(t):""}(function(t){(function(l){l.parser=function(ce,k){return new E(ce,k)},l.SAXParser=E,l.SAXStream=M,l.createStream=Z,l.MAX_BUFFER_LENGTH=64*1024;var r=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];l.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"];function E(ce,k){if(!(this instanceof E))return new E(ce,k);var Ne=this;A(Ne),Ne.q=Ne.c="",Ne.bufferCheckPosition=l.MAX_BUFFER_LENGTH,Ne.opt=k||{},Ne.opt.lowercase=Ne.opt.lowercase||Ne.opt.lowercasetags,Ne.looseCase=Ne.opt.lowercase?"toLowerCase":"toUpperCase",Ne.tags=[],Ne.closed=Ne.closedRoot=Ne.sawRoot=!1,Ne.tag=Ne.error=null,Ne.strict=!!ce,Ne.noscript=!!(ce||Ne.opt.noscript),Ne.state=Ee.BEGIN,Ne.strictEntities=Ne.opt.strictEntities,Ne.ENTITIES=Ne.strictEntities?Object.create(l.XML_ENTITIES):Object.create(l.ENTITIES),Ne.attribList=[],Ne.opt.xmlns&&(Ne.ns=Object.create(be)),Ne.trackPosition=Ne.opt.position!==!1,Ne.trackPosition&&(Ne.position=Ne.line=Ne.column=0),Re(Ne,"onready")}Object.create||(Object.create=function(ce){function k(){}k.prototype=ce;var Ne=new k;return Ne}),Object.keys||(Object.keys=function(ce){var k=[];for(var Ne in ce)ce.hasOwnProperty(Ne)&&k.push(Ne);return k});function T(ce){for(var k=Math.max(l.MAX_BUFFER_LENGTH,10),Ne=0,ye=0,Ze=r.length;ye<Ze;ye++){var Ge=ce[r[ye]].length;if(Ge>k)switch(r[ye]){case"textNode":Ve(ce);break;case"cdata":Le(ce,"oncdata",ce.cdata),ce.cdata="";break;case"script":Le(ce,"onscript",ce.script),ce.script="";break;default:tt(ce,"Max buffer length exceeded: "+r[ye])}Ne=Math.max(Ne,Ge)}var je=l.MAX_BUFFER_LENGTH-Ne;ce.bufferCheckPosition=je+ce.position}function A(ce){for(var k=0,Ne=r.length;k<Ne;k++)ce[r[k]]=""}function N(ce){Ve(ce),ce.cdata!==""&&(Le(ce,"oncdata",ce.cdata),ce.cdata=""),ce.script!==""&&(Le(ce,"onscript",ce.script),ce.script="")}E.prototype={end:function(){at(this)},write:pt,resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){N(this)}};var y;try{y=require("stream").Stream}catch(ce){y=function(){}}y||(y=function(){});var ue=l.EVENTS.filter(function(ce){return ce!=="error"&&ce!=="end"});function Z(ce,k){return new M(ce,k)}function M(ce,k){if(!(this instanceof M))return new M(ce,k);y.apply(this),this._parser=new E(ce,k),this.writable=!0,this.readable=!0;var Ne=this;this._parser.onend=function(){Ne.emit("end")},this._parser.onerror=function(ye){Ne.emit("error",ye),Ne._parser.error=null},this._decoder=null,ue.forEach(function(ye){Object.defineProperty(Ne,"on"+ye,{get:function(){return Ne._parser["on"+ye]},set:function(Ze){if(!Ze)return Ne.removeAllListeners(ye),Ne._parser["on"+ye]=Ze,Ze;Ne.on(ye,Ze)},enumerable:!0,configurable:!1})})}M.prototype=Object.create(y.prototype,{constructor:{value:M}}),M.prototype.write=function(ce){if(typeof Buffer=="function"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(ce)){if(!this._decoder){var k=string_decoder.StringDecoder;this._decoder=new k("utf8")}ce=this._decoder.write(ce)}return this._parser.write(ce.toString()),this.emit("data",ce),!0},M.prototype.end=function(ce){return ce&&ce.length&&this.write(ce),this._parser.end(),!0},M.prototype.on=function(ce,k){var Ne=this;return!Ne._parser["on"+ce]&&ue.indexOf(ce)!==-1&&(Ne._parser["on"+ce]=function(){var ye=arguments.length===1?[arguments[0]]:Array.apply(null,arguments);ye.splice(0,0,ce),Ne.emit.apply(Ne,ye)}),y.prototype.on.call(Ne,ce,k)};var Se="[CDATA[",ge="DOCTYPE",_e="http://www.w3.org/XML/1998/namespace",we="http://www.w3.org/2000/xmlns/",be={xml:_e,xmlns:we},Te=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,Ie=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,$e=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,De=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function Ce(ce){return ce===" "||ce===`
`||ce==="\r"||ce==="	"}function ke(ce){return ce==='"'||ce==="'"}function Fe(ce){return ce===">"||Ce(ce)}function Oe(ce,k){return ce.test(k)}function Be(ce,k){return!Oe(ce,k)}var Ee=0;l.STATE={BEGIN:Ee++,BEGIN_WHITESPACE:Ee++,TEXT:Ee++,TEXT_ENTITY:Ee++,OPEN_WAKA:Ee++,SGML_DECL:Ee++,SGML_DECL_QUOTED:Ee++,DOCTYPE:Ee++,DOCTYPE_QUOTED:Ee++,DOCTYPE_DTD:Ee++,DOCTYPE_DTD_QUOTED:Ee++,COMMENT_STARTING:Ee++,COMMENT:Ee++,COMMENT_ENDING:Ee++,COMMENT_ENDED:Ee++,CDATA:Ee++,CDATA_ENDING:Ee++,CDATA_ENDING_2:Ee++,PROC_INST:Ee++,PROC_INST_BODY:Ee++,PROC_INST_ENDING:Ee++,OPEN_TAG:Ee++,OPEN_TAG_SLASH:Ee++,ATTRIB:Ee++,ATTRIB_NAME:Ee++,ATTRIB_NAME_SAW_WHITE:Ee++,ATTRIB_VALUE:Ee++,ATTRIB_VALUE_QUOTED:Ee++,ATTRIB_VALUE_CLOSED:Ee++,ATTRIB_VALUE_UNQUOTED:Ee++,ATTRIB_VALUE_ENTITY_Q:Ee++,ATTRIB_VALUE_ENTITY_U:Ee++,CLOSE_TAG:Ee++,CLOSE_TAG_SAW_WHITE:Ee++,SCRIPT:Ee++,SCRIPT_ENDING:Ee++},l.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},l.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(l.ENTITIES).forEach(function(ce){var k=l.ENTITIES[ce],Ne=typeof k=="number"?String.fromCharCode(k):k;l.ENTITIES[ce]=Ne});for(var Pe in l.STATE)l.STATE[l.STATE[Pe]]=Pe;Ee=l.STATE;function Re(ce,k,Ne){ce[k]&&ce[k](Ne)}function Le(ce,k,Ne){ce.textNode&&Ve(ce),Re(ce,k,Ne)}function Ve(ce){ce.textNode=Qe(ce.opt,ce.textNode),ce.textNode&&Re(ce,"ontext",ce.textNode),ce.textNode=""}function Qe(ce,k){return ce.trim&&(k=k.trim()),ce.normalize&&(k=k.replace(/\s+/g," ")),k}function tt(ce,k){return Ve(ce),ce.trackPosition&&(k+=`
Line: `+ce.line+`
Column: `+ce.column+`
Char: `+ce.c),k=new Error(k),ce.error=k,Re(ce,"onerror",k),ce}function at(ce){return ce.sawRoot&&!ce.closedRoot&&Me(ce,"Unclosed root tag"),ce.state!==Ee.BEGIN&&ce.state!==Ee.BEGIN_WHITESPACE&&ce.state!==Ee.TEXT&&tt(ce,"Unexpected end"),Ve(ce),ce.c="",ce.closed=!0,Re(ce,"onend"),E.call(ce,ce.strict,ce.opt),ce}function Me(ce,k){if(typeof ce!="object"||!(ce instanceof E))throw new Error("bad call to strictFail");ce.strict&&tt(ce,k)}function nt(ce){ce.strict||(ce.tagName=ce.tagName[ce.looseCase]());var k=ce.tags[ce.tags.length-1]||ce,Ne=ce.tag={name:ce.tagName,attributes:{}};ce.opt.xmlns&&(Ne.ns=k.ns),ce.attribList.length=0,Le(ce,"onopentagstart",Ne)}function ft(ce,k){var Ne=ce.indexOf(":"),ye=Ne<0?["",ce]:ce.split(":"),Ze=ye[0],Ge=ye[1];return k&&ce==="xmlns"&&(Ze="xmlns",Ge=""),{prefix:Ze,local:Ge}}function ut(ce){if(ce.strict||(ce.attribName=ce.attribName[ce.looseCase]()),ce.attribList.indexOf(ce.attribName)!==-1||ce.tag.attributes.hasOwnProperty(ce.attribName)){ce.attribName=ce.attribValue="";return}if(ce.opt.xmlns){var k=ft(ce.attribName,!0),Ne=k.prefix,ye=k.local;if(Ne==="xmlns")if(ye==="xml"&&ce.attribValue!==_e)Me(ce,"xml: prefix must be bound to "+_e+`
Actual: `+ce.attribValue);else if(ye==="xmlns"&&ce.attribValue!==we)Me(ce,"xmlns: prefix must be bound to "+we+`
Actual: `+ce.attribValue);else{var Ze=ce.tag,Ge=ce.tags[ce.tags.length-1]||ce;Ze.ns===Ge.ns&&(Ze.ns=Object.create(Ge.ns)),Ze.ns[ye]=ce.attribValue}ce.attribList.push([ce.attribName,ce.attribValue])}else ce.tag.attributes[ce.attribName]=ce.attribValue,Le(ce,"onattribute",{name:ce.attribName,value:ce.attribValue});ce.attribName=ce.attribValue=""}function He(ce,k){if(ce.opt.xmlns){var Ne=ce.tag,ye=ft(ce.tagName);Ne.prefix=ye.prefix,Ne.local=ye.local,Ne.uri=Ne.ns[ye.prefix]||"",Ne.prefix&&!Ne.uri&&(Me(ce,"Unbound namespace prefix: "+JSON.stringify(ce.tagName)),Ne.uri=ye.prefix);var Ze=ce.tags[ce.tags.length-1]||ce;Ne.ns&&Ze.ns!==Ne.ns&&Object.keys(Ne.ns).forEach(function(rt){Le(ce,"onopennamespace",{prefix:rt,uri:Ne.ns[rt]})});for(var Ge=0,je=ce.attribList.length;Ge<je;Ge++){var Je=ce.attribList[Ge],et=Je[0],ot=Je[1],Ke=ft(et,!0),it=Ke.prefix,mt=Ke.local,_t=it===""?"":Ne.ns[it]||"",st={name:et,value:ot,prefix:it,local:mt,uri:_t};it&&it!=="xmlns"&&!_t&&(Me(ce,"Unbound namespace prefix: "+JSON.stringify(it)),st.uri=it),ce.tag.attributes[et]=st,Le(ce,"onattribute",st)}ce.attribList.length=0}ce.tag.isSelfClosing=!!k,ce.sawRoot=!0,ce.tags.push(ce.tag),Le(ce,"onopentag",ce.tag),k||(!ce.noscript&&ce.tagName.toLowerCase()==="script"?ce.state=Ee.SCRIPT:ce.state=Ee.TEXT,ce.tag=null,ce.tagName=""),ce.attribName=ce.attribValue="",ce.attribList.length=0}function qe(ce){if(!ce.tagName){Me(ce,"Weird empty close tag."),ce.textNode+="</>",ce.state=Ee.TEXT;return}if(ce.script){if(ce.tagName!=="script"){ce.script+="</"+ce.tagName+">",ce.tagName="",ce.state=Ee.SCRIPT;return}Le(ce,"onscript",ce.script),ce.script=""}var k=ce.tags.length,Ne=ce.tagName;ce.strict||(Ne=Ne[ce.looseCase]());for(var ye=Ne;k--;){var Ze=ce.tags[k];if(Ze.name!==ye)Me(ce,"Unexpected close tag");else break}if(k<0){Me(ce,"Unmatched closing tag: "+ce.tagName),ce.textNode+="</"+ce.tagName+">",ce.state=Ee.TEXT;return}ce.tagName=Ne;for(var Ge=ce.tags.length;Ge-- >k;){var je=ce.tag=ce.tags.pop();ce.tagName=ce.tag.name,Le(ce,"onclosetag",ce.tagName);var Je={};for(var et in je.ns)Je[et]=je.ns[et];var ot=ce.tags[ce.tags.length-1]||ce;ce.opt.xmlns&&je.ns!==ot.ns&&Object.keys(je.ns).forEach(function(Ke){var it=je.ns[Ke];Le(ce,"onclosenamespace",{prefix:Ke,uri:it})})}k===0&&(ce.closedRoot=!0),ce.tagName=ce.attribValue=ce.attribName="",ce.attribList.length=0,ce.state=Ee.TEXT}function ct(ce){var k=ce.entity,Ne=k.toLowerCase(),ye,Ze="";return ce.ENTITIES[k]?ce.ENTITIES[k]:ce.ENTITIES[Ne]?ce.ENTITIES[Ne]:(k=Ne,k.charAt(0)==="#"&&(k.charAt(1)==="x"?(k=k.slice(2),ye=parseInt(k,16),Ze=ye.toString(16)):(k=k.slice(1),ye=parseInt(k,10),Ze=ye.toString(10))),k=k.replace(/^0+/,""),isNaN(ye)||Ze.toLowerCase()!==k?(Me(ce,"Invalid character entity"),"&"+ce.entity+";"):String.fromCodePoint(ye))}function ht(ce,k){k==="<"?(ce.state=Ee.OPEN_WAKA,ce.startTagPosition=ce.position):Ce(k)||(Me(ce,"Non-whitespace before first tag."),ce.textNode=k,ce.state=Ee.TEXT)}function dt(ce,k){var Ne="";return k<ce.length&&(Ne=ce.charAt(k)),Ne}function pt(ce){var k=this;if(this.error)throw this.error;if(k.closed)return tt(k,"Cannot write after close. Assign an onready handler.");if(ce===null)return at(k);typeof ce=="object"&&(ce=ce.toString());for(var Ne=0,ye="";ye=dt(ce,Ne++),k.c=ye,!!ye;)switch(k.trackPosition&&(k.position++,ye===`
`?(k.line++,k.column=0):k.column++),k.state){case Ee.BEGIN:if(k.state=Ee.BEGIN_WHITESPACE,ye==="\uFEFF")continue;ht(k,ye);continue;case Ee.BEGIN_WHITESPACE:ht(k,ye);continue;case Ee.TEXT:if(k.sawRoot&&!k.closedRoot){for(var Ze=Ne-1;ye&&ye!=="<"&&ye!=="&";)ye=dt(ce,Ne++),ye&&k.trackPosition&&(k.position++,ye===`
`?(k.line++,k.column=0):k.column++);k.textNode+=ce.substring(Ze,Ne-1)}ye==="<"&&!(k.sawRoot&&k.closedRoot&&!k.strict)?(k.state=Ee.OPEN_WAKA,k.startTagPosition=k.position):(!Ce(ye)&&(!k.sawRoot||k.closedRoot)&&Me(k,"Text data outside of root node."),ye==="&"?k.state=Ee.TEXT_ENTITY:k.textNode+=ye);continue;case Ee.SCRIPT:ye==="<"?k.state=Ee.SCRIPT_ENDING:k.script+=ye;continue;case Ee.SCRIPT_ENDING:ye==="/"?k.state=Ee.CLOSE_TAG:(k.script+="<"+ye,k.state=Ee.SCRIPT);continue;case Ee.OPEN_WAKA:if(ye==="!")k.state=Ee.SGML_DECL,k.sgmlDecl="";else if(!Ce(ye))if(Oe(Te,ye))k.state=Ee.OPEN_TAG,k.tagName=ye;else if(ye==="/")k.state=Ee.CLOSE_TAG,k.tagName="";else if(ye==="?")k.state=Ee.PROC_INST,k.procInstName=k.procInstBody="";else{if(Me(k,"Unencoded <"),k.startTagPosition+1<k.position){var Ge=k.position-k.startTagPosition;ye=new Array(Ge).join(" ")+ye}k.textNode+="<"+ye,k.state=Ee.TEXT}continue;case Ee.SGML_DECL:(k.sgmlDecl+ye).toUpperCase()===Se?(Le(k,"onopencdata"),k.state=Ee.CDATA,k.sgmlDecl="",k.cdata=""):k.sgmlDecl+ye==="--"?(k.state=Ee.COMMENT,k.comment="",k.sgmlDecl=""):(k.sgmlDecl+ye).toUpperCase()===ge?(k.state=Ee.DOCTYPE,(k.doctype||k.sawRoot)&&Me(k,"Inappropriately located doctype declaration"),k.doctype="",k.sgmlDecl=""):ye===">"?(Le(k,"onsgmldeclaration",k.sgmlDecl),k.sgmlDecl="",k.state=Ee.TEXT):(ke(ye)&&(k.state=Ee.SGML_DECL_QUOTED),k.sgmlDecl+=ye);continue;case Ee.SGML_DECL_QUOTED:ye===k.q&&(k.state=Ee.SGML_DECL,k.q=""),k.sgmlDecl+=ye;continue;case Ee.DOCTYPE:ye===">"?(k.state=Ee.TEXT,Le(k,"ondoctype",k.doctype),k.doctype=!0):(k.doctype+=ye,ye==="["?k.state=Ee.DOCTYPE_DTD:ke(ye)&&(k.state=Ee.DOCTYPE_QUOTED,k.q=ye));continue;case Ee.DOCTYPE_QUOTED:k.doctype+=ye,ye===k.q&&(k.q="",k.state=Ee.DOCTYPE);continue;case Ee.DOCTYPE_DTD:k.doctype+=ye,ye==="]"?k.state=Ee.DOCTYPE:ke(ye)&&(k.state=Ee.DOCTYPE_DTD_QUOTED,k.q=ye);continue;case Ee.DOCTYPE_DTD_QUOTED:k.doctype+=ye,ye===k.q&&(k.state=Ee.DOCTYPE_DTD,k.q="");continue;case Ee.COMMENT:ye==="-"?k.state=Ee.COMMENT_ENDING:k.comment+=ye;continue;case Ee.COMMENT_ENDING:ye==="-"?(k.state=Ee.COMMENT_ENDED,k.comment=Qe(k.opt,k.comment),k.comment&&Le(k,"oncomment",k.comment),k.comment=""):(k.comment+="-"+ye,k.state=Ee.COMMENT);continue;case Ee.COMMENT_ENDED:ye!==">"?(Me(k,"Malformed comment"),k.comment+="--"+ye,k.state=Ee.COMMENT):k.state=Ee.TEXT;continue;case Ee.CDATA:ye==="]"?k.state=Ee.CDATA_ENDING:k.cdata+=ye;continue;case Ee.CDATA_ENDING:ye==="]"?k.state=Ee.CDATA_ENDING_2:(k.cdata+="]"+ye,k.state=Ee.CDATA);continue;case Ee.CDATA_ENDING_2:ye===">"?(k.cdata&&Le(k,"oncdata",k.cdata),Le(k,"onclosecdata"),k.cdata="",k.state=Ee.TEXT):ye==="]"?k.cdata+="]":(k.cdata+="]]"+ye,k.state=Ee.CDATA);continue;case Ee.PROC_INST:ye==="?"?k.state=Ee.PROC_INST_ENDING:Ce(ye)?k.state=Ee.PROC_INST_BODY:k.procInstName+=ye;continue;case Ee.PROC_INST_BODY:if(!k.procInstBody&&Ce(ye))continue;ye==="?"?k.state=Ee.PROC_INST_ENDING:k.procInstBody+=ye;continue;case Ee.PROC_INST_ENDING:ye===">"?(Le(k,"onprocessinginstruction",{name:k.procInstName,body:k.procInstBody}),k.procInstName=k.procInstBody="",k.state=Ee.TEXT):(k.procInstBody+="?"+ye,k.state=Ee.PROC_INST_BODY);continue;case Ee.OPEN_TAG:Oe(Ie,ye)?k.tagName+=ye:(nt(k),ye===">"?He(k):ye==="/"?k.state=Ee.OPEN_TAG_SLASH:(Ce(ye)||Me(k,"Invalid character in tag name"),k.state=Ee.ATTRIB));continue;case Ee.OPEN_TAG_SLASH:ye===">"?(He(k,!0),qe(k)):(Me(k,"Forward-slash in opening tag not followed by >"),k.state=Ee.ATTRIB);continue;case Ee.ATTRIB:if(Ce(ye))continue;ye===">"?He(k):ye==="/"?k.state=Ee.OPEN_TAG_SLASH:Oe(Te,ye)?(k.attribName=ye,k.attribValue="",k.state=Ee.ATTRIB_NAME):Me(k,"Invalid attribute name");continue;case Ee.ATTRIB_NAME:ye==="="?k.state=Ee.ATTRIB_VALUE:ye===">"?(Me(k,"Attribute without value"),k.attribValue=k.attribName,ut(k),He(k)):Ce(ye)?k.state=Ee.ATTRIB_NAME_SAW_WHITE:Oe(Ie,ye)?k.attribName+=ye:Me(k,"Invalid attribute name");continue;case Ee.ATTRIB_NAME_SAW_WHITE:if(ye==="=")k.state=Ee.ATTRIB_VALUE;else{if(Ce(ye))continue;Me(k,"Attribute without value"),k.tag.attributes[k.attribName]="",k.attribValue="",Le(k,"onattribute",{name:k.attribName,value:""}),k.attribName="",ye===">"?He(k):Oe(Te,ye)?(k.attribName=ye,k.state=Ee.ATTRIB_NAME):(Me(k,"Invalid attribute name"),k.state=Ee.ATTRIB)}continue;case Ee.ATTRIB_VALUE:if(Ce(ye))continue;ke(ye)?(k.q=ye,k.state=Ee.ATTRIB_VALUE_QUOTED):(Me(k,"Unquoted attribute value"),k.state=Ee.ATTRIB_VALUE_UNQUOTED,k.attribValue=ye);continue;case Ee.ATTRIB_VALUE_QUOTED:if(ye!==k.q){ye==="&"?k.state=Ee.ATTRIB_VALUE_ENTITY_Q:k.attribValue+=ye;continue}ut(k),k.q="",k.state=Ee.ATTRIB_VALUE_CLOSED;continue;case Ee.ATTRIB_VALUE_CLOSED:Ce(ye)?k.state=Ee.ATTRIB:ye===">"?He(k):ye==="/"?k.state=Ee.OPEN_TAG_SLASH:Oe(Te,ye)?(Me(k,"No whitespace between attributes"),k.attribName=ye,k.attribValue="",k.state=Ee.ATTRIB_NAME):Me(k,"Invalid attribute name");continue;case Ee.ATTRIB_VALUE_UNQUOTED:if(!Fe(ye)){ye==="&"?k.state=Ee.ATTRIB_VALUE_ENTITY_U:k.attribValue+=ye;continue}ut(k),ye===">"?He(k):k.state=Ee.ATTRIB;continue;case Ee.CLOSE_TAG:if(k.tagName)ye===">"?qe(k):Oe(Ie,ye)?k.tagName+=ye:k.script?(k.script+="</"+k.tagName,k.tagName="",k.state=Ee.SCRIPT):(Ce(ye)||Me(k,"Invalid tagname in closing tag"),k.state=Ee.CLOSE_TAG_SAW_WHITE);else{if(Ce(ye))continue;Be(Te,ye)?k.script?(k.script+="</"+ye,k.state=Ee.SCRIPT):Me(k,"Invalid tagname in closing tag."):k.tagName=ye}continue;case Ee.CLOSE_TAG_SAW_WHITE:if(Ce(ye))continue;ye===">"?qe(k):Me(k,"Invalid characters in closing tag");continue;case Ee.TEXT_ENTITY:case Ee.ATTRIB_VALUE_ENTITY_Q:case Ee.ATTRIB_VALUE_ENTITY_U:var je,Je;switch(k.state){case Ee.TEXT_ENTITY:je=Ee.TEXT,Je="textNode";break;case Ee.ATTRIB_VALUE_ENTITY_Q:je=Ee.ATTRIB_VALUE_QUOTED,Je="attribValue";break;case Ee.ATTRIB_VALUE_ENTITY_U:je=Ee.ATTRIB_VALUE_UNQUOTED,Je="attribValue";break}if(ye===";")if(k.opt.unparsedEntities){var et=ct(k);k.entity="",k.state=je,k.write(et)}else k[Je]+=ct(k),k.entity="",k.state=je;else Oe(k.entity.length?De:$e,ye)?k.entity+=ye:(Me(k,"Invalid character in entity name"),k[Je]+="&"+k.entity+ye,k.entity="",k.state=je);continue;default:throw new Error(k,"Unknown state: "+k.state)}return k.position>=k.bufferCheckPosition&&T(k),k}/*! http://mths.be/fromcodepoint v0.1.0 by @mathias */String.fromCodePoint||function(){var ce=String.fromCharCode,k=Math.floor,Ne=function(){var ye=16384,Ze=[],Ge,je,Je=-1,et=arguments.length;if(!et)return"";for(var ot="";++Je<et;){var Ke=Number(arguments[Je]);if(!isFinite(Ke)||Ke<0||Ke>1114111||k(Ke)!==Ke)throw RangeError("Invalid code point: "+Ke);Ke<=65535?Ze.push(Ke):(Ke-=65536,Ge=(Ke>>10)+55296,je=Ke%1024+56320,Ze.push(Ge,je)),(Je+1===et||Ze.length>ye)&&(ot+=ce.apply(null,Ze),Ze.length=0)}return ot};Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:Ne,configurable:!0,writable:!0}):String.fromCodePoint=Ne}()})(t)})(sax);const logger={debug:()=>{}},g$2=t=>{const l=sax.parser(!0,{trim:!1,normalize:!1});let r,E=null;return new Promise((T,A)=>{l.onopentag=N=>{const y={_parent:E||void 0,_children:[],_fTextNode:!1,_tag:N.name,_attrs:N.attributes};E!=null?E._children.push(y):r=y,E=y},l.onclosetag=()=>{E=E!=null?E._parent:null},l.ontext=N=>{E!=null&&E._children.push({_parent:E,_children:[],_fTextNode:!0,_text:N})},l.onend=()=>{T(r)},l.onerror=N=>{A(N)},l.write(t),l.end()})};function m$1(t,l,r=""){const E=r.length?"":'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>',T=[Buffer.from(E,"utf-8")];if(t._fTextNode)T.push(Buffer.from(d$1(t._text,l)));else{let A="";const N=t._attrs;Object.keys(N).forEach(M=>{A+=` ${M}="${_$2(N[M])}"`});const y=t._children.length>0,ue=y?"":"/";T.push(Buffer.from(`
${r}<${t._tag}${A}${ue}>`));let Z=!1;if(t._children.forEach(M=>{T.push(m$1(M,l,`${r}  `)),Z=!M._fTextNode}),y){const M=Z?`
${r}`:"";T.push(Buffer.from(`${M}</${t._tag}>`))}}return Buffer.concat(T)}const d$1=(t,l)=>{let r="";const E=t.split(l.literalXmlDelimiter);let T=!1;for(let A=0;A<E.length;A++){let N=E[A];T||(N=N.replace(/&/g,"&amp;"),N=N.replace(/</g,"&lt;"),N=N.replace(/>/g,"&gt;")),r+=N,T=!T}return r},_$2=t=>{let l=typeof t=="string"?t:t.value;return l=l.replace(/&/g,"&amp;"),l=l.replace(/</g,"&lt;"),l=l.replace(/>/g,"&gt;"),l=l.replace(/'/g,"&apos;"),l=l.replace(/"/g,"&quot;"),l};var n=Object.defineProperty,c$2=(t,l,r)=>l in t?n(t,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[l]=r,e=(t,l,r)=>c$2(t,typeof l!="symbol"?l+"":l,r);function isError(t){return t instanceof Error||typeof t=="object"&&!!t&&"name"in t&&"message"in t}class NullishCommandResultError extends Error{constructor(l){super(`Result of command ${l} is null or undefined and rejectNullish is set`),e(this,"command"),Object.setPrototypeOf(this,NullishCommandResultError.prototype),this.command=l}}class ObjectCommandResultError extends Error{constructor(l,r){super(`Result of command '${l}' is an object`),e(this,"command"),e(this,"result"),Object.setPrototypeOf(this,ObjectCommandResultError.prototype),this.command=l,this.result=r}}class CommandSyntaxError extends Error{constructor(l){super(`Invalid command syntax: ${l}`),e(this,"command"),Object.setPrototypeOf(this,CommandSyntaxError.prototype),this.command=l}}class InvalidCommandError extends Error{constructor(l,r){super(`${l}: ${r}`),e(this,"command"),Object.setPrototypeOf(this,InvalidCommandError.prototype),this.command=r}}class CommandExecutionError extends Error{constructor(l,r){super(`Error executing command '${r}': ${l.name}: ${l.message}`),e(this,"command"),e(this,"err"),Object.setPrototypeOf(this,CommandExecutionError.prototype),this.command=r,this.err=l}}class ImageError extends CommandExecutionError{}class InternalError extends Error{constructor(l){super(`INTERNAL ERROR: ${l}`)}}class TemplateParseError extends Error{}class IncompleteConditionalStatementError extends Error{constructor(){super("Incomplete IF/END-IF statement. Make sure each IF-statement has a corresponding END-IF command.")}}class UnterminatedForLoopError extends Error{constructor(l){super(`Unterminated FOR-loop ('FOR ${l.varName}'). Make sure each FOR loop has a corresponding END-FOR command.`)}}const i=t=>t._fTextNode?{_children:[],_fTextNode:!0,_text:t._text}:{_children:[],_fTextNode:!1,_tag:t._tag,_attrs:t._attrs},_$1=t=>{const l=t._parent;if(l==null)return null;const r=l._children,E=r.indexOf(t);return E<0||E>=r.length-1?null:r[E+1]},a$1=t=>{const l=t._parent;if(!(l&&!l._fTextNode&&l._tag==="w:t"))throw new TemplateParseError("Template syntax error: text node not within w:t");const r=l._parent;if(r==null)throw new TemplateParseError("Template syntax error: w:t node has no parent");const E=r._children.indexOf(l);if(E<0)throw new TemplateParseError("Template syntax error");const T=i(l);T._parent=r;const A={_parent:T,_children:[],_fTextNode:!0,_text:""};return T._children=[A],r._children.splice(E+1,0,T),A},c$1=(t,l={},r=[])=>{const E={_fTextNode:!1,_tag:t,_attrs:l,_children:r};return E._children.forEach(T=>{T._parent=E}),E},u=t=>({_children:[],_fTextNode:!0,_text:t}),p=(t,l)=>(t._children.push(l),l._parent=t,l),s=t=>t.loops.length?t.loops[t.loops.length-1]:null,f=t=>{const l=s(t);return l!=null&&l.idx<0},h$1=t=>{if(!t.length)return;const l=t.length-1,{varName:r,idx:E,loopOver:T,isIf:A}=t[l],N=E>=0?E+1:"EXPLORATION";logger.debug(`${A?"IF":"FOR"} loop on ${l}:${r}${N}/${T.length}`)},g$1=(t,l)=>{let r=t,E=!1,T=null,A=0;const N=`${l[0]}CMD_NODE${l[1]}`;for(;r!=null;){if(!r._fTextNode&&r._tag==="w:t"&&(r._attrs["xml:space"]="preserve"),!r._fTextNode&&r._tag==="w:p"&&T&&(T._text+=" "),r._fTextNode&&r._parent&&!r._parent._fTextNode&&r._parent._tag==="w:t"){T==null&&(T=r);const y=r._text;r._text="";for(let ue=0;ue<y.length;ue++){const Z=y[ue],M=E?l[1]:l[0];if(Z===M[A]){if(A+=1,A===M.length){E=!E;const Se=r===T;E&&T._text.length&&(T=a$1(T),Se&&(r=T)),T._text+=M,!E&&ue<y.length-1&&(T=a$1(T),Se&&(r=T)),A=0,E||(T=r)}}else A&&(T._text+=M.slice(0,A),A=0,E||(T=r)),T._text+=Z}!E&&!A&&(T=null),y.length&&!r._text.length&&(r._text=N)}if(r._children.length)r=r._children[0];else{let y=!1;for(;r._parent!=null;){const ue=r._parent,Z=_$1(r);if(Z){y=!0,r=Z;break}r=ue}y||(r=null)}}return t};var vmBrowserify={};(function(exports){var indexOf=function(t,l){if(t.indexOf)return t.indexOf(l);for(var r=0;r<t.length;r++)if(t[r]===l)return r;return-1},Object_keys=function(t){if(Object.keys)return Object.keys(t);var l=[];for(var r in t)l.push(r);return l},forEach=function(t,l){if(t.forEach)return t.forEach(l);for(var r=0;r<t.length;r++)l(t[r],r,t)},defineProp=function(){try{return Object.defineProperty({},"_",{}),function(t,l,r){Object.defineProperty(t,l,{writable:!0,enumerable:!1,configurable:!0,value:r})}}catch(t){return function(l,r,E){l[r]=E}}}(),globals=["Array","Boolean","Date","Error","EvalError","Function","Infinity","JSON","Math","NaN","Number","Object","RangeError","ReferenceError","RegExp","String","SyntaxError","TypeError","URIError","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","eval","isFinite","isNaN","parseFloat","parseInt","undefined","unescape"];function Context(){}Context.prototype={};var Script=exports.Script=function(l){if(!(this instanceof Script))return new Script(l);this.code=l};Script.prototype.runInContext=function(t){if(!(t instanceof Context))throw new TypeError("needs a 'context' argument.");var l=document.createElement("iframe");l.style||(l.style={}),l.style.display="none",document.body.appendChild(l);var r=l.contentWindow,E=r.eval,T=r.execScript;!E&&T&&(T.call(r,"null"),E=r.eval),forEach(Object_keys(t),function(y){r[y]=t[y]}),forEach(globals,function(y){t[y]&&(r[y]=t[y])});var A=Object_keys(r),N=E.call(r,this.code);return forEach(Object_keys(r),function(y){(y in t||indexOf(A,y)===-1)&&(t[y]=r[y])}),forEach(globals,function(y){y in t||defineProp(t,y,r[y])}),document.body.removeChild(l),N},Script.prototype.runInThisContext=function(){return eval(this.code)},Script.prototype.runInNewContext=function(t){var l=Script.createContext(t),r=this.runInContext(l);return t&&forEach(Object_keys(l),function(E){t[E]=l[E]}),r},forEach(Object_keys(Script.prototype),function(t){exports[t]=Script[t]=function(l){var r=Script(l);return r[t].apply(r,[].slice.call(arguments,1))}}),exports.isContext=function(t){return t instanceof Context},exports.createScript=function(t){return exports.Script(t)},exports.createContext=Script.createContext=function(t){var l=new Context;return typeof t=="object"&&forEach(Object_keys(t),function(r){l[r]=t[r]}),l}})(vmBrowserify);var w=Object.defineProperty,C=Object.defineProperties,c=Object.getOwnPropertyDescriptors,_=Object.getOwnPropertySymbols,h=Object.prototype.hasOwnProperty,b$1=Object.prototype.propertyIsEnumerable,m=(t,l,r)=>l in t?w(t,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[l]=r,a=(t,l)=>{for(var r in l||(l={}))h.call(l,r)&&m(t,r,l[r]);if(_)for(var r of _(l))b$1.call(l,r)&&m(t,r,l[r]);return t},d=(t,l)=>C(t,c(l));async function runUserJsAndGetRaw(t,l,r){var E;const T=a(a(d(a({},r.jsSandbox||{}),{__code__:l,__result__:void 0}),t),r.options.additionalJsContext),A=s(r);A&&(T.$idx=A.idx),Object.keys(r.vars).forEach(ue=>{T[`$${ue}`]=r.vars[ue]});let N,y;try{if(r.options.runJs){const ue=r.options.runJs({sandbox:T,ctx:r});N=ue.modifiedSandbox,y=await ue.result}else if(r.options.noSandbox)N=T,y=await new Function("with(this) { return eval(__code__); }").call(N);else{const ue=new vmBrowserify.Script((E=T.__code__)!=null?E:"");N=vmBrowserify.createContext(T),y=await ue.runInContext(N)}}catch(ue){const Z=isError(ue)?ue:new Error(`${ue}`);if(r.options.errorHandler!=null)N=T,y=await r.options.errorHandler(Z,l);else throw new CommandExecutionError(Z,l)}if(r.options.rejectNullish&&y==null){const ue=new NullishCommandResultError(l);if(r.options.errorHandler!=null)y=await r.options.errorHandler(ue,l);else throw ue}return r.jsSandbox=d(a({},N),{__code__:void 0,__result__:void 0}),y}const ImageExtensions=[".png",".gif",".jpg",".jpeg",".svg"],BUILT_IN_COMMANDS=["QUERY","CMD_NODE","ALIAS","FOR","END-FOR","IF","END-IF","INS","EXEC","IMAGE","LINK","HTML"];var Q=Object.defineProperty,G=Object.defineProperties,W=Object.getOwnPropertyDescriptors,L=Object.getOwnPropertySymbols,X=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable,$=(t,l,r)=>l in t?Q(t,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[l]=r,S=(t,l)=>{for(var r in l||(l={}))X.call(l,r)&&$(t,r,l[r]);if(L)for(var r of L(l))j.call(l,r)&&$(t,r,l[r]);return t},x=(t,l)=>G(t,W(l));function newContext(t,l=0){return{gCntIf:0,gCntEndIf:0,level:1,fCmd:!1,cmd:"",fSeekQuery:!1,buffers:{"w:p":{text:"",cmds:"",fInsertedText:!1},"w:tr":{text:"",cmds:"",fInsertedText:!1},"w:tc":{text:"",cmds:"",fInsertedText:!1}},imageAndShapeIdIncrement:l,images:{},linkId:0,links:{},htmlId:0,htmls:{},vars:{},loops:[],fJump:!1,shorthands:{},options:t,pIfCheckMap:new Map,trIfCheckMap:new Map}}async function extractQuery(t,l){const r=newContext(l);r.fSeekQuery=!0;let E=t;for(;;){if(E._children.length)E=E._children[0];else{let A=!1;for(;E._parent!=null;){const N=E._parent,y=_$1(E);if(y){E=y,A=!0;break}E=N}if(!A)break}if(!E)break;const T=E._parent;if(E._fTextNode&&T&&!T._fTextNode&&T._tag==="w:t"&&await U(null,E,r,H$1),r.query!=null)break}return r.query}async function produceJsReport(t,l,r){return walkTemplate(t,l,r,H$1)}function findHighestImgId(t){const l=[],r=E=>{for(const T of E._children){const A=T._fTextNode?null:T._tag;if(A!=null){if(A==="wp:docPr"){if(T._fTextNode)continue;const N=T._attrs.id;if(typeof N!="string")continue;const y=Number.parseInt(N,10);Number.isSafeInteger(y)&&l.push(y)}T._children.length>0&&r(T)}}};return r(t),l.length>0?Math.max(...l):0}const b=t=>JSON.stringify(t._fTextNode?{_ifName:t._ifName,_fTextNode:t._fTextNode,_text:t==null?void 0:t._text}:{_ifName:t._ifName,_fTextNode:t._fTextNode,_tag:t==null?void 0:t._tag,_attrs:t==null?void 0:t._attrs}),B$1=t=>{let l=t._parent,r=null;for(;l!=null&&r==null;){if((l._fTextNode?null:l._tag)==="w:p"){const E=l._parent!=null?l._parent._parent:null;E!=null&&!E._fTextNode&&E._tag==="w:tr"?r=E:r=l}l=l._parent}return r};async function walkTemplate(t,l,r,E){var T;const A=i(l);let N=l,y=A,ue,Z=0;const M=[];let Se=0;const ge=((T=r.options)==null?void 0:T.maximumWalkingDepth)||1e6;for(;;){const _e=s(r);let we=null;if(r.fJump){if(!_e)throw new InternalError("jumping while curLoop is null");logger.debug(`Jumping to level ${_e.refNodeLevel}...`,b(_e.refNode)),Z=r.level-_e.refNodeLevel,N=_e.refNode,r.level=_e.refNodeLevel,r.fJump=!1,ue="JUMP"}else if(N._children.length&&ue!=="UP")N=N._children[0],r.level+=1,ue="DOWN";else if(we=_$1(N))N=we,ue="SIDE";else{const be=N._parent;if(be==null)break;if(Se>ge)throw logger.debug(`=== parent is still not null after ${Se} loops, something must be wrong ...`,b(be)),new InternalError("infinite loop or massive dataset detected. Please review and try again");N=be,r.level-=1,ue="UP"}if(logger.debug(`Next node [${ue}, level ${r.level}]`,b(N)),ue!=="DOWN"){const be=y._fTextNode?null:y._tag;let Te=!1;if((be==="w:p"||be==="w:tbl"||be==="w:tr"||be==="w:tc")&&f(r))Te=!0;else if(be==="w:p"||be==="w:tr"||be==="w:tc"){const Ie=r.buffers[be];Te=Ie.text===""&&Ie.cmds!==""&&!Ie.fInsertedText,be==="w:tr"&&Te&&(Te=N._children.filter($e=>!$e._fTextNode&&$e._tag==="w:tr").length!==1),be==="w:tc"&&Te&&(Te=!(y._children.filter($e=>!$e._fTextNode&&$e._tag==="w:tbl").length>0))}Te&&y._parent!=null&&y._parent._children.pop()}if(ue==="UP"){f(r)&&_e&&N===_e.refNode._parent&&(_e.refNode=N,_e.refNodeLevel-=1,logger.debug(`Updated loop '${_e.varName}' refNode: `+b(N)));const be=y._parent;if(be==null)throw new InternalError("node parent is null");if(y=be,r.pendingImageNode&&!y._fTextNode&&y._tag==="w:t"){const Te=r.pendingImageNode.image,Ie=r.pendingImageNode.caption,$e=y._parent;$e&&(Te._parent=$e,$e._children.pop(),$e._children.push(Te),Ie&&$e._children.push(...Ie),r.buffers["w:p"].fInsertedText=!0,r.buffers["w:tr"].fInsertedText=!0,r.buffers["w:tc"].fInsertedText=!0),delete r.pendingImageNode}if(r.pendingLinkNode&&!y._fTextNode&&y._tag==="w:r"){const Te=r.pendingLinkNode,Ie=y._parent;Ie&&(Te._parent=Ie,Ie._children.pop(),Ie._children.push(Te),r.buffers["w:p"].fInsertedText=!0,r.buffers["w:tr"].fInsertedText=!0,r.buffers["w:tc"].fInsertedText=!0),delete r.pendingLinkNode}if(r.pendingHtmlNode&&!y._fTextNode&&y._tag==="w:p"){const Te=r.pendingHtmlNode,Ie=y._parent;Ie&&(Te._parent=Ie,Ie._children.pop(),Ie._children.push(Te),r.buffers["w:p"].fInsertedText=!0,r.buffers["w:tr"].fInsertedText=!0,r.buffers["w:tc"].fInsertedText=!0),delete r.pendingHtmlNode}!y._fTextNode&&y._tag==="w:tc"&&!y._children.filter(Te=>!Te._fTextNode&&(Te._tag==="w:p"||Te._tag==="w:altChunk")).length&&y._children.push({_parent:y,_children:[],_fTextNode:!1,_tag:"w:p",_attrs:{}}),!y._fTextNode&&y._tag==="w:rPr"&&(r.textRunPropsNode=y),!N._fTextNode&&N._tag==="w:r"&&delete r.textRunPropsNode}if(ue==="DOWN"||ue==="SIDE"){if(ue==="SIDE"){if(y._parent==null)throw new InternalError("node parent is null");y=y._parent}const be=N._fTextNode?null:N._tag;(be==="w:p"||be==="w:tr"||be==="w:tc")&&(r.buffers[be]={text:"",cmds:"",fInsertedText:!1});const Te=i(N);Te._parent=y,y._children.push(Te);const Ie=Te._tag;!f(r)&&(Ie==="wp:docPr"||Ie==="v:shape")&&(logger.debug("detected a - ",b(Te)),pe$1(Te,r));const $e=N._parent;if(N._fTextNode&&$e&&!$e._fTextNode&&$e._tag==="w:t"){const De=await U(t,N,r,E);typeof De=="string"?(Te._text=De,logger.debug("Inserted command result string into node. Updated node: "+b(Te))):M.push(...De)}y=Te}if(ue==="JUMP")for(;Z>0;){if(y._parent==null)throw new InternalError("node parent is null");y=y._parent,Z-=1}Se++}if(r.gCntIf!==r.gCntEndIf){const _e=new IncompleteConditionalStatementError;if(r.options.failFast)throw _e;M.push(_e)}if(r.loops.filter(_e=>!_e.isIf).length>0){const _e=r.loops[r.loops.length-1],we=new UnterminatedForLoopError(_e);if(r.options.failFast)throw we;M.push(we)}return M.length>0?{status:"errors",errors:M}:{status:"success",report:A,images:r.images,links:r.links,htmls:r.htmls}}const U=async(t,l,r,E)=>{const{cmdDelimiter:T,failFast:A}=r.options,N=l._text;if(N==null||N==="")return"";const y=N.split(T[0]).map(M=>M.split(T[1])).reduce((M,Se)=>M.concat(Se));let ue="";const Z=[];for(let M=0;M<y.length;M++){M>0&&R(T[0],r,{fCmd:!0});const Se=y[M];if(r.fCmd?r.cmd+=Se:f(r)||(ue+=Se),R(Se,r,{fCmd:r.fCmd}),M<y.length-1){if(r.fCmd){const ge=await E(t,l,r);if(ge!=null)if(typeof ge=="string")ue+=ge,R(ge,r,{fCmd:!1,fInsertedText:!0});else{if(A)throw ge;Z.push(ge)}}r.fCmd=!r.fCmd}}return Z.length>0?Z:ue},H$1=async(t,l,r)=>{const E=getCommand(r.cmd,r.shorthands,r.options.fixSmartQuotes);r.cmd="";try{const{cmdName:T,cmdRest:A}=splitCommand(E);if(T!=="CMD_NODE"&&logger.debug(`Processing cmd: ${E}`),r.fSeekQuery){T==="QUERY"&&(r.query=A);return}if(!(T==="QUERY"||T==="CMD_NODE"))if(T==="ALIAS"){const N=/^(\S+)\s+(.+)/.exec(A);if(!N)throw new InvalidCommandError("Invalid ALIAS command",E);const y=N[1],ue=N[2];r.shorthands[y]=ue,logger.debug(`Defined alias '${y}' for: ${ue}`)}else if(T==="FOR"||T==="IF")await re(t,l,r,E,T,A);else if(T==="END-FOR"||T==="END-IF")oe(l,r,E,T,A);else if(T==="INS"){if(!f(r)){let N=await runUserJsAndGetRaw(t,A,r);if(N==null)return"";if(typeof N=="object"&&!Array.isArray(N)){const ue=new ObjectCommandResultError(A,N);if(r.options.errorHandler!=null)N=await r.options.errorHandler(ue,A);else throw ue}let y=String(N);if(r.options.processLineBreaks){const{literalXmlDelimiter:ue}=r.options;if(r.options.processLineBreaksAsNewText){const Z=y.split(`
`),M=`${ue}<w:br/>${ue}`,Se=`${ue}</w:t>${ue}`,ge=`${ue}<w:t xml:space="preserve">${ue}`;y=Z.join(`${Se}${M}${ge}`)}else y=y.replace(/\n/g,`${ue}<w:br/>${ue}`)}return y}}else if(T==="EXEC")f(r)||await runUserJsAndGetRaw(t,A,r);else if(T==="IMAGE"){if(!f(r)){const N=await runUserJsAndGetRaw(t,A,r);if(N!=null)try{se(r,N)}catch(y){throw isError(y)?new ImageError(y,E):y}}}else if(T==="LINK"){if(!f(r)){const N=await runUserJsAndGetRaw(t,A,r);N!=null&&await le(r,N)}}else if(T==="HTML"){if(!f(r)){const N=await runUserJsAndGetRaw(t,A,r);N!=null&&await fe(r,N)}}else throw new CommandSyntaxError(E);return}catch(T){if(!isError(T))throw T;return r.options.errorHandler!=null?r.options.errorHandler(T):T}},te$1=BUILT_IN_COMMANDS.map(t=>new RegExp(`^${t}\\b`)),ne=t=>!te$1.some(l=>l.test(t.toUpperCase()));function getCommand(t,l,r){let E=t.trim();if(E[0]==="*"){const T=E.slice(1).trim();if(!l[T])throw new InvalidCommandError("Unknown alias",E);E=l[T]}else E[0]==="="?E=`INS ${E.slice(1).trim()}`:E[0]==="!"?E=`EXEC ${E.slice(1).trim()}`:ne(E)&&(E=`INS ${E.trim()}`);return r&&(E=E.replace(/[\u201C\u201D\u201E]/g,'"').replace(/[\u2018\u2019\u201A]/g,"'")),E.trim()}function splitCommand(t){const l=/^(\S+)\s*/.exec(t);let r,E="";return l!=null&&(r=l[1].toUpperCase(),E=t.slice(r.length).trim()),{cmdName:r,cmdRest:E}}const re=async(t,l,r,E,T,A)=>{const N=T==="IF";let y=null,ue;if(N)l._ifName||(l._ifName=`__if_${r.gCntIf}`,r.gCntIf+=1),ue=l._ifName;else{if(y=/^(\S+)\s+IN\s+(.+)/i.exec(A),!y)throw new InvalidCommandError("Invalid FOR command",E);ue=y[1]}const Z=s(r);if(!(Z&&Z.varName===ue)){if(N){const _e=B$1(l),we=_e!=null?_e._fTextNode?null:_e._tag:null;if(_e!=null){if(we==="w:p"){if(r.pIfCheckMap.has(_e)&&r.pIfCheckMap.get(_e)!==E)throw new InvalidCommandError("Invalid IF command nested into another IF command on the same line",E);r.pIfCheckMap.set(_e,E)}else if(we==="w:tr"){if(r.trIfCheckMap.has(_e)&&r.trIfCheckMap.get(_e)!==E)throw new InvalidCommandError("Invalid IF command nested into another IF command on the same table row",E);r.trIfCheckMap.set(_e,E)}}}const M=r.loops.length-1,Se=M>=0&&r.loops[M].idx===-1;let ge;if(Se)ge=[];else if(N)ge=await runUserJsAndGetRaw(t,A,r)?[1]:[];else{if(!y)throw new InvalidCommandError("Invalid FOR command",E);if(ge=await runUserJsAndGetRaw(t,y[2],r),!Array.isArray(ge))throw new InvalidCommandError("Invalid FOR command (can only iterate over Array)",E)}r.loops.push({refNode:l,refNodeLevel:r.level,varName:ue,loopOver:ge,isIf:N,idx:-1})}h$1(r.loops)},oe=(t,l,r,E,T)=>{const A=E==="END-IF",N=s(l);if(!N)throw new InvalidCommandError(`Unexpected ${E} outside of ${A?"IF statement":"FOR loop"} context`,r);const y=B$1(t),ue=y!=null?y._fTextNode?null:y._tag:null;ue==="w:p"?l.pIfCheckMap.delete(y):ue==="w:tr"&&l.trIfCheckMap.delete(y),A&&!t._ifName&&(t._ifName=N.varName,l.gCntEndIf+=1);const Z=A?t._ifName:T;if(N.varName!==Z){if(l.loops.find(ge=>ge.varName===Z)==null){logger.debug(`Ignoring ${r} (${Z}, but we're expecting ${N.varName})`);return}throw new InvalidCommandError("Invalid command",r)}const M=N.idx+1,Se=N.loopOver[M];Se!=null?(l.vars[Z]=Se,l.fJump=!0,N.idx=M):l.loops.pop()},J=(t,l)=>{P(l),t.imageAndShapeIdIncrement+=1;const r=`img${String(t.imageAndShapeIdIncrement)}`;return t.images[r]=l,r};function P(t){if(!(t.data instanceof Uint8Array||t.data instanceof ArrayBuffer||typeof t.data=="string"))throw new Error("image .data property needs to be provided as Uint8Array (e.g. Buffer), ArrayBuffer, or as a base64-encoded string");if(!ImageExtensions.includes(t.extension))throw new Error(`An extension (one of ${ImageExtensions}) needs to be provided when providing an image or a thumbnail.`)}function ie(t){if(!Number.isFinite(t.width))throw new Error(`invalid image width: ${t.width} (in cm)`);if(!Number.isFinite(t.height))throw new Error(`invalid image height: ${t.height} (in cm)`);P(t),t.thumbnail&&P(t.thumbnail)}const se=(t,l)=>{var r;ie(l);const E=(l.width*36e4).toFixed(0),T=(l.height*36e4).toFixed(0);let A=J(t,ae(l));const N=String(t.imageAndShapeIdIncrement),y=l.alt||"desc",ue=c$1,Z=[];Z.push(ue("a:ext",{uri:"{28A0092B-C50C-407E-A947-70E740481C1C}"},[ue("a14:useLocalDpi",{"xmlns:a14":"http://schemas.microsoft.com/office/drawing/2010/main",val:"0"})]));const M=l.rotation?(l.rotation*6e4).toString():void 0;if(t.images[A].extension===".svg"){const _e=(r=l.thumbnail)!=null?r:{data:"bm90aGluZwo=",extension:".png"},we=J(t,_e);Z.push(ue("a:ext",{uri:"{96DAC541-7B7A-43D3-8B79-37D633B846F1}"},[ue("asvg:svgBlip",{"xmlns:asvg":"http://schemas.microsoft.com/office/drawing/2016/SVG/main","r:embed":A})])),A=we}const Se=ue("pic:pic",{"xmlns:pic":"http://schemas.openxmlformats.org/drawingml/2006/picture"},[ue("pic:nvPicPr",{},[ue("pic:cNvPr",{id:"0",name:`Picture ${N}`,descr:y}),ue("pic:cNvPicPr",{},[ue("a:picLocks",{noChangeAspect:"1",noChangeArrowheads:"1"})])]),ue("pic:blipFill",{},[ue("a:blip",{"r:embed":A,cstate:"print"},[ue("a:extLst",{},Z)]),ue("a:srcRect"),ue("a:stretch",{},[ue("a:fillRect")])]),ue("pic:spPr",{bwMode:"auto"},[ue("a:xfrm",M?{rot:M}:{},[ue("a:off",{x:"0",y:"0"}),ue("a:ext",{cx:E,cy:T})]),ue("a:prstGeom",{prst:"rect"},[ue("a:avLst")]),ue("a:noFill"),ue("a:ln",{},[ue("a:noFill")])])]),ge=ue("w:drawing",{},[ue("wp:inline",{distT:"0",distB:"0",distL:"0",distR:"0"},[ue("wp:extent",{cx:E,cy:T}),ue("wp:docPr",{id:N,name:`Picture ${N}`,descr:y}),ue("wp:cNvGraphicFramePr",{},[ue("a:graphicFrameLocks",{"xmlns:a":"http://schemas.openxmlformats.org/drawingml/2006/main",noChangeAspect:"1"})]),ue("a:graphic",{"xmlns:a":"http://schemas.openxmlformats.org/drawingml/2006/main"},[ue("a:graphicData",{uri:"http://schemas.openxmlformats.org/drawingml/2006/picture"},[Se])])])]);t.pendingImageNode={image:ge},l.caption&&(t.pendingImageNode.caption=[ue("w:br"),ue("w:t",{},[u(l.caption)])])};function ae(t){const{data:l,extension:r}=t;if(!r)throw new Error("If you return image `data`, make sure you return an extension as well!");return{extension:r,data:l}}const le=async(t,l)=>{const{url:r,label:E=r}=l;t.linkId+=1;const T=`link${String(t.linkId)}`;t.links[T]={url:r};const A=c$1,{textRunPropsNode:N}=t,y=A("w:hyperlink",{"r:id":T,"w:history":"1"},[A("w:r",{},[N||A("w:rPr",{},[A("w:u",{"w:val":"single"})]),A("w:t",{},[u(E)])])]);t.pendingLinkNode=y},fe=async(t,l)=>{t.htmlId+=1;const r=`html${String(t.htmlId)}`;t.htmls[r]=l;const E=c$1("w:altChunk",{"r:id":r});t.pendingHtmlNode=E},de=["w:p","w:tr","w:tc"],R=(t,l,r)=>{if(l.fSeekQuery)return;const{fCmd:E,fInsertedText:T}=r,A=E?"cmds":"text";de.forEach(N=>{const y=l.buffers[N];y[A]+=t,T&&(y.fInsertedText=!0)})};function pe$1(t,l){l.imageAndShapeIdIncrement+=1;const r=String(l.imageAndShapeIdIncrement);t._attrs=x(S({},t._attrs),{id:`${r}`})}const z="+++",D="||",B="[Content_Types].xml",g="word",me=new RegExp(`${g}\\/[^\\/]+\\.xml`);async function parseTemplate(t){const l=await n$1(t),r=await readContentTypes(l),E=getMainDoc(r),T=`${g}/${E}`,A=await s$1(l,T);if(A==null)throw new TemplateParseError(`${E} could not be found`);logger.debug(`${T} file length: ${A.length}`),new Date().getTime();const N=await g$2(A);return new Date().getTime(),{jsTemplate:N,mainDocument:E,zip:l,contentTypes:r}}async function V(t,l,r){const E=[];t.forEach(async A=>{me.test(A)&&A!==`${g}/${l}`&&A.indexOf(`${g}/template`)!==0&&E.push(A)}),E.sort();const T=[];for(const A of E){const N=await s$1(t,A);if(N==null)throw new TemplateParseError(`${A} could not be read`);logger.debug(`${A} file length: ${N.length}`);const y=await g$2(N),ue=g$1(y,r.cmdDelimiter);T.push([ue,A])}return T}async function pe(t,l){const{template:r,data:E,queryVars:T}=t,A=t.literalXmlDelimiter||D,N={cmdDelimiter:te(t.cmdDelimiter),literalXmlDelimiter:A,processLineBreaks:t.processLineBreaks!=null?t.processLineBreaks:!0,noSandbox:t.noSandbox||!1,runJs:t.runJs,additionalJsContext:t.additionalJsContext||{},failFast:t.failFast==null?!0:t.failFast,rejectNullish:t.rejectNullish==null?!1:t.rejectNullish,errorHandler:typeof t.errorHandler=="function"?t.errorHandler:null,fixSmartQuotes:t.fixSmartQuotes==null?!1:t.fixSmartQuotes,processLineBreaksAsNewText:t.processLineBreaksAsNewText==null?!1:t.processLineBreaksAsNewText,maximumWalkingDepth:t.maximumWalkingDepth},y={literalXmlDelimiter:A},{jsTemplate:ue,mainDocument:Z,zip:M,contentTypes:Se}=await parseTemplate(r),ge=g$1(ue,N.cmdDelimiter);let _e=null;if(typeof E=="function"){const Ee=await extractQuery(ge,N);_e=await E(Ee,T)}else _e=E;const we=await V(M,Z,N),be=Math.max(...we.map(([Ee,Pe])=>findHighestImgId(Ee)),findHighestImgId(ge));let Te=newContext(N,be);const Ie=await produceJsReport(_e,ge,Te);if(Ie.status==="errors")throw Ie.errors;const{report:$e,images:De,links:Ce,htmls:ke}=Ie;if(l==="JS")return $e;const Fe=m$1($e,y);if(l==="XML")return Fe.toString("utf-8");o(M,`${g}/${Z}`,Fe);let Oe=Object.keys(De).length,Be=Object.keys(ke).length;await Y(De,Z,M),await K(Ce,Z,M),await ee(ke,Z,M);for(const[Ee,Pe]of we){Te=newContext(N,Te.imageAndShapeIdIncrement);const Re=await produceJsReport(_e,Ee,Te);if(Re.status==="errors")throw Re.errors;const{report:Le,images:Ve,links:Qe,htmls:tt}=Re,at=m$1(Le,y);o(M,Pe,at),Oe+=Object.keys(Ve).length,Be+=Object.keys(tt).length;const Me=Pe.split("/"),nt=Me[Me.length-1];await Y(Ve,nt,M),await K(Qe,Z,M),await ee(tt,Z,M)}if(Oe||Be){const Ee=(Re,Le)=>{Se._children.filter(Ve=>{var Qe;return!Ve._fTextNode&&((Qe=Ve._attrs.Extension)==null?void 0:Qe.toLowerCase())===(Re==null?void 0:Re.toLowerCase())}).length||p(Se,c$1("Default",{Extension:Re,ContentType:Le}))};Oe&&(Ee("png","image/png"),Ee("jpg","image/jpeg"),Ee("jpeg","image/jpeg"),Ee("gif","image/gif"),Ee("bmp","image/bmp"),Ee("svg","image/svg+xml")),Be&&Ee("html","text/html");const Pe=m$1(Se,y);o(M,B,Pe)}return await p$1(M)}async function listCommands(t,l){const r={cmdDelimiter:te(l),literalXmlDelimiter:D,processLineBreaks:!0,noSandbox:!1,additionalJsContext:{},failFast:!1,rejectNullish:!1,errorHandler:null,fixSmartQuotes:!1,processLineBreaksAsNewText:!1},{jsTemplate:E,mainDocument:T,zip:A}=await parseTemplate(t),N=await V(A,T,r),y=[E,...N.map(([Z,M])=>Z)],ue=[];for(const Z of y){const M=g$1(Z,r.cmdDelimiter),Se=newContext(r);await walkTemplate(void 0,M,Se,async(ge,_e,we)=>{const be=getCommand(we.cmd,we.shorthands,we.options.fixSmartQuotes);we.cmd="";const{cmdName:Te,cmdRest:Ie}=splitCommand(be),$e=Te;$e!=null&&$e!=="CMD_NODE"&&ue.push({raw:be,type:$e,code:Ie})})}return ue}async function getMetadata(t){const l="docProps/app.xml",r="docProps/core.xml",E=await n$1(t),T=await F(E,l),A=await F(E,r);function N(Z){if(Z._children.length===0)return;const M=Z._children[0];if(M._fTextNode)return M._text;throw new Error("Not a text node")}function y(Z,M){for(const Se of Z._children)if(!Se._fTextNode&&Se._tag===M)return N(Se)}const ue=Z=>{const M=Number(Z);if(Number.isFinite(M))return M};return{pages:ue(y(T,"Pages")),words:ue(y(T,"Words")),characters:ue(y(T,"Characters")),lines:ue(y(T,"Lines")),paragraphs:ue(y(T,"Paragraphs")),company:y(T,"Company"),template:y(T,"Template"),title:y(A,"dc:title"),subject:y(A,"dc:subject"),creator:y(A,"dc:creator"),description:y(A,"dc:description"),lastModifiedBy:y(A,"cp:lastModifiedBy"),revision:y(A,"cp:revision"),lastPrinted:y(A,"cp:lastPrinted"),created:y(A,"dcterms:created"),modified:y(A,"dcterms:modified"),category:y(A,"cp:category")}}async function F(t,l){const r=await s$1(t,l);if(r==null)throw new TemplateParseError(`${l} could not be read`);const E=await g$2(r);if(E._fTextNode)throw new TemplateParseError(`${l} is a text node when parsed`);return E}async function readContentTypes(t){return await F(t,B)}function getMainDoc(t){const l=["application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml","application/vnd.ms-word.document.macroEnabled.main+xml"];for(const r of t._children)if(!r._fTextNode&&r._attrs.ContentType!=null&&l.includes(r._attrs.ContentType)){const E=r._attrs.PartName;if(E)return E.replace("/word/","")}throw new TemplateParseError(`Could not find main document (e.g. document.xml) in ${B}`)}const Y=async(t,l,r)=>{const E=Object.keys(t);if(!E.length)return;const T=`${g}/_rels/${l}.rels`,A=await H(r,T);for(const y of E){const{extension:ue,data:Z}=t[y],M=`template_${l}_${y}${ue}`,Se=`${g}/media/${M}`;typeof Z=="string"?r.file(Se,Z,{base64:!0}):r.file(Se,Z,{binary:!0}),p(A,c$1("Relationship",{Id:y,Type:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",Target:`media/${M}`}))}const N=m$1(A,{literalXmlDelimiter:D});o(r,T,N)},K=async(t,l,r)=>{const E=Object.keys(t);if(E.length){const T=`${g}/_rels/${l}.rels`,A=await H(r,T);for(const y of E){const{url:ue}=t[y];p(A,c$1("Relationship",{Id:y,Type:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",Target:ue,TargetMode:"External"}))}const N=m$1(A,{literalXmlDelimiter:D});o(r,T,N)}},ee=async(t,l,r)=>{const E=Object.keys(t);if(E.length){const T=`${g}/_rels/${l}.rels`,A=await H(r,T);for(const y of E){const ue=t[y],Z=`template_${l.replace(/\./g,"_")}_${y}.html`,M=`${g}/${Z}`;o(r,M,Buffer.from(ue)),p(A,c$1("Relationship",{Id:y,Type:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/aFChunk",Target:`${Z}`}))}const N=m$1(A,{literalXmlDelimiter:D});o(r,T,N)}},H=async(t,l)=>{let r=await s$1(t,l);return r||(r=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
        <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
        </Relationships>`),g$2(r)},te=t=>t?typeof t=="string"?[t,t]:t:[z,z];export{CommandExecutionError,CommandSyntaxError,ImageError,IncompleteConditionalStatementError,InternalError,InvalidCommandError,NullishCommandResultError,ObjectCommandResultError,TemplateParseError,UnterminatedForLoopError,pe as createReport,getMetadata,isError,listCommands};
//# sourceMappingURL=browser.js.map
