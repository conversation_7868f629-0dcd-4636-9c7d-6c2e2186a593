"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runUserJsAndGetRaw = void 0;
var vm_1 = __importDefault(require("vm"));
var reportUtils_1 = require("./reportUtils");
var errors_1 = require("./errors");
var debug_1 = require("./debug");
// Runs a user snippet in a sandbox, and returns the result.
// The snippet can return a Promise, which is then awaited.
// The sandbox is kept for the execution of snippets later on
// in the template. Sandboxing can also be disabled via
// ctx.options.noSandbox.
function runUserJsAndGetRaw(data, code, ctx) {
    return __awaiter(this, void 0, void 0, function () {
        var sandbox, curLoop, context, result, temp, wrapper, script, err_1, e, nerr;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    sandbox = __assign(__assign(__assign(__assign({}, (ctx.jsSandbox || {})), { __code__: code, __result__: undefined }), data), ctx.options.additionalJsContext);
                    curLoop = (0, reportUtils_1.getCurLoop)(ctx);
                    if (curLoop)
                        sandbox.$idx = curLoop.idx;
                    Object.keys(ctx.vars).forEach(function (varName) {
                        sandbox["$".concat(varName)] = ctx.vars[varName];
                    });
                    _b.label = 1;
                case 1:
                    _b.trys.push([1, 8, , 12]);
                    if (!ctx.options.runJs) return [3 /*break*/, 3];
                    temp = ctx.options.runJs({ sandbox: sandbox, ctx: ctx });
                    context = temp.modifiedSandbox;
                    return [4 /*yield*/, temp.result];
                case 2:
                    result = _b.sent();
                    return [3 /*break*/, 7];
                case 3:
                    if (!ctx.options.noSandbox) return [3 /*break*/, 5];
                    context = sandbox;
                    wrapper = new Function('with(this) { return eval(__code__); }');
                    return [4 /*yield*/, wrapper.call(context)];
                case 4:
                    result = _b.sent();
                    return [3 /*break*/, 7];
                case 5:
                    script = new vm_1.default.Script((_a = sandbox.__code__) !== null && _a !== void 0 ? _a : '');
                    context = vm_1.default.createContext(sandbox);
                    return [4 /*yield*/, script.runInContext(context)];
                case 6:
                    result = _b.sent();
                    _b.label = 7;
                case 7: return [3 /*break*/, 12];
                case 8:
                    err_1 = _b.sent();
                    e = (0, errors_1.isError)(err_1) ? err_1 : new Error("".concat(err_1));
                    if (!(ctx.options.errorHandler != null)) return [3 /*break*/, 10];
                    context = sandbox;
                    return [4 /*yield*/, ctx.options.errorHandler(e, code)];
                case 9:
                    result = _b.sent();
                    return [3 /*break*/, 11];
                case 10: throw new errors_1.CommandExecutionError(e, code);
                case 11: return [3 /*break*/, 12];
                case 12:
                    if (!(ctx.options.rejectNullish && result == null)) return [3 /*break*/, 15];
                    nerr = new errors_1.NullishCommandResultError(code);
                    if (!(ctx.options.errorHandler != null)) return [3 /*break*/, 14];
                    return [4 /*yield*/, ctx.options.errorHandler(nerr, code)];
                case 13:
                    result = _b.sent();
                    return [3 /*break*/, 15];
                case 14: throw nerr;
                case 15:
                    // Save the sandbox for later use, omitting the __code__ and __result__ properties.
                    ctx.jsSandbox = __assign(__assign({}, context), { __code__: undefined, __result__: undefined });
                    debug_1.logger.debug('Command returned: ', result);
                    return [2 /*return*/, result];
            }
        });
    });
}
exports.runUserJsAndGetRaw = runUserJsAndGetRaw;
