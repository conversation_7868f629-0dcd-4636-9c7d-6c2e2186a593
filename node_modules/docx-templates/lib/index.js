"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createReport = exports.getMetadata = exports.listCommands = void 0;
var main_1 = __importDefault(require("./main"));
exports.createReport = main_1.default;
var main_2 = require("./main");
Object.defineProperty(exports, "listCommands", { enumerable: true, get: function () { return main_2.listCommands; } });
Object.defineProperty(exports, "getMetadata", { enumerable: true, get: function () { return main_2.getMetadata; } });
__exportStar(require("./errors"), exports);
exports.default = main_1.default;
