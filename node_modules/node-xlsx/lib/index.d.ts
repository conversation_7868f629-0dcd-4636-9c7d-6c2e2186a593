/// <reference types="node" />
import { AOA2SheetOpts, AutoFilterInfo, ColInfo, ParsingOptions, ProtectInfo, Range, RowInfo, Sheet2JSONOpts, WritingOptions } from 'xlsx';
export declare const parse: <T = any[]>(mixed: unknown, options?: Sheet2JSONOpts & ParsingOptions) => {
    name: string;
    data: T[];
}[];
export declare const parseMetadata: (mixed: unknown, options?: ParsingOptions) => {
    name: string;
    data: Range | null;
}[];
export type WorkSheetOptions = {
    /** Column Info */
    '!cols'?: ColInfo[];
    /** Row Info */
    '!rows'?: RowInfo[];
    /** Merge Ranges */
    '!merges'?: Range[];
    /** Worksheet Protection info */
    '!protect'?: ProtectInfo;
    /** AutoFilter info */
    '!autofilter'?: AutoFilterInfo;
};
export type WorkSheet<T = unknown> = {
    name: string;
    data: T[][];
    options: WorkSheetOptions;
};
export type BuildOptions = WorkSheetOptions & {
    parseOptions?: AOA2SheetOpts;
    writeOptions?: WritingOptions;
    sheetOptions?: WorkSheetOptions;
};
export declare const build: (worksheets: WorkSheet[], { parseOptions, writeOptions, sheetOptions, ...otherOptions }?: BuildOptions) => Buffer;
declare const _default: {
    parse: <T = any[]>(mixed: unknown, options?: Sheet2JSONOpts & ParsingOptions) => {
        name: string;
        data: T[];
    }[];
    parseMetadata: (mixed: unknown, options?: ParsingOptions) => {
        name: string;
        data: Range | null;
    }[];
    build: (worksheets: WorkSheet<unknown>[], { parseOptions, writeOptions, sheetOptions, ...otherOptions }?: BuildOptions) => Buffer;
};
export default _default;
