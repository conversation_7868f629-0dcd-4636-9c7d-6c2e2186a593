/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var XLSX={};function make_xlsx_lib(e){e.version="0.19.3";var r=1200,t=1252;var a;var n=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4];var i={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969};var s=function(e){if(n.indexOf(e)==-1)return;t=i[0]=e};function l(){s(1252)}var o=function(e){r=e;s(e)};function c(){o(1200);l()}function f(e){var r=[];for(var t=0,a=e.length;t<a;++t)r[t]=e.charCodeAt(t);return r}function u(e){var r=[];for(var t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t)+(e.charCodeAt(2*t+1)<<8));return r.join("")}function h(e){var r=[];for(var t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e[2*t]+(e[2*t+1]<<8));return r.join("")}function d(e){var r=[];for(var t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t+1)+(e.charCodeAt(2*t)<<8));return r.join("")}var m=function(e){var r=e.charCodeAt(0),t=e.charCodeAt(1);if(r==255&&t==254)return u(e.slice(2));if(r==254&&t==255)return d(e.slice(2));if(r==65279)return e.slice(1);return e};var p=function Ac(e){return String.fromCharCode(e)};var v=function Ec(e){return String.fromCharCode(e)};function g(e){a=e;o=function(e){r=e;s(e)};m=function(e){if(e.charCodeAt(0)===255&&e.charCodeAt(1)===254){return a.utils.decode(1200,f(e.slice(2)))}return e};p=function n(e){if(r===1200)return String.fromCharCode(e);return a.utils.decode(r,[e&255,e>>8])[0]};v=function i(e){return a.utils.decode(t,[e])[0]};ia()}var b=null;var w=true;var k="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function y(e){var r="";var t=0,a=0,n=0,i=0,s=0,l=0,o=0;for(var c=0;c<e.length;){t=e.charCodeAt(c++);i=t>>2;a=e.charCodeAt(c++);s=(t&3)<<4|a>>4;n=e.charCodeAt(c++);l=(a&15)<<2|n>>6;o=n&63;if(isNaN(a)){l=o=64}else if(isNaN(n)){o=64}r+=k.charAt(i)+k.charAt(s)+k.charAt(l)+k.charAt(o)}return r}function x(e){var r="";var t=0,a=0,n=0,i=0,s=0,l=0,o=0;for(var c=0;c<e.length;){t=e.charCodeAt(c++);if(t>255)t=95;i=t>>2;a=e.charCodeAt(c++);if(a>255)a=95;s=(t&3)<<4|a>>4;n=e.charCodeAt(c++);if(n>255)n=95;l=(a&15)<<2|n>>6;o=n&63;if(isNaN(a)){l=o=64}else if(isNaN(n)){o=64}r+=k.charAt(i)+k.charAt(s)+k.charAt(l)+k.charAt(o)}return r}function S(e){var r="";var t=0,a=0,n=0,i=0,s=0,l=0,o=0;e=e.replace(/^data:([^\/]+\/[^\/]+)?;base64\,/,"").replace(/[^\w\+\/\=]/g,"");for(var c=0;c<e.length;){i=k.indexOf(e.charAt(c++));s=k.indexOf(e.charAt(c++));t=i<<2|s>>4;r+=String.fromCharCode(t);l=k.indexOf(e.charAt(c++));a=(s&15)<<4|l>>2;if(l!==64){r+=String.fromCharCode(a)}o=k.indexOf(e.charAt(c++));n=(l&3)<<6|o;if(o!==64){r+=String.fromCharCode(n)}}return r}var C=function(){return typeof Buffer!=="undefined"&&typeof undefined!=="undefined"&&typeof{}!=="undefined"&&!!{}.node}();var _=function(){if(typeof Buffer!=="undefined"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(r){e=true}return e?function(e,r){return r?new Buffer(e,r):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();var A=function(){if(typeof Buffer==="undefined")return false;var e=_([65,0]);if(!e)return false;var r=e.toString("utf16le");return r.length==1}();function E(e){if(C)return Buffer.alloc?Buffer.alloc(e):new Buffer(e);return typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}function F(e){if(C)return Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e);return typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}var T=function Fc(e){if(C)return _(e,"binary");return e.split("").map(function(e){return e.charCodeAt(0)&255})};function D(e){if(typeof ArrayBuffer==="undefined")return T(e);var r=new ArrayBuffer(e.length),t=new Uint8Array(r);for(var a=0;a!=e.length;++a)t[a]=e.charCodeAt(a)&255;return r}function O(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");var r=[];for(var t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function M(e){if(typeof Uint8Array==="undefined")throw new Error("Unsupported");return new Uint8Array(e)}function N(e){if(typeof ArrayBuffer=="undefined")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return N(new Uint8Array(e));var r=new Array(e.length);for(var t=0;t<e.length;++t)r[t]=e[t];return r}var P=C?function(e){return Buffer.concat(e.map(function(e){return Buffer.isBuffer(e)?e:_(e)}))}:function(e){if(typeof Uint8Array!=="undefined"){var r=0,t=0;for(r=0;r<e.length;++r)t+=e[r].length;var a=new Uint8Array(t);var n=0;for(r=0,t=0;r<e.length;t+=n,++r){n=e[r].length;if(e[r]instanceof Uint8Array)a.set(e[r],t);else if(typeof e[r]=="string")a.set(new Uint8Array(T(e[r])),t);else a.set(new Uint8Array(e[r]),t)}return a}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))};function I(e){var r=[],t=0,a=e.length+250;var n=E(e.length+255);for(var i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)n[t++]=s;else if(s<2048){n[t++]=192|s>>6&31;n[t++]=128|s&63}else if(s>=55296&&s<57344){s=(s&1023)+64;var l=e.charCodeAt(++i)&1023;n[t++]=240|s>>8&7;n[t++]=128|s>>2&63;n[t++]=128|l>>6&15|(s&3)<<4;n[t++]=128|l&63}else{n[t++]=224|s>>12&15;n[t++]=128|s>>6&63;n[t++]=128|s&63}if(t>a){r.push(n.slice(0,t));t=0;n=E(65535);a=65530}}r.push(n.slice(0,t));return P(r)}var R=/\u0000/g,L=/[\u0001-\u0006]/g;function B(e){var r="",t=e.length-1;while(t>=0)r+=e.charAt(t--);return r}function z(e,r){var t=""+e;return t.length>=r?t:_r("0",r-t.length)+t}function W(e,r){var t=""+e;return t.length>=r?t:_r(" ",r-t.length)+t}function U(e,r){var t=""+e;return t.length>=r?t:t+_r(" ",r-t.length)}function j(e,r){var t=""+Math.round(e);return t.length>=r?t:_r("0",r-t.length)+t}function H(e,r){var t=""+e;return t.length>=r?t:_r("0",r-t.length)+t}var V=Math.pow(2,32);function X(e,r){if(e>V||e<-V)return j(e,r);var t=Math.round(e);return H(t,r)}function G(e,r){r=r||0;return e.length>=7+r&&(e.charCodeAt(r)|32)===103&&(e.charCodeAt(r+1)|32)===101&&(e.charCodeAt(r+2)|32)===110&&(e.charCodeAt(r+3)|32)===101&&(e.charCodeAt(r+4)|32)===114&&(e.charCodeAt(r+5)|32)===97&&(e.charCodeAt(r+6)|32)===108}var Y=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]];var J=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function K(e){if(!e)e={};e[0]="General";e[1]="0";e[2]="0.00";e[3]="#,##0";e[4]="#,##0.00";e[9]="0%";e[10]="0.00%";e[11]="0.00E+00";e[12]="# ?/?";e[13]="# ??/??";e[14]="m/d/yy";e[15]="d-mmm-yy";e[16]="d-mmm";e[17]="mmm-yy";e[18]="h:mm AM/PM";e[19]="h:mm:ss AM/PM";e[20]="h:mm";e[21]="h:mm:ss";e[22]="m/d/yy h:mm";e[37]="#,##0 ;(#,##0)";e[38]="#,##0 ;[Red](#,##0)";e[39]="#,##0.00;(#,##0.00)";e[40]="#,##0.00;[Red](#,##0.00)";e[45]="mm:ss";e[46]="[h]:mm:ss";e[47]="mmss.0";e[48]="##0.0E+0";e[49]="@";e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "';return e}var Z={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'};var q={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0};var Q={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function ee(e,r,t){var a=e<0?-1:1;var n=e*a;var i=0,s=1,l=0;var o=1,c=0,f=0;var u=Math.floor(n);while(c<r){u=Math.floor(n);l=u*s+i;f=u*c+o;if(n-u<5e-8)break;n=1/(n-u);i=s;s=l;o=c;c=f}if(f>r){if(c>r){f=o;l=i}else{f=c;l=s}}if(!t)return[0,a*l,f];var h=Math.floor(a*l/f);return[h,a*l-h*f,f]}function re(e,r,t){if(e>2958465||e<0)return null;var a=e|0,n=Math.floor(86400*(e-a)),i=0;var s=[];var l={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(l.u)<1e-6)l.u=0;if(r&&r.date1904)a+=1462;if(l.u>.9999){l.u=0;if(++n==86400){l.T=n=0;++a;++l.D}}if(a===60){s=t?[1317,10,29]:[1900,2,29];i=3}else if(a===0){s=t?[1317,8,29]:[1900,1,0];i=6}else{if(a>60)--a;var o=new Date(1900,0,1);o.setDate(o.getDate()+a-1);s=[o.getFullYear(),o.getMonth()+1,o.getDate()];i=o.getDay();if(a<60)i=(i+6)%7;if(t)i=he(o,s)}l.y=s[0];l.m=s[1];l.d=s[2];l.S=n%60;n=Math.floor(n/60);l.M=n%60;n=Math.floor(n/60);l.H=n;l.q=i;return l}var te=new Date(1899,11,31,0,0,0);var ae=te.getTime();var ne=new Date(1900,2,1,0,0,0);function ie(e,r){var t=e.getTime();if(r)t-=1461*24*60*60*1e3;else if(e>=ne)t+=24*60*60*1e3;return(t-(ae+(e.getTimezoneOffset()-te.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function se(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function le(e){if(e.indexOf("E")==-1)return e;return e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function oe(e){var r=e<0?12:11;var t=se(e.toFixed(12));if(t.length<=r)return t;t=e.toPrecision(10);if(t.length<=r)return t;return e.toExponential(5)}function ce(e){var r=se(e.toFixed(11));return r.length>(e<0?12:11)||r==="0"||r==="-0"?e.toPrecision(6):r}function fe(e){var r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),t;if(r>=-4&&r<=-1)t=e.toPrecision(10+r);else if(Math.abs(r)<=9)t=oe(e);else if(r===10)t=e.toFixed(10).substr(0,12);else t=ce(e);return se(le(t.toUpperCase()))}function ue(e,r){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):fe(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return je(14,ie(e,r&&r.date1904),r);}throw new Error("unsupported value in General format: "+e)}function he(e,r){r[0]-=581;var t=e.getDay();if(e<60)t=(t+6)%7;return t}function de(e,r,t,a){var n="",i=0,s=0,l=t.y,o,c=0;switch(e){case 98:l=t.y+543;case 121:switch(r.length){case 1:;case 2:o=l%100;c=2;break;default:o=l%1e4;c=4;break;}break;case 109:switch(r.length){case 1:;case 2:o=t.m;c=r.length;break;case 3:return J[t.m-1][1];case 5:return J[t.m-1][0];default:return J[t.m-1][2];}break;case 100:switch(r.length){case 1:;case 2:o=t.d;c=r.length;break;case 3:return Y[t.q][0];default:return Y[t.q][1];}break;case 104:switch(r.length){case 1:;case 2:o=1+(t.H+11)%12;c=r.length;break;default:throw"bad hour format: "+r;}break;case 72:switch(r.length){case 1:;case 2:o=t.H;c=r.length;break;default:throw"bad hour format: "+r;}break;case 77:switch(r.length){case 1:;case 2:o=t.M;c=r.length;break;default:throw"bad minute format: "+r;}break;case 115:if(r!="s"&&r!="ss"&&r!=".0"&&r!=".00"&&r!=".000")throw"bad second format: "+r;if(t.u===0&&(r=="s"||r=="ss"))return z(t.S,r.length);if(a>=2)s=a===3?1e3:100;else s=a===1?10:1;i=Math.round(s*(t.S+t.u));if(i>=60*s)i=0;if(r==="s")return i===0?"0":""+i/s;n=z(i,2+a);if(r==="ss")return n.substr(0,2);return"."+n.substr(2,r.length-1);case 90:switch(r){case"[h]":;case"[hh]":o=t.D*24+t.H;break;case"[m]":;case"[mm]":o=(t.D*24+t.H)*60+t.M;break;case"[s]":;case"[ss]":o=((t.D*24+t.H)*60+t.M)*60+Math.round(t.S+t.u);break;default:throw"bad abstime format: "+r;}c=r.length===3?1:2;break;case 101:o=l;c=1;break;}var f=c>0?z(o,c):"";return f}function me(e){var r=3;if(e.length<=r)return e;var t=e.length%r,a=e.substr(0,t);for(;t!=e.length;t+=r)a+=(a.length>0?",":"")+e.substr(t,r);return a}var pe=/%/g;function ve(e,r,t){var a=r.replace(pe,""),n=r.length-a.length;return Ie(e,a,t*Math.pow(10,2*n))+_r("%",n)}function ge(e,r,t){var a=r.length-1;while(r.charCodeAt(a-1)===44)--a;return Ie(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}function be(e,r){var t;var a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(r==0)return"0.0E+0";else if(r<0)return"-"+be(e,-r);var n=e.indexOf(".");if(n===-1)n=e.indexOf("E");var i=Math.floor(Math.log(r)*Math.LOG10E)%n;if(i<0)i+=n;t=(r/Math.pow(10,i)).toPrecision(a+1+(n+i)%n);if(t.indexOf("e")===-1){var s=Math.floor(Math.log(r)*Math.LOG10E);if(t.indexOf(".")===-1)t=t.charAt(0)+"."+t.substr(1)+"E+"+(s-t.length+i);else t+="E+"+(s-i);while(t.substr(0,2)==="0."){t=t.charAt(0)+t.substr(2,n)+"."+t.substr(2+n);t=t.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.")}t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,r,t,a){return r+t+a.substr(0,(n+i)%n)+"."+a.substr(i)+"E"})}else t=r.toExponential(a);if(e.match(/E\+00$/)&&t.match(/e[+-]\d$/))t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1);if(e.match(/E\-/)&&t.match(/e\+/))t=t.replace(/e\+/,"e");return t.replace("e","E")}var we=/# (\?+)( ?)\/( ?)(\d+)/;function ke(e,r,t){var a=parseInt(e[4],10),n=Math.round(r*a),i=Math.floor(n/a);var s=n-i*a,l=a;return t+(i===0?"":""+i)+" "+(s===0?_r(" ",e[1].length+1+e[4].length):W(s,e[1].length)+e[2]+"/"+e[3]+z(l,e[4].length))}function ye(e,r,t){return t+(r===0?"":""+r)+_r(" ",e[1].length+2+e[4].length)}var xe=/^#*0*\.([0#]+)/;var Se=/\).*[0#]/;var Ce=/\(###\) ###\\?-####/;function _e(e){var r="",t;for(var a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t);}return r}function Ae(e,r){var t=Math.pow(10,r);return""+Math.round(e*t)/t}function Ee(e,r){var t=e-Math.floor(e),a=Math.pow(10,r);if(r<(""+Math.round(t*a)).length)return 0;return Math.round(t*a)}function Fe(e,r){if(r<(""+Math.round((e-Math.floor(e))*Math.pow(10,r))).length){return 1}return 0}function Te(e){if(e<2147483647&&e>-2147483648)return""+(e>=0?e|0:e-1|0);return""+Math.floor(e)}function De(e,r,t){if(e.charCodeAt(0)===40&&!r.match(Se)){var a=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");if(t>=0)return De("n",a,t);return"("+De("n",a,-t)+")"}if(r.charCodeAt(r.length-1)===44)return ge(e,r,t);if(r.indexOf("%")!==-1)return ve(e,r,t);if(r.indexOf("E")!==-1)return be(r,t);if(r.charCodeAt(0)===36)return"$"+De(e,r.substr(r.charAt(1)==" "?2:1),t);var n;var i,s,l,o=Math.abs(t),c=t<0?"-":"";if(r.match(/^00+$/))return c+X(o,r.length);if(r.match(/^[#?]+$/)){n=X(t,0);if(n==="0")n="";return n.length>r.length?n:_e(r.substr(0,r.length-n.length))+n}if(i=r.match(we))return ke(i,o,c);if(r.match(/^#+0+$/))return c+X(o,r.length-r.indexOf("0"));if(i=r.match(xe)){n=Ae(t,i[1].length).replace(/^([^\.]+)$/,"$1."+_e(i[1])).replace(/\.$/,"."+_e(i[1])).replace(/\.(\d*)$/,function(e,r){return"."+r+_r("0",_e(i[1]).length-r.length)});return r.indexOf("0.")!==-1?n:n.replace(/^0\./,".")}r=r.replace(/^#+([0.])/,"$1");if(i=r.match(/^(0*)\.(#*)$/)){return c+Ae(o,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".")}if(i=r.match(/^#{1,3},##0(\.?)$/))return c+me(X(o,0));if(i=r.match(/^#,##0\.([#0]*0)$/)){return t<0?"-"+De(e,r,-t):me(""+(Math.floor(t)+Fe(t,i[1].length)))+"."+z(Ee(t,i[1].length),i[1].length)}if(i=r.match(/^#,#*,#0/))return De(e,r.replace(/^#,#*,/,""),t);if(i=r.match(/^([0#]+)(\\?-([0#]+))+$/)){n=B(De(e,r.replace(/[\\-]/g,""),t));s=0;return B(B(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return s<n.length?n.charAt(s++):e==="0"?"0":""}))}if(r.match(Ce)){n=De(e,"##########",t);return"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6)}var f="";if(i=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/)){s=Math.min(i[4].length,7);l=ee(o,Math.pow(10,s)-1,false);n=""+c;f=Ie("n",i[1],l[1]);if(f.charAt(f.length-1)==" ")f=f.substr(0,f.length-1)+"0";n+=f+i[2]+"/"+i[3];f=U(l[2],s);if(f.length<i[4].length)f=_e(i[4].substr(i[4].length-f.length))+f;n+=f;return n}if(i=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/)){s=Math.min(Math.max(i[1].length,i[4].length),7);l=ee(o,Math.pow(10,s)-1,true);return c+(l[0]||(l[1]?"":"0"))+" "+(l[1]?W(l[1],s)+i[2]+"/"+i[3]+U(l[2],s):_r(" ",2*s+1+i[2].length+i[3].length))}if(i=r.match(/^[#0?]+$/)){n=X(t,0);if(r.length<=n.length)return n;return _e(r.substr(0,r.length-n.length))+n}if(i=r.match(/^([#0?]+)\.([#0]+)$/)){n=""+t.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1");s=n.indexOf(".");var u=r.indexOf(".")-s,h=r.length-n.length-u;return _e(r.substr(0,u)+n+r.substr(r.length-h))}if(i=r.match(/^00,000\.([#0]*0)$/)){s=Ee(t,i[1].length);return t<0?"-"+De(e,r,-t):me(Te(t)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?z(0,3-e.length):"")+e})+"."+z(s,i[1].length)}switch(r){case"###,##0.00":return De(e,"#,##0.00",t);case"###,###":;case"##,###":;case"#,###":var d=me(X(o,0));return d!=="0"?c+d:"";case"###,###.00":return De(e,"###,##0.00",t).replace(/^0\./,".");case"#,###.00":return De(e,"#,##0.00",t).replace(/^0\./,".");default:;}throw new Error("unsupported format |"+r+"|")}function Oe(e,r,t){var a=r.length-1;while(r.charCodeAt(a-1)===44)--a;return Ie(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}function Me(e,r,t){var a=r.replace(pe,""),n=r.length-a.length;return Ie(e,a,t*Math.pow(10,2*n))+_r("%",n)}function Ne(e,r){var t;var a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(r==0)return"0.0E+0";else if(r<0)return"-"+Ne(e,-r);var n=e.indexOf(".");if(n===-1)n=e.indexOf("E");var i=Math.floor(Math.log(r)*Math.LOG10E)%n;if(i<0)i+=n;t=(r/Math.pow(10,i)).toPrecision(a+1+(n+i)%n);if(!t.match(/[Ee]/)){var s=Math.floor(Math.log(r)*Math.LOG10E);if(t.indexOf(".")===-1)t=t.charAt(0)+"."+t.substr(1)+"E+"+(s-t.length+i);else t+="E+"+(s-i);t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,r,t,a){return r+t+a.substr(0,(n+i)%n)+"."+a.substr(i)+"E"})}else t=r.toExponential(a);if(e.match(/E\+00$/)&&t.match(/e[+-]\d$/))t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1);if(e.match(/E\-/)&&t.match(/e\+/))t=t.replace(/e\+/,"e");return t.replace("e","E")}function Pe(e,r,t){if(e.charCodeAt(0)===40&&!r.match(Se)){var a=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");if(t>=0)return Pe("n",a,t);return"("+Pe("n",a,-t)+")"}if(r.charCodeAt(r.length-1)===44)return Oe(e,r,t);if(r.indexOf("%")!==-1)return Me(e,r,t);if(r.indexOf("E")!==-1)return Ne(r,t);if(r.charCodeAt(0)===36)return"$"+Pe(e,r.substr(r.charAt(1)==" "?2:1),t);var n;var i,s,l,o=Math.abs(t),c=t<0?"-":"";if(r.match(/^00+$/))return c+z(o,r.length);if(r.match(/^[#?]+$/)){n=""+t;if(t===0)n="";return n.length>r.length?n:_e(r.substr(0,r.length-n.length))+n}if(i=r.match(we))return ye(i,o,c);if(r.match(/^#+0+$/))return c+z(o,r.length-r.indexOf("0"));if(i=r.match(xe)){n=(""+t).replace(/^([^\.]+)$/,"$1."+_e(i[1])).replace(/\.$/,"."+_e(i[1]));n=n.replace(/\.(\d*)$/,function(e,r){return"."+r+_r("0",_e(i[1]).length-r.length)});return r.indexOf("0.")!==-1?n:n.replace(/^0\./,".")}r=r.replace(/^#+([0.])/,"$1");if(i=r.match(/^(0*)\.(#*)$/)){return c+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".")}if(i=r.match(/^#{1,3},##0(\.?)$/))return c+me(""+o);if(i=r.match(/^#,##0\.([#0]*0)$/)){return t<0?"-"+Pe(e,r,-t):me(""+t)+"."+_r("0",i[1].length)}if(i=r.match(/^#,#*,#0/))return Pe(e,r.replace(/^#,#*,/,""),t);if(i=r.match(/^([0#]+)(\\?-([0#]+))+$/)){n=B(Pe(e,r.replace(/[\\-]/g,""),t));s=0;return B(B(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return s<n.length?n.charAt(s++):e==="0"?"0":""}))}if(r.match(Ce)){n=Pe(e,"##########",t);return"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6)}var f="";if(i=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/)){s=Math.min(i[4].length,7);l=ee(o,Math.pow(10,s)-1,false);n=""+c;f=Ie("n",i[1],l[1]);if(f.charAt(f.length-1)==" ")f=f.substr(0,f.length-1)+"0";n+=f+i[2]+"/"+i[3];f=U(l[2],s);if(f.length<i[4].length)f=_e(i[4].substr(i[4].length-f.length))+f;n+=f;return n}if(i=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/)){s=Math.min(Math.max(i[1].length,i[4].length),7);l=ee(o,Math.pow(10,s)-1,true);return c+(l[0]||(l[1]?"":"0"))+" "+(l[1]?W(l[1],s)+i[2]+"/"+i[3]+U(l[2],s):_r(" ",2*s+1+i[2].length+i[3].length))}if(i=r.match(/^[#0?]+$/)){n=""+t;if(r.length<=n.length)return n;return _e(r.substr(0,r.length-n.length))+n}if(i=r.match(/^([#0]+)\.([#0]+)$/)){n=""+t.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1");s=n.indexOf(".");var u=r.indexOf(".")-s,h=r.length-n.length-u;return _e(r.substr(0,u)+n+r.substr(r.length-h))}if(i=r.match(/^00,000\.([#0]*0)$/)){return t<0?"-"+Pe(e,r,-t):me(""+t).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?z(0,3-e.length):"")+e})+"."+z(0,i[1].length)}switch(r){case"###,###":;case"##,###":;case"#,###":var d=me(""+o);return d!=="0"?c+d:"";default:if(r.match(/\.[0#?]*$/))return Pe(e,r.slice(0,r.lastIndexOf(".")),t)+_e(r.slice(r.lastIndexOf(".")));}throw new Error("unsupported format |"+r+"|")}function Ie(e,r,t){return(t|0)===t?Pe(e,r,t):De(e,r,t)}function Re(e){var r=[];var t=false;for(var a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:t=!t;break;case 95:;case 42:;case 92:++a;break;case 59:r[r.length]=e.substr(n,a-n);n=a+1;}r[r.length]=e.substr(n);if(t===true)throw new Error("Format |"+e+"| unterminated string ");return r}var Le=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Be(e){var r=0,t="",a="";while(r<e.length){switch(t=e.charAt(r)){case"G":if(G(e,r))r+=6;r++;break;case'"':for(;e.charCodeAt(++r)!==34&&r<e.length;){}++r;break;case"\\":r+=2;break;case"_":r+=2;break;case"@":++r;break;case"B":;case"b":if(e.charAt(r+1)==="1"||e.charAt(r+1)==="2")return true;case"M":;case"D":;case"Y":;case"H":;case"S":;case"E":;case"m":;case"d":;case"y":;case"h":;case"s":;case"e":;case"g":return true;case"A":;case"a":;case"上":if(e.substr(r,3).toUpperCase()==="A/P")return true;if(e.substr(r,5).toUpperCase()==="AM/PM")return true;if(e.substr(r,5).toUpperCase()==="上午/下午")return true;++r;break;case"[":a=t;while(e.charAt(r++)!=="]"&&r<e.length)a+=e.charAt(r);if(a.match(Le))return true;break;case".":;case"0":;case"#":while(r<e.length&&("0#?.,E+-%".indexOf(t=e.charAt(++r))>-1||t=="\\"&&e.charAt(r+1)=="-"&&"0#".indexOf(e.charAt(r+2))>-1)){}break;case"?":while(e.charAt(++r)===t){}break;case"*":++r;if(e.charAt(r)==" "||e.charAt(r)=="*")++r;break;case"(":;case")":++r;break;case"1":;case"2":;case"3":;case"4":;case"5":;case"6":;case"7":;case"8":;case"9":while(r<e.length&&"0123456789".indexOf(e.charAt(++r))>-1){}break;case" ":++r;break;default:++r;break;}}return false}function ze(e,r,t,a){var n=[],i="",s=0,l="",o="t",c,f,u;var h="H";while(s<e.length){switch(l=e.charAt(s)){case"G":if(!G(e,s))throw new Error("unrecognized character "+l+" in "+e);n[n.length]={t:"G",v:"General"};s+=7;break;case'"':for(i="";(u=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(u);n[n.length]={t:"t",v:i};++s;break;case"\\":var d=e.charAt(++s),m=d==="("||d===")"?d:"t";n[n.length]={t:m,v:d};++s;break;case"_":n[n.length]={t:"t",v:" "};s+=2;break;case"@":n[n.length]={t:"T",v:r};++s;break;case"B":;case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(c==null){c=re(r,t,e.charAt(s+1)==="2");if(c==null)return""}n[n.length]={t:"X",v:e.substr(s,2)};o=l;s+=2;break};case"M":;case"D":;case"Y":;case"H":;case"S":;case"E":l=l.toLowerCase();case"m":;case"d":;case"y":;case"h":;case"s":;case"e":;case"g":if(r<0)return"";if(c==null){c=re(r,t);if(c==null)return""}i=l;while(++s<e.length&&e.charAt(s).toLowerCase()===l)i+=l;if(l==="m"&&o.toLowerCase()==="h")l="M";if(l==="h")l=h;n[n.length]={t:l,v:i};o=l;break;case"A":;case"a":;case"上":var p={t:l,v:l};if(c==null)c=re(r,t);if(e.substr(s,3).toUpperCase()==="A/P"){if(c!=null)p.v=c.H>=12?e.charAt(s+2):l;p.t="T";h="h";s+=3}else if(e.substr(s,5).toUpperCase()==="AM/PM"){if(c!=null)p.v=c.H>=12?"PM":"AM";p.t="T";s+=5;h="h"}else if(e.substr(s,5).toUpperCase()==="上午/下午"){if(c!=null)p.v=c.H>=12?"下午":"上午";p.t="T";s+=5;h="h"}else{p.t="t";++s}if(c==null&&p.t==="T")return"";n[n.length]=p;o=l;break;case"[":i=l;while(e.charAt(s++)!=="]"&&s<e.length)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(Le)){if(c==null){c=re(r,t);if(c==null)return""}n[n.length]={t:"Z",v:i.toLowerCase()};o=i.charAt(1)}else if(i.indexOf("$")>-1){i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$";if(!Be(e))n[n.length]={t:"t",v:i}}break;case".":if(c!=null){i=l;while(++s<e.length&&(l=e.charAt(s))==="0")i+=l;n[n.length]={t:"s",v:i};break};case"0":;case"#":i=l;while(++s<e.length&&"0#?.,E+-%".indexOf(l=e.charAt(s))>-1)i+=l;n[n.length]={t:"n",v:i};break;case"?":i=l;while(e.charAt(++s)===l)i+=l;n[n.length]={t:l,v:i};o=l;break;case"*":++s;if(e.charAt(s)==" "||e.charAt(s)=="*")++s;break;case"(":;case")":n[n.length]={t:a===1?"t":l,v:l};++s;break;case"1":;case"2":;case"3":;case"4":;case"5":;case"6":;case"7":;case"8":;case"9":i=l;while(s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1)i+=e.charAt(s);n[n.length]={t:"D",v:i};break;case" ":n[n.length]={t:l,v:l};++s;break;case"$":n[n.length]={t:"t",v:"$"};++s;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(l)===-1)throw new Error("unrecognized character "+l+" in "+e);n[n.length]={t:"t",v:l};++s;break;}}var v=0,g=0,b;for(s=n.length-1,o="t";s>=0;--s){switch(n[s].t){case"h":;case"H":n[s].t=h;o="h";if(v<1)v=1;break;case"s":if(b=n[s].v.match(/\.0+$/))g=Math.max(g,b[0].length-1);if(v<3)v=3;case"d":;case"y":;case"M":;case"e":o=n[s].t;break;case"m":if(o==="s"){n[s].t="M";if(v<2)v=2}break;case"X":break;case"Z":if(v<1&&n[s].v.match(/[Hh]/))v=1;if(v<2&&n[s].v.match(/[Mm]/))v=2;if(v<3&&n[s].v.match(/[Ss]/))v=3;}}switch(v){case 0:break;case 1:if(c.u>=.5){c.u=0;++c.S}if(c.S>=60){c.S=0;++c.M}if(c.M>=60){c.M=0;++c.H}break;case 2:if(c.u>=.5){c.u=0;++c.S}if(c.S>=60){c.S=0;++c.M}break;}var w="",k;for(s=0;s<n.length;++s){switch(n[s].t){case"t":;case"T":;case" ":;case"D":break;case"X":n[s].v="";n[s].t=";";break;case"d":;case"m":;case"y":;case"h":;case"H":;case"M":;case"s":;case"e":;case"b":;case"Z":n[s].v=de(n[s].t.charCodeAt(0),n[s].v,c,g);n[s].t="t";break;case"n":;case"?":k=s+1;while(n[k]!=null&&((l=n[k].t)==="?"||l==="D"||(l===" "||l==="t")&&n[k+1]!=null&&(n[k+1].t==="?"||n[k+1].t==="t"&&n[k+1].v==="/")||n[s].t==="("&&(l===" "||l==="n"||l===")")||l==="t"&&(n[k].v==="/"||n[k].v===" "&&n[k+1]!=null&&n[k+1].t=="?"))){n[s].v+=n[k].v;n[k]={v:"",t:";"};++k}w+=n[s].v;s=k-1;break;case"G":n[s].t="t";n[s].v=ue(r,t);break;}}var y="",x,S;if(w.length>0){if(w.charCodeAt(0)==40){x=r<0&&w.charCodeAt(0)===45?-r:r;S=Ie("n",w,x)}else{x=r<0&&a>1?-r:r;S=Ie("n",w,x);if(x<0&&n[0]&&n[0].t=="t"){S=S.substr(1);n[0].v="-"+n[0].v}}k=S.length-1;var C=n.length;for(s=0;s<n.length;++s)if(n[s]!=null&&n[s].t!="t"&&n[s].v.indexOf(".")>-1){C=s;break}var _=n.length;if(C===n.length&&S.indexOf("E")===-1){for(s=n.length-1;s>=0;--s){if(n[s]==null||"n?".indexOf(n[s].t)===-1)continue;if(k>=n[s].v.length-1){k-=n[s].v.length;n[s].v=S.substr(k+1,n[s].v.length)}else if(k<0)n[s].v="";else{n[s].v=S.substr(0,k+1);k=-1}n[s].t="t";_=s}if(k>=0&&_<n.length)n[_].v=S.substr(0,k+1)+n[_].v}else if(C!==n.length&&S.indexOf("E")===-1){k=S.indexOf(".")-1;for(s=C;s>=0;--s){if(n[s]==null||"n?".indexOf(n[s].t)===-1)continue;f=n[s].v.indexOf(".")>-1&&s===C?n[s].v.indexOf(".")-1:n[s].v.length-1;y=n[s].v.substr(f+1);for(;f>=0;--f){if(k>=0&&(n[s].v.charAt(f)==="0"||n[s].v.charAt(f)==="#"))y=S.charAt(k--)+y}n[s].v=y;n[s].t="t";_=s}if(k>=0&&_<n.length)n[_].v=S.substr(0,k+1)+n[_].v;k=S.indexOf(".")+1;for(s=C;s<n.length;++s){if(n[s]==null||"n?(".indexOf(n[s].t)===-1&&s!==C)continue;f=n[s].v.indexOf(".")>-1&&s===C?n[s].v.indexOf(".")+1:0;y=n[s].v.substr(0,f);for(;f<n[s].v.length;++f){if(k<S.length)y+=S.charAt(k++)}n[s].v=y;n[s].t="t";_=s}}}for(s=0;s<n.length;++s)if(n[s]!=null&&"n?".indexOf(n[s].t)>-1){x=a>1&&r<0&&s>0&&n[s-1].v==="-"?-r:r;n[s].v=Ie(n[s].t,n[s].v,x);n[s].t="t"}var A="";for(s=0;s!==n.length;++s)if(n[s]!=null)A+=n[s].v;return A}var $e=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function We(e,r){if(r==null)return false;var t=parseFloat(r[2]);switch(r[1]){case"=":if(e==t)return true;break;case">":if(e>t)return true;break;case"<":if(e<t)return true;break;case"<>":if(e!=t)return true;break;case">=":if(e>=t)return true;break;case"<=":if(e<=t)return true;break;}return false}function Ue(e,r){var t=Re(e);var a=t.length,n=t[a-1].indexOf("@");if(a<4&&n>-1)--a;if(t.length>4)throw new Error("cannot find right format for |"+t.join("|")+"|");if(typeof r!=="number")return[4,t.length===4||n>-1?t[t.length-1]:"@"];switch(t.length){case 1:t=n>-1?["General","General","General",t[0]]:[t[0],t[0],t[0],"@"];break;case 2:t=n>-1?[t[0],t[0],t[0],t[1]]:[t[0],t[1],t[0],"@"];break;case 3:t=n>-1?[t[0],t[1],t[0],t[2]]:[t[0],t[1],t[2],"@"];break;case 4:break;}var i=r>0?t[0]:r<0?t[1]:t[2];if(t[0].indexOf("[")===-1&&t[1].indexOf("[")===-1)return[a,i];if(t[0].match(/\[[=<>]/)!=null||t[1].match(/\[[=<>]/)!=null){var s=t[0].match($e);var l=t[1].match($e);return We(r,s)?[a,t[0]]:We(r,l)?[a,t[1]]:[a,t[s!=null&&l!=null?2:1]]}return[a,i]}function je(e,r,t){if(t==null)t={};var a="";switch(typeof e){case"string":if(e=="m/d/yy"&&t.dateNF)a=t.dateNF;else a=e;break;case"number":if(e==14&&t.dateNF)a=t.dateNF;else a=(t.table!=null?t.table:Z)[e];if(a==null)a=t.table&&t.table[q[e]]||Z[q[e]];if(a==null)a=Q[e]||"General";break;}if(G(a,0))return ue(r,t);if(r instanceof Date)r=ie(r,t.date1904);var n=Ue(a,r);if(G(n[1]))return ue(r,t);if(r===true)r="TRUE";else if(r===false)r="FALSE";else if(r===""||r==null)return"";return ze(n[1],r,t,n[0])}function He(e,r){if(typeof r!="number"){r=+r||-1;for(var t=0;t<392;++t){if(Z[t]==undefined){if(r<0)r=t;continue}if(Z[t]==e){r=t;break}}if(r<0)r=391}Z[r]=e;return r}function Ve(e){for(var r=0;r!=392;++r)if(e[r]!==undefined)He(e[r],r)}function Xe(){Z=K()}var Ge={format:je,load:He,_table:Z,load_table:Ve,parse_date_code:re,is_date:Be,get_table:function Tc(){return Ge._table=Z}};var Ye={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"};var Je=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Ke(e){var r=typeof e=="number"?Z[e]:e;r=r.replace(Je,"(\\d+)");return new RegExp("^"+r+"$")}function Ze(e,r,t){var a=-1,n=-1,i=-1,s=-1,l=-1,o=-1;(r.match(Je)||[]).forEach(function(e,r){var c=parseInt(t[r+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=c;break;case"d":i=c;break;case"h":s=c;break;case"s":o=c;break;case"m":if(s>=0)l=c;else n=c;break;}});if(o>=0&&l==-1&&n>=0){l=n;n=-1}var c=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);if(c.length==7)c="0"+c;if(c.length==8)c="20"+c;var f=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(l>=0?l:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);if(s==-1&&l==-1&&o==-1)return c;if(a==-1&&n==-1&&i==-1)return f;return c+"T"+f}var qe={"d.m":"d\\.m"};function Qe(e,r){return He(qe[e]||e,r)}var er=function(){var e={};e.version="1.2.0";function r(){var e=0,r=new Array(256);for(var t=0;t!=256;++t){e=t;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;
e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;r[t]=e}return typeof Int32Array!=="undefined"?new Int32Array(r):r}var t=r();function a(e){var r=0,t=0,a=0,n=typeof Int32Array!=="undefined"?new Int32Array(4096):new Array(4096);for(a=0;a!=256;++a)n[a]=e[a];for(a=0;a!=256;++a){t=e[a];for(r=256+a;r<4096;r+=256)t=n[r]=t>>>8^e[t&255]}var i=[];for(a=1;a!=16;++a)i[a-1]=typeof Int32Array!=="undefined"&&typeof n.subarray=="function"?n.subarray(a*256,a*256+256):n.slice(a*256,a*256+256);return i}var n=a(t);var i=n[0],s=n[1],l=n[2],o=n[3],c=n[4];var f=n[5],u=n[6],h=n[7],d=n[8],m=n[9];var p=n[10],v=n[11],g=n[12],b=n[13],w=n[14];function k(e,r){var a=r^-1;for(var n=0,i=e.length;n<i;)a=a>>>8^t[(a^e.charCodeAt(n++))&255];return~a}function y(e,r){var a=r^-1,n=e.length-15,k=0;for(;k<n;)a=w[e[k++]^a&255]^b[e[k++]^a>>8&255]^g[e[k++]^a>>16&255]^v[e[k++]^a>>>24]^p[e[k++]]^m[e[k++]]^d[e[k++]]^h[e[k++]]^u[e[k++]]^f[e[k++]]^c[e[k++]]^o[e[k++]]^l[e[k++]]^s[e[k++]]^i[e[k++]]^t[e[k++]];n+=15;while(k<n)a=a>>>8^t[(a^e[k++])&255];return~a}function x(e,r){var a=r^-1;for(var n=0,i=e.length,s=0,l=0;n<i;){s=e.charCodeAt(n++);if(s<128){a=a>>>8^t[(a^s)&255]}else if(s<2048){a=a>>>8^t[(a^(192|s>>6&31))&255];a=a>>>8^t[(a^(128|s&63))&255]}else if(s>=55296&&s<57344){s=(s&1023)+64;l=e.charCodeAt(n++)&1023;a=a>>>8^t[(a^(240|s>>8&7))&255];a=a>>>8^t[(a^(128|s>>2&63))&255];a=a>>>8^t[(a^(128|l>>6&15|(s&3)<<4))&255];a=a>>>8^t[(a^(128|l&63))&255]}else{a=a>>>8^t[(a^(224|s>>12&15))&255];a=a>>>8^t[(a^(128|s>>6&63))&255];a=a>>>8^t[(a^(128|s&63))&255]}}return~a}e.table=t;e.bstr=k;e.buf=y;e.str=x;return e}();var rr=function Dc(){var e={};e.version="1.2.2";function r(e,r){var t=e.split("/"),a=r.split("/");for(var n=0,i=0,s=Math.min(t.length,a.length);n<s;++n){if(i=t[n].length-a[n].length)return i;if(t[n]!=a[n])return t[n]<a[n]?-1:1}return t.length-a.length}function t(e){if(e.charAt(e.length-1)=="/")return e.slice(0,-1).indexOf("/")===-1?e:t(e.slice(0,-1));var r=e.lastIndexOf("/");return r===-1?e:e.slice(0,r+1)}function a(e){if(e.charAt(e.length-1)=="/")return a(e.slice(0,-1));var r=e.lastIndexOf("/");return r===-1?e:e.slice(r+1)}function n(e,r){if(typeof r==="string")r=new Date(r);var t=r.getHours();t=t<<6|r.getMinutes();t=t<<5|r.getSeconds()>>>1;e._W(2,t);var a=r.getFullYear()-1980;a=a<<4|r.getMonth()+1;a=a<<5|r.getDate();e._W(2,a)}function i(e){var r=e._R(2)&65535;var t=e._R(2)&65535;var a=new Date;var n=t&31;t>>>=5;var i=t&15;t>>>=4;a.setMilliseconds(0);a.setFullYear(t+1980);a.setMonth(i-1);a.setDate(n);var s=r&31;r>>>=5;var l=r&63;r>>>=6;a.setHours(r);a.setMinutes(l);a.setSeconds(s<<1);return a}function s(e){ba(e,0);var r={};var t=0;while(e.l<=e.length-4){var a=e._R(2);var n=e._R(2),i=e.l+n;var s={};switch(a){case 21589:{t=e._R(1);if(t&1)s.mtime=e._R(4);if(n>5){if(t&2)s.atime=e._R(4);if(t&4)s.ctime=e._R(4)}if(s.mtime)s.mt=new Date(s.mtime*1e3)}break;case 1:{var l=e._R(4),o=e._R(4);s.usz=o*Math.pow(2,32)+l;l=e._R(4);o=e._R(4);s.csz=o*Math.pow(2,32)+l}break;}e.l=i;r[a]=s}return r}var l;function o(){return l||(l=tr)}function c(e,r){if(e[0]==80&&e[1]==75)return Oe(e,r);if((e[0]|32)==109&&(e[1]|32)==105)return $e(e,r);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var t=3;var a=512;var n=0;var i=0;var s=0;var l=0;var o=0;var c=[];var m=e.slice(0,512);ba(m,0);var v=f(m);t=v[0];switch(t){case 3:a=512;break;case 4:a=4096;break;case 0:if(v[1]==0)return Oe(e,r);default:throw new Error("Major Version: Expected 3 or 4 saw "+t);}if(a!==512){m=e.slice(0,a);ba(m,28)}var w=e.slice(0,a);u(m,t);var k=m._R(4,"i");if(t===3&&k!==0)throw new Error("# Directory Sectors: Expected 0 saw "+k);m.l+=4;s=m._R(4,"i");m.l+=4;m.chk("00100000","Mini Stream Cutoff Size: ");l=m._R(4,"i");n=m._R(4,"i");o=m._R(4,"i");i=m._R(4,"i");for(var y=-1,x=0;x<109;++x){y=m._R(4,"i");if(y<0)break;c[x]=y}var S=h(e,a);p(o,i,S,a,c);var C=g(S,s,c,a);if(s<C.length)C[s].name="!Directory";if(n>0&&l!==B)C[l].name="!MiniFAT";C[c[0]].name="!FAT";C.fat_addrs=c;C.ssz=a;var _={},A=[],E=[],F=[];b(s,C,S,A,n,_,E,l);d(E,F,A);A.shift();var T={FileIndex:E,FullPaths:F};if(r&&r.raw)T.raw={header:w,sectors:S};return T}function f(e){if(e[e.l]==80&&e[e.l+1]==75)return[0,0];e.chk(z,"Header Signature: ");e.l+=16;var r=e._R(2,"u");return[e._R(2,"u"),r]}function u(e,r){var t=9;e.l+=2;switch(t=e._R(2)){case 9:if(r!=3)throw new Error("Sector Shift: Expected 9 saw "+t);break;case 12:if(r!=4)throw new Error("Sector Shift: Expected 12 saw "+t);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+t);}e.chk("0600","Mini Sector Shift: ");e.chk("000000000000","Reserved: ")}function h(e,r){var t=Math.ceil(e.length/r)-1;var a=[];for(var n=1;n<t;++n)a[n-1]=e.slice(n*r,(n+1)*r);a[t-1]=e.slice(t*r);return a}function d(e,r,t){var a=0,n=0,i=0,s=0,l=0,o=t.length;var c=[],f=[];for(;a<o;++a){c[a]=f[a]=a;r[a]=t[a]}for(;l<f.length;++l){a=f[l];n=e[a].L;i=e[a].R;s=e[a].C;if(c[a]===a){if(n!==-1&&c[n]!==n)c[a]=c[n];if(i!==-1&&c[i]!==i)c[a]=c[i]}if(s!==-1)c[s]=a;if(n!==-1&&a!=c[a]){c[n]=c[a];if(f.lastIndexOf(n)<l)f.push(n)}if(i!==-1&&a!=c[a]){c[i]=c[a];if(f.lastIndexOf(i)<l)f.push(i)}}for(a=1;a<o;++a)if(c[a]===a){if(i!==-1&&c[i]!==i)c[a]=c[i];else if(n!==-1&&c[n]!==n)c[a]=c[n]}for(a=1;a<o;++a){if(e[a].type===0)continue;l=a;if(l!=c[l])do{l=c[l];r[a]=r[l]+"/"+r[a]}while(l!==0&&-1!==c[l]&&l!=c[l]);c[a]=-1}r[0]+="/";for(a=1;a<o;++a){if(e[a].type!==2)r[a]+="/"}}function m(e,r,t){var a=e.start,n=e.size;var i=[];var s=a;while(t&&n>0&&s>=0){i.push(r.slice(s*I,s*I+I));n-=I;s=fa(t,s*4)}if(i.length===0)return ka(0);return P(i).slice(0,e.size)}function p(e,r,t,a,n){var i=B;if(e===B){if(r!==0)throw new Error("DIFAT chain shorter than expected")}else if(e!==-1){var s=t[e],l=(a>>>2)-1;if(!s)return;for(var o=0;o<l;++o){if((i=fa(s,o*4))===B)break;n.push(i)}if(r>=1)p(fa(s,a-4),r-1,t,a,n)}}function v(e,r,t,a,n){var i=[],s=[];if(!n)n=[];var l=a-1,o=0,c=0;for(o=r;o>=0;){n[o]=true;i[i.length]=o;s.push(e[o]);var f=t[Math.floor(o*4/a)];c=o*4&l;if(a<4+c)throw new Error("FAT boundary crossed: "+o+" 4 "+a);if(!e[f])break;o=fa(e[f],c)}return{nodes:i,data:zt([s])}}function g(e,r,t,a){var n=e.length,i=[];var s=[],l=[],o=[];var c=a-1,f=0,u=0,h=0,d=0;for(f=0;f<n;++f){l=[];h=f+r;if(h>=n)h-=n;if(s[h])continue;o=[];var m=[];for(u=h;u>=0;){m[u]=true;s[u]=true;l[l.length]=u;o.push(e[u]);var p=t[Math.floor(u*4/a)];d=u*4&c;if(a<4+d)throw new Error("FAT boundary crossed: "+u+" 4 "+a);if(!e[p])break;u=fa(e[p],d);if(m[u])break}i[h]={nodes:l,data:zt([o])}}return i}function b(e,r,t,a,n,i,s,l){var o=0,c=a.length?2:0;var f=r[e].data;var u=0,h=0,d;for(;u<f.length;u+=128){var p=f.slice(u,u+128);ba(p,64);h=p._R(2);d=Wt(p,0,h-c);a.push(d);var g={name:d,type:p._R(1),color:p._R(1),L:p._R(4,"i"),R:p._R(4,"i"),C:p._R(4,"i"),clsid:p._R(16),state:p._R(4,"i"),start:0,size:0};var b=p._R(2)+p._R(2)+p._R(2)+p._R(2);if(b!==0)g.ct=w(p,p.l-8);var k=p._R(2)+p._R(2)+p._R(2)+p._R(2);if(k!==0)g.mt=w(p,p.l-8);g.start=p._R(4,"i");g.size=p._R(4,"i");if(g.size<0&&g.start<0){g.size=g.type=0;g.start=B;g.name=""}if(g.type===5){o=g.start;if(n>0&&o!==B)r[o].name="!StreamData"}else if(g.size>=4096){g.storage="fat";if(r[g.start]===undefined)r[g.start]=v(t,g.start,r.fat_addrs,r.ssz);r[g.start].name=g.name;g.content=r[g.start].data.slice(0,g.size)}else{g.storage="minifat";if(g.size<0)g.size=0;else if(o!==B&&g.start!==B&&r[o]){g.content=m(g,r[o].data,(r[l]||{}).data)}}if(g.content)ba(g.content,0);i[d]=g;s.push(g)}}function w(e,r){return new Date((ca(e,r+4)/1e7*Math.pow(2,32)+ca(e,r)/1e7-11644473600)*1e3)}function k(e,r){o();return c(l.readFileSync(e),r)}function x(e,r){var t=r&&r.type;if(!t){if(C&&Buffer.isBuffer(e))t="buffer"}switch(t||"base64"){case"file":return k(e,r);case"base64":return c(T(S(e)),r);case"binary":return c(T(e),r);}return c(e,r)}function A(e,r){var t=r||{},a=t.root||"Root Entry";if(!e.FullPaths)e.FullPaths=[];if(!e.FileIndex)e.FileIndex=[];if(e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");if(e.FullPaths.length===0){e.FullPaths[0]=a+"/";e.FileIndex[0]={name:a,type:5}}if(t.CLSID)e.FileIndex[0].clsid=t.CLSID;D(e)}function D(e){var r="Sh33tJ5";if(rr.find(e,"/"+r))return;var t=ka(4);t[0]=55;t[1]=t[3]=50;t[2]=54;e.FileIndex.push({name:r,type:2,content:t,size:4,L:69,R:69,C:69});e.FullPaths.push(e.FullPaths[0]+r);O(e)}function O(e,n){A(e);var i=false,s=false;for(var l=e.FullPaths.length-1;l>=0;--l){var o=e.FileIndex[l];switch(o.type){case 0:if(s)i=true;else{e.FileIndex.pop();e.FullPaths.pop()}break;case 1:;case 2:;case 5:s=true;if(isNaN(o.R*o.L*o.C))i=true;if(o.R>-1&&o.L>-1&&o.R==o.L)i=true;break;default:i=true;break;}}if(!i&&!n)return;var c=new Date(1987,1,19),f=0;var u=Object.create?Object.create(null):{};var h=[];for(l=0;l<e.FullPaths.length;++l){u[e.FullPaths[l]]=true;if(e.FileIndex[l].type===0)continue;h.push([e.FullPaths[l],e.FileIndex[l]])}for(l=0;l<h.length;++l){var d=t(h[l][0]);s=u[d];while(!s){while(t(d)&&!u[t(d)])d=t(d);h.push([d,{name:a(d).replace("/",""),type:1,clsid:W,ct:c,mt:c,content:null}]);u[d]=true;d=t(h[l][0]);s=u[d]}}h.sort(function(e,t){return r(e[0],t[0])});e.FullPaths=[];e.FileIndex=[];for(l=0;l<h.length;++l){e.FullPaths[l]=h[l][0];e.FileIndex[l]=h[l][1]}for(l=0;l<h.length;++l){var m=e.FileIndex[l];var p=e.FullPaths[l];m.name=a(p).replace("/","");m.L=m.R=m.C=-(m.color=1);m.size=m.content?m.content.length:0;m.start=0;m.clsid=m.clsid||W;if(l===0){m.C=h.length>1?1:-1;m.size=0;m.type=5}else if(p.slice(-1)=="/"){for(f=l+1;f<h.length;++f)if(t(e.FullPaths[f])==p)break;m.C=f>=h.length?-1:f;for(f=l+1;f<h.length;++f)if(t(e.FullPaths[f])==t(p))break;m.R=f>=h.length?-1:f;m.type=1}else{if(t(e.FullPaths[l+1]||"")==t(p))m.R=l+1;m.type=2}}}function M(e,r){var t=r||{};if(t.fileType=="mad")return We(e,t);O(e);switch(t.fileType){case"zip":return Ne(e,t);}var a=function(e){var r=0,t=0;for(var a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(!n.content)continue;var i=n.content.length;if(i>0){if(i<4096)r+=i+63>>6;else t+=i+511>>9}}var s=e.FullPaths.length+3>>2;var l=r+7>>3;var o=r+127>>7;var c=l+t+s+o;var f=c+127>>7;var u=f<=109?0:Math.ceil((f-109)/127);while(c+f+u+127>>7>f)u=++f<=109?0:Math.ceil((f-109)/127);var h=[1,u,f,o,s,t,r,0];e.FileIndex[0].size=r<<6;h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3);return h}(e);var n=ka(a[7]<<9);var i=0,s=0;{for(i=0;i<8;++i)n._W(1,$[i]);for(i=0;i<8;++i)n._W(2,0);n._W(2,62);n._W(2,3);n._W(2,65534);n._W(2,9);n._W(2,6);for(i=0;i<3;++i)n._W(2,0);n._W(4,0);n._W(4,a[2]);n._W(4,a[0]+a[1]+a[2]+a[3]-1);n._W(4,0);n._W(4,1<<12);n._W(4,a[3]?a[0]+a[1]+a[2]-1:B);n._W(4,a[3]);n._W(-4,a[1]?a[0]-1:B);n._W(4,a[1]);for(i=0;i<109;++i)n._W(-4,i<a[2]?a[1]+i:-1)}if(a[1]){for(s=0;s<a[1];++s){for(;i<236+s*127;++i)n._W(-4,i<a[2]?a[1]+i:-1);n._W(-4,s===a[1]-1?B:s+1)}}var l=function(e){for(s+=e;i<s-1;++i)n._W(-4,i+1);if(e){++i;n._W(-4,B)}};s=i=0;for(s+=a[1];i<s;++i)n._W(-4,U.DIFSECT);for(s+=a[2];i<s;++i)n._W(-4,U.FATSECT);l(a[3]);l(a[4]);var o=0,c=0;var f=e.FileIndex[0];for(;o<e.FileIndex.length;++o){f=e.FileIndex[o];if(!f.content)continue;c=f.content.length;if(c<4096)continue;f.start=s;l(c+511>>9)}l(a[6]+7>>3);while(n.l&511)n._W(-4,U.ENDOFCHAIN);s=i=0;for(o=0;o<e.FileIndex.length;++o){f=e.FileIndex[o];if(!f.content)continue;c=f.content.length;if(!c||c>=4096)continue;f.start=s;l(c+63>>6)}while(n.l&511)n._W(-4,U.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var u=e.FullPaths[i];if(!u||u.length===0){for(o=0;o<17;++o)n._W(4,0);for(o=0;o<3;++o)n._W(4,-1);for(o=0;o<12;++o)n._W(4,0);continue}f=e.FileIndex[i];if(i===0)f.start=f.size?f.start-1:B;var h=i===0&&t.root||f.name;if(h.length>32){console.error("Name "+h+" will be truncated to "+h.slice(0,32));h=h.slice(0,32)}c=2*(h.length+1);n._W(64,h,"utf16le");n._W(2,c);n._W(1,f.type);n._W(1,f.color);n._W(-4,f.L);n._W(-4,f.R);n._W(-4,f.C);if(!f.clsid)for(o=0;o<4;++o)n._W(4,0);else n._W(16,f.clsid,"hex");n._W(4,f.state||0);n._W(4,0);n._W(4,0);n._W(4,0);n._W(4,0);n._W(4,f.start);n._W(4,f.size);n._W(4,0)}for(i=1;i<e.FileIndex.length;++i){f=e.FileIndex[i];if(f.size>=4096){n.l=f.start+1<<9;if(C&&Buffer.isBuffer(f.content)){f.content.copy(n,n.l,0,f.size);n.l+=f.size+511&-512}else{for(o=0;o<f.size;++o)n._W(1,f.content[o]);for(;o&511;++o)n._W(1,0)}}}for(i=1;i<e.FileIndex.length;++i){f=e.FileIndex[i];if(f.size>0&&f.size<4096){if(C&&Buffer.isBuffer(f.content)){f.content.copy(n,n.l,0,f.size);n.l+=f.size+63&-64}else{for(o=0;o<f.size;++o)n._W(1,f.content[o]);for(;o&63;++o)n._W(1,0)}}}if(C){n.l=n.length}else{while(n.l<n.length)n._W(1,0)}return n}function N(e,r){var t=e.FullPaths.map(function(e){return e.toUpperCase()});var a=t.map(function(e){var r=e.split("/");return r[r.length-(e.slice(-1)=="/"?2:1)]});var n=false;if(r.charCodeAt(0)===47){n=true;r=t[0].slice(0,-1)+r}else n=r.indexOf("/")!==-1;var i=r.toUpperCase();var s=n===true?t.indexOf(i):a.indexOf(i);if(s!==-1)return e.FileIndex[s];var l=!i.match(L);i=i.replace(R,"");if(l)i=i.replace(L,"!");for(s=0;s<t.length;++s){if((l?t[s].replace(L,"!"):t[s]).replace(R,"")==i)return e.FileIndex[s];if((l?a[s].replace(L,"!"):a[s]).replace(R,"")==i)return e.FileIndex[s]}return null}var I=64;var B=-2;var z="d0cf11e0a1b11ae1";var $=[208,207,17,224,161,177,26,225];var W="00000000000000000000000000000000";var U={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:B,FREESECT:-1,HEADER_SIGNATURE:z,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:W,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function j(e,r,t){o();var a=M(e,t);l.writeFileSync(r,a)}function H(e){var r=new Array(e.length);for(var t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function V(e,r){var t=M(e,r);switch(r&&r.type||"buffer"){case"file":o();l.writeFileSync(r.filename,t);return t;case"binary":return typeof t=="string"?t:H(t);case"base64":return y(typeof t=="string"?t:H(t));case"buffer":if(C)return Buffer.isBuffer(t)?t:_(t);case"array":return typeof t=="string"?T(t):t;}return t}var X;function G(e){try{var r=e.InflateRaw;var t=new r;t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag);if(t.bytesRead)X=e;else throw new Error("zlib does not expose bytesRead")}catch(a){console.error("cannot use native zlib: "+(a.message||a))}}function Y(e,r){if(!X)return Te(e,r);var t=X.InflateRaw;var a=new t;var n=a._processChunk(e.slice(e.l),a._finishFlushFlag);e.l+=a.bytesRead;return n}function J(e){return X?X.deflateRawSync(e):ye(e)}var K=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];var Z=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258];var q=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function Q(e){var r=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(r>>16|r>>8|r)&255}var ee=typeof Uint8Array!=="undefined";var re=ee?new Uint8Array(1<<8):[];for(var te=0;te<1<<8;++te)re[te]=Q(te);function ae(e,r){var t=re[e&255];if(r<=8)return t>>>8-r;t=t<<8|re[e>>8&255];if(r<=16)return t>>>16-r;t=t<<8|re[e>>16&255];return t>>>24-r}function ne(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=6?0:e[a+1]<<8))>>>t&3}function ie(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=5?0:e[a+1]<<8))>>>t&7}function se(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=4?0:e[a+1]<<8))>>>t&15}function le(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=3?0:e[a+1]<<8))>>>t&31}function oe(e,r){var t=r&7,a=r>>>3;return(e[a]|(t<=1?0:e[a+1]<<8))>>>t&127}function ce(e,r,t){var a=r&7,n=r>>>3,i=(1<<t)-1;var s=e[n]>>>a;if(t<8-a)return s&i;s|=e[n+1]<<8-a;if(t<16-a)return s&i;s|=e[n+2]<<16-a;if(t<24-a)return s&i;s|=e[n+3]<<24-a;return s&i}function fe(e,r,t){var a=r&7,n=r>>>3;if(a<=5)e[n]|=(t&7)<<a;else{e[n]|=t<<a&255;e[n+1]=(t&7)>>8-a}return r+3}function ue(e,r,t){var a=r&7,n=r>>>3;t=(t&1)<<a;e[n]|=t;return r+1}function he(e,r,t){var a=r&7,n=r>>>3;t<<=a;e[n]|=t&255;t>>>=8;e[n+1]=t;return r+8}function de(e,r,t){var a=r&7,n=r>>>3;t<<=a;e[n]|=t&255;t>>>=8;e[n+1]=t&255;e[n+2]=t>>>8;return r+16}function me(e,r){var t=e.length,a=2*t>r?2*t:r+5,n=0;if(t>=r)return e;if(C){var i=F(a);if(e.copy)e.copy(i);else for(;n<e.length;++n)i[n]=e[n];return i}else if(ee){var s=new Uint8Array(a);if(s.set)s.set(e);else for(;n<t;++n)s[n]=e[n];return s}e.length=a;return e}function pe(e){var r=new Array(e);for(var t=0;t<e;++t)r[t]=0;return r}function ve(e,r,t){var a=1,n=0,i=0,s=0,l=0,o=e.length;var c=ee?new Uint16Array(32):pe(32);for(i=0;i<32;++i)c[i]=0;for(i=o;i<t;++i)e[i]=0;o=e.length;var f=ee?new Uint16Array(o):pe(o);for(i=0;i<o;++i){c[n=e[i]]++;if(a<n)a=n;f[i]=0}c[0]=0;for(i=1;i<=a;++i)c[i+16]=l=l+c[i-1]<<1;for(i=0;i<o;++i){l=e[i];if(l!=0)f[i]=c[l+16]++}var u=0;for(i=0;i<o;++i){u=e[i];if(u!=0){l=ae(f[i],a)>>a-u;for(s=(1<<a+4-u)-1;s>=0;--s)r[l|s<<u]=u&15|i<<4}}return a}var ge=ee?new Uint16Array(512):pe(512);var be=ee?new Uint16Array(32):pe(32);if(!ee){for(var we=0;we<512;++we)ge[we]=0;for(we=0;we<32;++we)be[we]=0}(function(){var e=[];var r=0;for(;r<32;r++)e.push(5);ve(e,be,32);var t=[];r=0;for(;r<=143;r++)t.push(8);for(;r<=255;r++)t.push(9);for(;r<=279;r++)t.push(7);for(;r<=287;r++)t.push(8);ve(t,ge,288)})();var ke=function Ge(){var e=ee?new Uint8Array(32768):[];var r=0,t=0;for(;r<q.length-1;++r){for(;t<q[r+1];++t)e[t]=r}for(;t<32768;++t)e[t]=29;var a=ee?new Uint8Array(259):[];for(r=0,t=0;r<Z.length-1;++r){for(;t<Z[r+1];++t)a[t]=r}function n(e,r){var t=0;while(t<e.length){var a=Math.min(65535,e.length-t);var n=t+a==e.length;r._W(1,+n);r._W(2,a);r._W(2,~a&65535);while(a-- >0)r[r.l++]=e[t++]}return r.l}function i(r,t){var n=0;var i=0;var s=ee?new Uint16Array(32768):[];while(i<r.length){var l=Math.min(65535,r.length-i);if(l<10){n=fe(t,n,+!!(i+l==r.length));if(n&7)n+=8-(n&7);t.l=n/8|0;t._W(2,l);t._W(2,~l&65535);while(l-- >0)t[t.l++]=r[i++];n=t.l*8;continue}n=fe(t,n,+!!(i+l==r.length)+2);var o=0;while(l-- >0){var c=r[i];o=(o<<5^c)&32767;var f=-1,u=0;if(f=s[o]){f|=i&~32767;if(f>i)f-=32768;if(f<i)while(r[f+u]==r[i+u]&&u<250)++u}if(u>2){c=a[u];if(c<=22)n=he(t,n,re[c+1]>>1)-1;else{he(t,n,3);n+=5;he(t,n,re[c-23]>>5);n+=3}var h=c<8?0:c-4>>2;if(h>0){de(t,n,u-Z[c]);n+=h}c=e[i-f];n=he(t,n,re[c]>>3);n-=3;var d=c<4?0:c-2>>1;if(d>0){de(t,n,i-f-q[c]);n+=d}for(var m=0;m<u;++m){s[o]=i&32767;o=(o<<5^r[i])&32767;++i}l-=u-1}else{if(c<=143)c=c+48;else n=ue(t,n,1);n=he(t,n,re[c]);s[o]=i&32767;++i}}n=he(t,n,0)-1}t.l=(n+7)/8|0;return t.l}return function s(e,r){if(e.length<8)return n(e,r);return i(e,r)}}();function ye(e){var r=ka(50+Math.floor(e.length*1.1));var t=ke(e,r);return r.slice(0,t)}var xe=ee?new Uint16Array(32768):pe(32768);var Se=ee?new Uint16Array(32768):pe(32768);var Ce=ee?new Uint16Array(128):pe(128);var _e=1,Ae=1;function Ee(e,r){var t=le(e,r)+257;r+=5;var a=le(e,r)+1;r+=5;var n=se(e,r)+4;r+=4;var i=0;var s=ee?new Uint8Array(19):pe(19);var l=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];var o=1;var c=ee?new Uint8Array(8):pe(8);var f=ee?new Uint8Array(8):pe(8);var u=s.length;for(var h=0;h<n;++h){s[K[h]]=i=ie(e,r);if(o<i)o=i;c[i]++;r+=3}var d=0;c[0]=0;for(h=1;h<=o;++h)f[h]=d=d+c[h-1]<<1;for(h=0;h<u;++h)if((d=s[h])!=0)l[h]=f[d]++;var m=0;for(h=0;h<u;++h){m=s[h];if(m!=0){d=re[l[h]]>>8-m;for(var p=(1<<7-m)-1;p>=0;--p)Ce[d|p<<m]=m&7|h<<3}}var v=[];o=1;for(;v.length<t+a;){d=Ce[oe(e,r)];r+=d&7;switch(d>>>=3){case 16:i=3+ne(e,r);r+=2;d=v[v.length-1];while(i-- >0)v.push(d);break;case 17:i=3+ie(e,r);r+=3;while(i-- >0)v.push(0);break;case 18:i=11+oe(e,r);r+=7;while(i-- >0)v.push(0);break;default:v.push(d);if(o<d)o=d;break;}}var g=v.slice(0,t),b=v.slice(t);for(h=t;h<286;++h)g[h]=0;for(h=a;h<30;++h)b[h]=0;_e=ve(g,xe,286);Ae=ve(b,Se,30);return r}function Fe(e,r){if(e[0]==3&&!(e[1]&3)){return[E(r),2]}var t=0;var a=0;var n=F(r?r:1<<18);var i=0;var s=n.length>>>0;var l=0,o=0;while((a&1)==0){a=ie(e,t);t+=3;if(a>>>1==0){if(t&7)t+=8-(t&7);var c=e[t>>>3]|e[(t>>>3)+1]<<8;t+=32;if(c>0){if(!r&&s<i+c){n=me(n,i+c);s=n.length}while(c-- >0){n[i++]=e[t>>>3];t+=8}}continue}else if(a>>1==1){l=9;o=5}else{t=Ee(e,t);l=_e;o=Ae}for(;;){if(!r&&s<i+32767){n=me(n,i+32767);s=n.length}var f=ce(e,t,l);var u=a>>>1==1?ge[f]:xe[f];t+=u&15;u>>>=4;if((u>>>8&255)===0)n[i++]=u;else if(u==256)break;else{u-=257;var h=u<8?0:u-4>>2;if(h>5)h=0;var d=i+Z[u];if(h>0){d+=ce(e,t,h);t+=h}f=ce(e,t,o);u=a>>>1==1?be[f]:Se[f];t+=u&15;u>>>=4;var m=u<4?0:u-2>>1;var p=q[u];if(m>0){p+=ce(e,t,m);t+=m}if(!r&&s<d){n=me(n,d+100);s=n.length}while(i<d){n[i]=n[i-p];++i}}}}if(r)return[n,t+7>>>3];return[n.slice(0,i),t+7>>>3]}function Te(e,r){var t=e.slice(e.l||0);var a=Fe(t,r);e.l+=a[1];return a[0]}function De(e,r){if(e){if(typeof console!=="undefined")console.error(r)}else throw new Error(r)}function Oe(e,r){var t=e;ba(t,0);var a=[],n=[];var i={FileIndex:a,FullPaths:n};A(i,{root:r.root});var l=t.length-4;while((t[l]!=80||t[l+1]!=75||t[l+2]!=5||t[l+3]!=6)&&l>=0)--l;t.l=l+4;t.l+=4;var o=t._R(2);t.l+=6;var c=t._R(4);t.l=c;for(l=0;l<o;++l){t.l+=20;var f=t._R(4);var u=t._R(4);var h=t._R(2);var d=t._R(2);var m=t._R(2);t.l+=8;var p=t._R(4);var v=s(t.slice(t.l+h,t.l+h+d));t.l+=h+d+m;var g=t.l;t.l=p+4;if(v&&v[1]){if((v[1]||{}).usz)u=v[1].usz;if((v[1]||{}).csz)f=v[1].csz}Me(t,f,u,i,v);t.l=g}return i}function Me(e,r,t,a,n){e.l+=2;var l=e._R(2);var o=e._R(2);var c=i(e);if(l&8257)throw new Error("Unsupported ZIP encryption");var f=e._R(4);var u=e._R(4);var h=e._R(4);var d=e._R(2);var m=e._R(2);var p="";for(var v=0;v<d;++v)p+=String.fromCharCode(e[e.l++]);if(m){var g=s(e.slice(e.l,e.l+m));if((g[21589]||{}).mt)c=g[21589].mt;if((g[1]||{}).usz)h=g[1].usz;if((g[1]||{}).csz)u=g[1].csz;if(n){if((n[21589]||{}).mt)c=n[21589].mt;if((n[1]||{}).usz)h=g[1].usz;if((n[1]||{}).csz)u=g[1].csz}}e.l+=m;var b=e.slice(e.l,e.l+u);switch(o){case 8:b=Y(e,h);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+o);}var w=false;if(l&8){f=e._R(4);if(f==134695760){f=e._R(4);w=true}u=e._R(4);h=e._R(4)}if(u!=r)De(w,"Bad compressed size: "+r+" != "+u);if(h!=t)De(w,"Bad uncompressed size: "+t+" != "+h);je(a,p,b,{unsafe:true,mt:c})}function Ne(e,r){var t=r||{};var a=[],i=[];var s=ka(1);var l=t.compression?8:0,o=0;var c=false;if(c)o|=8;var f=0,u=0;var h=0,d=0;var m=e.FullPaths[0],p=m,v=e.FileIndex[0];var g=[];var b=0;for(f=1;f<e.FullPaths.length;++f){p=e.FullPaths[f].slice(m.length);v=e.FileIndex[f];if(!v.size||!v.content||p=="Sh33tJ5")continue;var w=h;var k=ka(p.length);for(u=0;u<p.length;++u)k._W(1,p.charCodeAt(u)&127);k=k.slice(0,k.l);g[d]=typeof v.content=="string"?er.bstr(v.content,0):er.buf(v.content,0);var y=typeof v.content=="string"?T(v.content):v.content;if(l==8)y=J(y);s=ka(30);s._W(4,67324752);s._W(2,20);s._W(2,o);s._W(2,l);if(v.mt)n(s,v.mt);else s._W(4,0);s._W(-4,o&8?0:g[d]);s._W(4,o&8?0:y.length);s._W(4,o&8?0:v.content.length);s._W(2,k.length);s._W(2,0);h+=s.length;a.push(s);h+=k.length;a.push(k);h+=y.length;a.push(y);if(o&8){s=ka(12);s._W(-4,g[d]);s._W(4,y.length);s._W(4,v.content.length);h+=s.l;a.push(s)}s=ka(46);s._W(4,33639248);s._W(2,0);s._W(2,20);s._W(2,o);s._W(2,l);s._W(4,0);s._W(-4,g[d]);s._W(4,y.length);s._W(4,v.content.length);s._W(2,k.length);s._W(2,0);s._W(2,0);s._W(2,0);s._W(2,0);s._W(4,0);s._W(4,w);b+=s.l;i.push(s);b+=k.length;i.push(k);++d}s=ka(22);s._W(4,101010256);s._W(2,0);s._W(2,0);s._W(2,d);s._W(2,d);s._W(4,b);s._W(4,h);s._W(2,0);return P([P(a),P(i),s])}var Pe={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Ie(e,r){if(e.ctype)return e.ctype;var t=e.name||"",a=t.match(/\.([^\.]+)$/);if(a&&Pe[a[1]])return Pe[a[1]];if(r){a=(t=r).match(/[\.\\]([^\.\\])+$/);if(a&&Pe[a[1]])return Pe[a[1]]}return"application/octet-stream"}function Re(e){var r=y(e);var t=[];for(var a=0;a<r.length;a+=76)t.push(r.slice(a,a+76));return t.join("\r\n")+"\r\n"}function Le(e){var r=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var r=e.charCodeAt(0).toString(16).toUpperCase();return"="+(r.length==1?"0"+r:r)});r=r.replace(/ $/gm,"=20").replace(/\t$/gm,"=09");if(r.charAt(0)=="\n")r="=0D"+r.slice(1);r=r.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A");var t=[],a=r.split("\r\n");for(var n=0;n<a.length;++n){var i=a[n];if(i.length==0){t.push("");continue}for(var s=0;s<i.length;){var l=76;var o=i.slice(s,s+l);if(o.charAt(l-1)=="=")l--;else if(o.charAt(l-2)=="=")l-=2;else if(o.charAt(l-3)=="=")l-=3;o=i.slice(s,s+l);s+=l;if(s<i.length)o+="=";t.push(o)}}return t.join("\r\n")}function Be(e){var r=[];for(var t=0;t<e.length;++t){var a=e[t];while(t<=e.length&&a.charAt(a.length-1)=="=")a=a.slice(0,a.length-1)+e[++t];r.push(a)}for(var n=0;n<r.length;++n)r[n]=r[n].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return T(r.join("\r\n"))}function ze(e,r,t){var a="",n="",i="",s;var l=0;for(;l<10;++l){var o=r[l];if(!o||o.match(/^\s*$/))break;var c=o.match(/^(.*?):\s*([^\s].*)$/);if(c)switch(c[1].toLowerCase()){case"content-location":a=c[2].trim();break;case"content-type":i=c[2].trim();break;case"content-transfer-encoding":n=c[2].trim();break;}}++l;switch(n.toLowerCase()){case"base64":s=T(S(r.slice(l).join("")));break;case"quoted-printable":s=Be(r.slice(l));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+n);}var f=je(e,a.slice(t.length),s,{unsafe:true});if(i)f.ctype=i}function $e(e,r){if(H(e.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var t=r&&r.root||"";var a=(C&&Buffer.isBuffer(e)?e.toString("binary"):H(e)).split("\r\n");var n=0,i="";for(n=0;n<a.length;++n){i=a[n];if(!/^Content-Location:/i.test(i))continue;i=i.slice(i.indexOf("file"));if(!t)t=i.slice(0,i.lastIndexOf("/")+1);if(i.slice(0,t.length)==t)continue;while(t.length>0){t=t.slice(0,t.length-1);t=t.slice(0,t.lastIndexOf("/")+1);if(i.slice(0,t.length)==t)break}}var s=(a[1]||"").match(/boundary="(.*?)"/);if(!s)throw new Error("MAD cannot find boundary");var l="--"+(s[1]||"");var o=[],c=[];var f={FileIndex:o,FullPaths:c};A(f);var u,h=0;for(n=0;n<a.length;++n){var d=a[n];if(d!==l&&d!==l+"--")continue;if(h++)ze(f,a.slice(u,n),t);u=n}return f}function We(e,r){var t=r||{};var a=t.boundary||"SheetJS";a="------="+a;var n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+a.slice(2)+'"',"","",""];var i=e.FullPaths[0],s=i,l=e.FileIndex[0];for(var o=1;o<e.FullPaths.length;++o){s=e.FullPaths[o].slice(i.length);l=e.FileIndex[o];if(!l.size||!l.content||s=="Sh33tJ5")continue;s=s.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});var c=l.content;var f=C&&Buffer.isBuffer(c)?c.toString("binary"):H(c);var u=0,h=Math.min(1024,f.length),d=0;for(var m=0;m<=h;++m)if((d=f.charCodeAt(m))>=32&&d<128)++u;var p=u>=h*4/5;n.push(a);n.push("Content-Location: "+(t.root||"file:///C:/SheetJS/")+s);n.push("Content-Transfer-Encoding: "+(p?"quoted-printable":"base64"));n.push("Content-Type: "+Ie(l,s));n.push("");n.push(p?Le(f):Re(f))}n.push(a+"--\r\n");return n.join("\r\n")}function Ue(e){var r={};A(r,e);return r}function je(e,r,t,n){var i=n&&n.unsafe;if(!i)A(e);var s=!i&&rr.find(e,r);if(!s){var l=e.FullPaths[0];if(r.slice(0,l.length)==l)l=r;else{if(l.slice(-1)!="/")l+="/";l=(l+r).replace("//","/")}s={name:a(r),type:2};e.FileIndex.push(s);e.FullPaths.push(l);if(!i)rr.utils.cfb_gc(e)}s.content=t;s.size=t?t.length:0;if(n){if(n.CLSID)s.clsid=n.CLSID;if(n.mt)s.mt=n.mt;if(n.ct)s.ct=n.ct}return s}function He(e,r){A(e);var t=rr.find(e,r);if(t)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==t){e.FileIndex.splice(a,1);e.FullPaths.splice(a,1);return true}return false}function Ve(e,r,t){A(e);var n=rr.find(e,r);if(n)for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==n){e.FileIndex[i].name=a(t);e.FullPaths[i]=t;return true}return false}function Xe(e){O(e,true)}e.find=N;e.read=x;e.parse=c;e.write=V;e.writeFile=j;e.utils={cfb_new:Ue,cfb_add:je,cfb_del:He,cfb_mov:Ve,cfb_gc:Xe,ReadShift:ha,CheckField:ga,prep_blob:ba,bconcat:P,use_zlib:G,_deflateRaw:ye,_inflateRaw:Te,consts:U};return e}();var tr;function ar(e){tr=e}function nr(e){if(typeof e==="string")return D(e);if(Array.isArray(e))return M(e);return e}function ir(e,r,t){if(typeof tr!=="undefined"&&tr.writeFileSync)return t?tr.writeFileSync(e,r,t):tr.writeFileSync(e,r);if(typeof Deno!=="undefined"){if(t&&typeof r=="string")switch(t){case"utf8":r=new TextEncoder(t).encode(r);break;case"binary":r=D(r);break;default:throw new Error("Unsupported encoding "+t);}return Deno.writeFileSync(e,r)}var a=t=="utf8"?bt(r):r;if(typeof IE_SaveFile!=="undefined")return IE_SaveFile(a,e);if(typeof Blob!=="undefined"){var n=new Blob([nr(a)],{type:"application/octet-stream"});if(typeof navigator!=="undefined"&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if(typeof saveAs!=="undefined")return saveAs(n,e);if(typeof URL!=="undefined"&&typeof document!=="undefined"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(n);if(typeof chrome==="object"&&typeof(chrome.downloads||{}).download=="function"){if(URL.revokeObjectURL&&typeof setTimeout!=="undefined")setTimeout(function(){URL.revokeObjectURL(i)},6e4);return chrome.downloads.download({url:i,filename:e,saveAs:true})}var s=document.createElement("a");if(s.download!=null){s.download=e;s.href=i;document.body.appendChild(s);s.click();document.body.removeChild(s);if(URL.revokeObjectURL&&typeof setTimeout!=="undefined")setTimeout(function(){URL.revokeObjectURL(i)},6e4);return i}}}if(typeof $!=="undefined"&&typeof File!=="undefined"&&typeof Folder!=="undefined")try{var l=File(e);l.open("w");l.encoding="binary";if(Array.isArray(r))r=O(r);l.write(r);l.close();return r}catch(o){if(!o.message||!o.message.match(/onstruct/))throw o}throw new Error("cannot save file "+e)}function sr(e){if(typeof tr!=="undefined")return tr.readFileSync(e);if(typeof Deno!=="undefined")return Deno.readFileSync(e);if(typeof $!=="undefined"&&typeof File!=="undefined"&&typeof Folder!=="undefined")try{var r=File(e);r.open("r");r.encoding="binary";var t=r.read();r.close();return t}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function lr(e){var r=Object.keys(e),t=[];for(var a=0;a<r.length;++a)if(Object.prototype.hasOwnProperty.call(e,r[a]))t.push(r[a]);return t}function or(e,r){var t=[],a=lr(e);for(var n=0;n!==a.length;++n)if(t[e[a[n]][r]]==null)t[e[a[n]][r]]=a[n];return t}function cr(e){var r=[],t=lr(e);for(var a=0;a!==t.length;++a)r[e[t[a]]]=t[a];return r}function fr(e){var r=[],t=lr(e);for(var a=0;a!==t.length;++a)r[e[t[a]]]=parseInt(t[a],10);return r}function ur(e){var r=[],t=lr(e);for(var a=0;a!==t.length;++a){if(r[e[t[a]]]==null)r[e[t[a]]]=[];r[e[t[a]]].push(t[a])}return r}var hr=new Date(1899,11,30,0,0,0);function dr(e,r){var t=e.getTime();if(r)t-=1462*24*60*60*1e3;var a=hr.getTime()+(e.getTimezoneOffset()-hr.getTimezoneOffset())*6e4;
return(t-a)/(24*60*60*1e3)}var mr=new Date;var pr=hr.getTime()+(mr.getTimezoneOffset()-hr.getTimezoneOffset())*6e4;var vr=mr.getTimezoneOffset();function gr(e){var r=new Date;r.setTime(e*24*60*60*1e3+pr);if(r.getTimezoneOffset()!==vr){r.setTime(r.getTime()+(r.getTimezoneOffset()-vr)*6e4)}return r}function br(e){var r=0,t=0,a=false;var n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var i=1;i!=n.length;++i){if(!n[i])continue;t=1;if(i>3)a=true;switch(n[i].slice(n[i].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[i].slice(n[i].length-1));case"D":t*=24;case"H":t*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");else t*=60;case"S":break;}r+=t*parseInt(n[i],10)}return r}var wr=new Date("2017-02-19T19:06:09.000Z");var kr=isNaN(wr.getFullYear())?new Date("2/19/17"):wr;var yr=kr.getFullYear()==2017;function xr(e,r){var t=new Date(e);if(yr){if(r>0)t.setTime(t.getTime()+t.getTimezoneOffset()*60*1e3);else if(r<0)t.setTime(t.getTime()-t.getTimezoneOffset()*60*1e3);return t}if(e instanceof Date)return e;if(kr.getFullYear()==1917&&!isNaN(t.getFullYear())){var a=t.getFullYear();if(e.indexOf(""+a)>-1)return t;t.setFullYear(t.getFullYear()+100);return t}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"];var i=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);if(e.indexOf("Z")>-1)i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3);return i}function Sr(e,r){if(C&&Buffer.isBuffer(e)){if(r&&A){if(e[0]==255&&e[1]==254)return bt(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return bt(d(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder!=="undefined")try{if(r){if(e[0]==255&&e[1]==254)return bt(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return bt(new TextDecoder("utf-16be").decode(e.slice(2)))}var t={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};if(Array.isArray(e))e=new Uint8Array(e);return new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return t[e]||e})}catch(a){}var n=[];for(var i=0;i!=e.length;++i)n.push(String.fromCharCode(e[i]));return n.join("")}function Cr(e){if(typeof JSON!="undefined"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var r={};for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))r[t]=Cr(e[t]);return r}function _r(e,r){var t="";while(t.length<r)t+=e;return t}function Ar(e){var r=Number(e);if(!isNaN(r))return isFinite(r)?r:NaN;if(!/\d/.test(e))return r;var t=1;var a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){t*=100;return""});if(!isNaN(r=Number(a)))return r/t;a=a.replace(/[(](.*)[)]/,function(e,r){t=-t;return r});if(!isNaN(r=Number(a)))return r/t;return r}var Er=/^(0?\d|1[0-2])(?:|:([0-5]?\d)(?:|(\.\d+)(?:|:([0-5]?\d))|:([0-5]?\d)(|\.\d+)))\s+([ap])m?$/;function Fr(e){if(!e[2])return new Date(1899,11,30,+e[1]%12+(e[7]=="p"?12:0),0,0,0);if(e[3]){if(e[4])return new Date(1899,11,30,+e[1]%12+(e[7]=="p"?12:0),+e[2],+e[4],parseFloat(e[3])*1e3);else return new Date(1899,11,30,e[7]=="p"?12:0,+e[1],+e[2],parseFloat(e[3])*1e3)}else if(e[5])return new Date(1899,11,30,+e[1]%12+(e[7]=="p"?12:0),+e[2],+e[5],e[6]?parseFloat(e[6])*1e3:0);else return new Date(1899,11,30,+e[1]%12+(e[7]=="p"?12:0),+e[2],0,0)}var Tr=["january","february","march","april","may","june","july","august","september","october","november","december"];function Dr(e){var r=e.toLowerCase();var t=r.replace(/\s+/g," ").trim();var a=t.match(Er);if(a)return Fr(a);var n=new Date(e),i=new Date(NaN);var s=n.getYear(),l=n.getMonth(),o=n.getDate();if(isNaN(o))return i;if(r.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){r=r.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"");if(r.length>3&&Tr.indexOf(r)==-1)return i}else if(r.replace(/[ap]m?/,"").match(/[a-z]/))return i;if(s<0||s>8099||e.match(/[^-0-9:,\/\\]/))return i;return n}var Or=function(){var e="abacaba".split(/(:?b)/i).length==5;return function r(t,a,n){if(e||typeof a=="string")return t.split(a);var i=t.split(a),s=[i[0]];for(var l=1;l<i.length;++l){s.push(n);s.push(i[l])}return s}}();function Mr(e){if(!e)return null;if(e.content&&e.type)return Sr(e.content,true);if(e.data)return m(e.data);if(e.asNodeBuffer&&C)return m(e.asNodeBuffer().toString("binary"));if(e.asBinary)return m(e.asBinary());if(e._data&&e._data.getContent)return m(Sr(Array.prototype.slice.call(e._data.getContent(),0)));return null}function Nr(e){if(!e)return null;if(e.data)return f(e.data);if(e.asNodeBuffer&&C)return e.asNodeBuffer();if(e._data&&e._data.getContent){var r=e._data.getContent();if(typeof r=="string")return f(r);return Array.prototype.slice.call(r)}if(e.content&&e.type)return e.content;return null}function Pr(e){return e&&e.name.slice(-4)===".bin"?Nr(e):Mr(e)}function Ir(e,r){var t=e.FullPaths||lr(e.files);var a=r.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/");for(var i=0;i<t.length;++i){var s=t[i].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==s||n==s)return e.files?e.files[t[i]]:e.FileIndex[i]}return null}function Rr(e,r){var t=Ir(e,r);if(t==null)throw new Error("Cannot find file "+r+" in zip");return t}function Lr(e,r,t){if(!t)return Pr(Rr(e,r));if(!r)return null;try{return Lr(e,r)}catch(a){return null}}function Br(e,r,t){if(!t)return Mr(Rr(e,r));if(!r)return null;try{return Br(e,r)}catch(a){return null}}function zr(e,r,t){if(!t)return Nr(Rr(e,r));if(!r)return null;try{return zr(e,r)}catch(a){return null}}function $r(e){var r=e.FullPaths||lr(e.files),t=[];for(var a=0;a<r.length;++a)if(r[a].slice(-1)!="/")t.push(r[a].replace(/^Root Entry[\/]/,""));return t.sort()}function Wr(e,r,t){if(e.FullPaths){if(typeof t=="string"){var a;if(C)a=_(t);else a=I(t);return rr.utils.cfb_add(e,r,a)}rr.utils.cfb_add(e,r,t)}else e.file(r,t)}function Ur(){return rr.utils.cfb_new()}function jr(e,r){switch(r.type){case"base64":return rr.read(e,{type:"base64"});case"binary":return rr.read(e,{type:"binary"});case"buffer":;case"array":return rr.read(e,{type:"buffer"});}throw new Error("Unrecognized type "+r.type)}function Hr(e,r){if(e.charAt(0)=="/")return e.slice(1);var t=r.split("/");if(r.slice(-1)!="/")t.pop();var a=e.split("/");while(a.length!==0){var n=a.shift();if(n==="..")t.pop();else if(n!==".")t.push(n)}return t.join("/")}var Vr='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n';var Xr=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g;var Gr=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/gm,Yr=/<[^>]*>/g;var Jr=Vr.match(Gr)?Gr:Yr;var Kr=/<\w*:/,Zr=/<(\/?)\w+:/;function qr(e,r,t){var a={};var n=0,i=0;for(;n!==e.length;++n)if((i=e.charCodeAt(n))===32||i===10||i===13)break;if(!r)a[0]=e.slice(0,n);if(n===e.length)return a;var s=e.match(Xr),l=0,o="",c=0,f="",u="",h=1;if(s)for(c=0;c!=s.length;++c){u=s[c];for(i=0;i!=u.length;++i)if(u.charCodeAt(i)===61)break;f=u.slice(0,i).trim();while(u.charCodeAt(i+1)==32)++i;h=(n=u.charCodeAt(i+1))==34||n==39?1:0;o=u.slice(i+1+h,u.length-h);for(l=0;l!=f.length;++l)if(f.charCodeAt(l)===58)break;if(l===f.length){if(f.indexOf("_")>0)f=f.slice(0,f.indexOf("_"));a[f]=o;if(!t)a[f.toLowerCase()]=o}else{var d=(l===5&&f.slice(0,5)==="xmlns"?"xmlns":"")+f.slice(l+1);if(a[d]&&f.slice(l-3,l)=="ext")continue;a[d]=o;if(!t)a[d.toLowerCase()]=o}}return a}function Qr(e){return e.replace(Zr,"<$1")}var et={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"};var rt=cr(et);var tt=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,r=/_x([\da-fA-F]{4})_/gi;function t(a){var n=a+"",i=n.indexOf("<![CDATA[");if(i==-1)return n.replace(e,function(e,r){return et[e]||String.fromCharCode(parseInt(r,e.indexOf("x")>-1?16:10))||e}).replace(r,function(e,r){return String.fromCharCode(parseInt(r,16))});var s=n.indexOf("]]>");return t(n.slice(0,i))+n.slice(i+9,s)+t(n.slice(s+3))}return function a(e,r){var a=t(e);return r?a.replace(/\r\n/g,"\n"):a}}();var at=/[&<>'"]/g,nt=/[\u0000-\u0008\u000b-\u001f\uFFFE-\uFFFF]/g;function it(e){var r=e+"";return r.replace(at,function(e){return rt[e]}).replace(nt,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function st(e){return it(e).replace(/ /g,"_x0020_")}var lt=/[\u0000-\u001f]/g;function ot(e){var r=e+"";return r.replace(at,function(e){return rt[e]}).replace(/\n/g,"<br/>").replace(lt,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}function ct(e){var r=e+"";return r.replace(at,function(e){return rt[e]}).replace(lt,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}var ft=function(){var e=/&#(\d+);/g;function r(e,r){return String.fromCharCode(parseInt(r,10))}return function t(a){return a.replace(e,r)}}();function ut(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function ht(e){switch(e){case 1:;case true:;case"1":;case"true":return true;case 0:;case false:;case"0":;case"false":return false;}return false}function dt(e){var r="",t=0,a=0,n=0,i=0,s=0,l=0;while(t<e.length){a=e.charCodeAt(t++);if(a<128){r+=String.fromCharCode(a);continue}n=e.charCodeAt(t++);if(a>191&&a<224){s=(a&31)<<6;s|=n&63;r+=String.fromCharCode(s);continue}i=e.charCodeAt(t++);if(a<240){r+=String.fromCharCode((a&15)<<12|(n&63)<<6|i&63);continue}s=e.charCodeAt(t++);l=((a&7)<<18|(n&63)<<12|(i&63)<<6|s&63)-65536;r+=String.fromCharCode(55296+(l>>>10&1023));r+=String.fromCharCode(56320+(l&1023))}return r}function mt(e){var r=E(2*e.length),t,a,n=1,i=0,s=0,l;for(a=0;a<e.length;a+=n){n=1;if((l=e.charCodeAt(a))<128)t=l;else if(l<224){t=(l&31)*64+(e.charCodeAt(a+1)&63);n=2}else if(l<240){t=(l&15)*4096+(e.charCodeAt(a+1)&63)*64+(e.charCodeAt(a+2)&63);n=3}else{n=4;t=(l&7)*262144+(e.charCodeAt(a+1)&63)*4096+(e.charCodeAt(a+2)&63)*64+(e.charCodeAt(a+3)&63);t-=65536;s=55296+(t>>>10&1023);t=56320+(t&1023)}if(s!==0){r[i++]=s&255;r[i++]=s>>>8;s=0}r[i++]=t%256;r[i++]=t>>>8}return r.slice(0,i).toString("ucs2")}function pt(e){return _(e,"binary").toString("utf8")}var vt="foo bar bazâð£";var gt=C&&(pt(vt)==dt(vt)&&pt||mt(vt)==dt(vt)&&mt)||dt;var bt=C?function(e){return _(e,"utf8").toString("binary")}:function(e){var r=[],t=0,a=0,n=0;while(t<e.length){a=e.charCodeAt(t++);switch(true){case a<128:r.push(String.fromCharCode(a));break;case a<2048:r.push(String.fromCharCode(192+(a>>6)));r.push(String.fromCharCode(128+(a&63)));break;case a>=55296&&a<57344:a-=55296;n=e.charCodeAt(t++)-56320+(a<<10);r.push(String.fromCharCode(240+(n>>18&7)));r.push(String.fromCharCode(144+(n>>12&63)));r.push(String.fromCharCode(128+(n>>6&63)));r.push(String.fromCharCode(128+(n&63)));break;default:r.push(String.fromCharCode(224+(a>>12)));r.push(String.fromCharCode(128+(a>>6&63)));r.push(String.fromCharCode(128+(a&63)));}}return r.join("")};var wt=function(){var e={};return function r(t,a){var n=t+"|"+(a||"");if(e[n])return e[n];return e[n]=new RegExp("<(?:\\w+:)?"+t+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+t+">",a||"")}}();var kt=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]});return function r(t){var a=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,"");for(var n=0;n<e.length;++n)a=a.replace(e[n][0],e[n][1]);return a}}();var yt=function(){var e={};return function r(t){if(e[t]!==undefined)return e[t];return e[t]=new RegExp("<(?:vt:)?"+t+">([\\s\\S]*?)</(?:vt:)?"+t+">","g")}}();var xt=/<\/?(?:vt:)?variant>/g,St=/<(?:vt:)([^>]*)>([\s\S]*)</;function Ct(e,r){var t=qr(e);var a=e.match(yt(t.baseType))||[];var n=[];if(a.length!=t.size){if(r.WTF)throw new Error("unexpected vector length "+a.length+" != "+t.size);return n}a.forEach(function(e){var r=e.replace(xt,"").match(St);if(r)n.push({v:gt(r[2]),t:r[1]})});return n}var _t=/(^\s|\s$|\n)/;function At(e,r){return"<"+e+(r.match(_t)?' xml:space="preserve"':"")+">"+r+"</"+e+">"}function Et(e){return lr(e).map(function(r){return" "+r+'="'+e[r]+'"'}).join("")}function Ft(e,r,t){return"<"+e+(t!=null?Et(t):"")+(r!=null?(r.match(_t)?' xml:space="preserve"':"")+">"+r+"</"+e:"/")+">"}function Tt(e,r){try{return e.toISOString().replace(/\.\d*/,"")}catch(t){if(r)throw t}return""}function Dt(e,r){switch(typeof e){case"string":var t=Ft("vt:lpwstr",it(e));if(r)t=t.replace(/&quot;/g,"_x0022_");return t;case"number":return Ft((e|0)==e?"vt:i4":"vt:r8",it(String(e)));case"boolean":return Ft("vt:bool",e?"true":"false");}if(e instanceof Date)return Ft("vt:filetime",Tt(e));throw new Error("Unable to serialize "+e)}function Ot(e){if(C&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e==="string")return e;if(typeof Uint8Array!=="undefined"&&e instanceof Uint8Array)return gt(O(N(e)));throw new Error("Bad input format: expected Buffer or string")}var Mt=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/](?:[^>=]|="[^"]*?")*)?>/gm;var Nt={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"};var Pt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];var It={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Rt(e,r){var t=1-2*(e[r+7]>>>7);var a=((e[r+7]&127)<<4)+(e[r+6]>>>4&15);var n=e[r+6]&15;for(var i=5;i>=0;--i)n=n*256+e[r+i];if(a==2047)return n==0?t*Infinity:NaN;if(a==0)a=-1022;else{a-=1023;n+=Math.pow(2,52)}return t*Math.pow(2,a-52)*n}function Lt(e,r,t){var a=(r<0||1/r==-Infinity?1:0)<<7,n=0,i=0;var s=a?-r:r;if(!isFinite(s)){n=2047;i=isNaN(r)?26985:0}else if(s==0)n=i=0;else{n=Math.floor(Math.log(s)/Math.LN2);i=s*Math.pow(2,52-n);if(n<=-1023&&(!isFinite(i)||i<Math.pow(2,52))){n=-1022}else{i-=Math.pow(2,52);n+=1023}}for(var l=0;l<=5;++l,i/=256)e[t+l]=i&255;e[t+6]=(n&15)<<4|i&15;e[t+7]=n>>4|a}var Bt=function(e){var r=[],t=10240;for(var a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,i=e[0][a].length;n<i;n+=t)r.push.apply(r,e[0][a].slice(n,n+t));return r};var zt=C?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(e){return Buffer.isBuffer(e)?e:_(e)})):Bt(e)}:Bt;var $t=function(e,r,t){var a=[];for(var n=r;n<t;n+=2)a.push(String.fromCharCode(la(e,n)));return a.join("").replace(R,"")};var Wt=C?function(e,r,t){if(!Buffer.isBuffer(e)||!A)return $t(e,r,t);return e.toString("utf16le",r,t).replace(R,"")}:$t;var Ut=function(e,r,t){var a=[];for(var n=r;n<r+t;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")};var jt=C?function(e,r,t){return Buffer.isBuffer(e)?e.toString("hex",r,r+t):Ut(e,r,t)}:Ut;var Ht=function(e,r,t){var a=[];for(var n=r;n<t;n++)a.push(String.fromCharCode(sa(e,n)));return a.join("")};var Vt=C?function Oc(e,r,t){return Buffer.isBuffer(e)?e.toString("utf8",r,t):Ht(e,r,t)}:Ht;var Xt=function(e,r){var t=ca(e,r);return t>0?Vt(e,r+4,r+4+t-1):""};var Gt=Xt;var Yt=function(e,r){var t=ca(e,r);return t>0?Vt(e,r+4,r+4+t-1):""};var Jt=Yt;var Kt=function(e,r){var t=2*ca(e,r);return t>0?Vt(e,r+4,r+4+t-1):""};var Zt=Kt;var qt=function Mc(e,r){var t=ca(e,r);return t>0?Wt(e,r+4,r+4+t):""};var Qt=qt;var ea=function(e,r){var t=ca(e,r);return t>0?Vt(e,r+4,r+4+t):""};var ra=ea;var ta=function(e,r){return Rt(e,r)};var aa=ta;var na=function Nc(e){return Array.isArray(e)||typeof Uint8Array!=="undefined"&&e instanceof Uint8Array};if(C){Gt=function Pc(e,r){if(!Buffer.isBuffer(e))return Xt(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""};Jt=function Ic(e,r){if(!Buffer.isBuffer(e))return Yt(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""};Zt=function Rc(e,r){if(!Buffer.isBuffer(e)||!A)return Kt(e,r);var t=2*e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t-1)};Qt=function Lc(e,r){if(!Buffer.isBuffer(e)||!A)return qt(e,r);var t=e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t)};ra=function Bc(e,r){if(!Buffer.isBuffer(e))return ea(e,r);var t=e.readUInt32LE(r);return e.toString("utf8",r+4,r+4+t)};aa=function zc(e,r){if(Buffer.isBuffer(e))return e.readDoubleLE(r);return ta(e,r)};na=function $c(e){return Buffer.isBuffer(e)||Array.isArray(e)||typeof Uint8Array!=="undefined"&&e instanceof Uint8Array}}function ia(){Wt=function(e,r,t){return a.utils.decode(1200,e.slice(r,t)).replace(R,"")};Vt=function(e,r,t){return a.utils.decode(65001,e.slice(r,t))};Gt=function(e,r){var n=ca(e,r);return n>0?a.utils.decode(t,e.slice(r+4,r+4+n-1)):""};Jt=function(e,t){var n=ca(e,t);return n>0?a.utils.decode(r,e.slice(t+4,t+4+n-1)):""};Zt=function(e,r){var t=2*ca(e,r);return t>0?a.utils.decode(1200,e.slice(r+4,r+4+t-1)):""};Qt=function(e,r){var t=ca(e,r);return t>0?a.utils.decode(1200,e.slice(r+4,r+4+t)):""};ra=function(e,r){var t=ca(e,r);return t>0?a.utils.decode(65001,e.slice(r+4,r+4+t)):""}}if(typeof a!=="undefined")ia();var sa=function(e,r){return e[r]};var la=function(e,r){return e[r+1]*(1<<8)+e[r]};var oa=function(e,r){var t=e[r+1]*(1<<8)+e[r];return t<32768?t:(65535-t+1)*-1};var ca=function(e,r){return e[r+3]*(1<<24)+(e[r+2]<<16)+(e[r+1]<<8)+e[r]};var fa=function(e,r){return e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]};var ua=function(e,r){return e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]};function ha(e,t){var n="",i,s,l=[],o,c,f,u;switch(t){case"dbcs":u=this.l;if(C&&Buffer.isBuffer(this)&&A)n=this.slice(this.l,this.l+2*e).toString("utf16le");else for(f=0;f<e;++f){n+=String.fromCharCode(la(this,u));u+=2}e*=2;break;case"utf8":n=Vt(this,this.l,this.l+e);break;case"utf16le":e*=2;n=Wt(this,this.l,this.l+e);break;case"wstr":if(typeof a!=="undefined")n=a.utils.decode(r,this.slice(this.l,this.l+2*e));else return ha.call(this,e,"dbcs");e=2*e;break;case"lpstr-ansi":n=Gt(this,this.l);e=4+ca(this,this.l);break;case"lpstr-cp":n=Jt(this,this.l);e=4+ca(this,this.l);break;case"lpwstr":n=Zt(this,this.l);e=4+2*ca(this,this.l);break;case"lpp4":e=4+ca(this,this.l);n=Qt(this,this.l);if(e&2)e+=2;break;case"8lpp4":e=4+ca(this,this.l);n=ra(this,this.l);if(e&3)e+=4-(e&3);break;case"cstr":e=0;n="";while((o=sa(this,this.l+e++))!==0)l.push(p(o));n=l.join("");break;case"_wstr":e=0;n="";while((o=la(this,this.l+e))!==0){l.push(p(o));e+=2}e+=2;n=l.join("");break;case"dbcs-cont":n="";u=this.l;for(f=0;f<e;++f){if(this.lens&&this.lens.indexOf(u)!==-1){o=sa(this,u);this.l=u+1;c=ha.call(this,e-f,o?"dbcs-cont":"sbcs-cont");return l.join("")+c}l.push(p(la(this,u)));u+=2}n=l.join("");e*=2;break;case"cpstr":if(typeof a!=="undefined"){n=a.utils.decode(r,this.slice(this.l,this.l+e));break};case"sbcs-cont":n="";u=this.l;for(f=0;f!=e;++f){if(this.lens&&this.lens.indexOf(u)!==-1){o=sa(this,u);this.l=u+1;c=ha.call(this,e-f,o?"dbcs-cont":"sbcs-cont");return l.join("")+c}l.push(p(sa(this,u)));u+=1}n=l.join("");break;default:switch(e){case 1:i=sa(this,this.l);this.l++;return i;case 2:i=(t==="i"?oa:la)(this,this.l);this.l+=2;return i;case 4:;case-4:if(t==="i"||(this[this.l+3]&128)===0){i=(e>0?fa:ua)(this,this.l);this.l+=4;return i}else{s=ca(this,this.l);this.l+=4}return s;case 8:;case-8:if(t==="f"){if(e==8)s=aa(this,this.l);else s=aa([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0);this.l+=8;return s}else e=8;case 16:n=jt(this,this.l,e);break;};}this.l+=e;return n}var da=function(e,r,t){e[t]=r&255;e[t+1]=r>>>8&255;e[t+2]=r>>>16&255;e[t+3]=r>>>24&255};var ma=function(e,r,t){e[t]=r&255;e[t+1]=r>>8&255;e[t+2]=r>>16&255;e[t+3]=r>>24&255};var pa=function(e,r,t){e[t]=r&255;e[t+1]=r>>>8&255};function va(e,n,i){var s=0,l=0;if(i==="dbcs"){for(l=0;l!=n.length;++l)pa(this,n.charCodeAt(l),this.l+2*l);s=2*n.length}else if(i==="sbcs"||i=="cpstr"){if(typeof a!=="undefined"&&t==874){for(l=0;l!=n.length;++l){var o=a.utils.encode(t,n.charAt(l));this[this.l+l]=o[0]}s=n.length}else if(typeof a!=="undefined"&&i=="cpstr"){o=a.utils.encode(r,n);if(o.length==n.length)for(l=0;l<n.length;++l)if(o[l]==0&&n.charCodeAt(l)!=0)o[l]=95;if(o.length==2*n.length)for(l=0;l<n.length;++l)if(o[2*l]==0&&o[2*l+1]==0&&n.charCodeAt(l)!=0)o[2*l]=95;for(l=0;l<o.length;++l)this[this.l+l]=o[l];s=o.length}else{n=n.replace(/[^\x00-\x7F]/g,"_");for(l=0;l!=n.length;++l)this[this.l+l]=n.charCodeAt(l)&255;s=n.length}}else if(i==="hex"){for(;l<e;++l){this[this.l++]=parseInt(n.slice(2*l,2*l+2),16)||0}return this}else if(i==="utf16le"){var c=Math.min(this.l+e,this.length);for(l=0;l<Math.min(n.length,e);++l){var f=n.charCodeAt(l);this[this.l++]=f&255;this[this.l++]=f>>8}while(this.l<c)this[this.l++]=0;return this}else switch(e){case 1:s=1;this[this.l]=n&255;break;case 2:s=2;this[this.l]=n&255;n>>>=8;this[this.l+1]=n&255;break;case 3:s=3;this[this.l]=n&255;n>>>=8;this[this.l+1]=n&255;n>>>=8;this[this.l+2]=n&255;break;case 4:s=4;da(this,n,this.l);break;case 8:s=8;if(i==="f"){Lt(this,n,this.l);break};case 16:break;case-4:s=4;ma(this,n,this.l);break;}this.l+=s;return this}function ga(e,r){var t=jt(this,this.l,e.length>>1);if(t!==e)throw new Error(r+"Expected "+e+" saw "+t);this.l+=e.length>>1}function ba(e,r){e.l=r;e._R=ha;e.chk=ga;e._W=va}function wa(e,r){e.l+=r}function ka(e){var r=E(e);ba(r,0);return r}function ya(e,r,t){if(!e)return;var a,n,i;ba(e,e.l||0);var s=e.length,l=0,o=0;while(e.l<s){l=e._R(1);if(l&128)l=(l&127)+((e._R(1)&127)<<7);var c=XLSBRecordEnum[l]||XLSBRecordEnum[65535];a=e._R(1);i=a&127;for(n=1;n<4&&a&128;++n)i+=((a=e._R(1))&127)<<7*n;o=e.l+i;var f=c.f&&c.f(e,i,t);e.l=o;if(r(f,c,l))return}}function xa(){var e=[],r=C?256:2048;var t=function o(e){var r=ka(e);ba(r,0);return r};var a=t(r);var n=function c(){if(!a)return;if(a.l){if(a.length>a.l){a=a.slice(0,a.l);a.l=a.length}if(a.length>0)e.push(a)}a=null};var i=function f(e){if(a&&e<a.length-a.l)return a;n();return a=t(Math.max(e+1,r))};var s=function u(){n();return P(e)};var l=function h(e){n();a=e;if(a.l==null)a.l=a.length;i(r)};return{next:i,push:l,end:s,_bufs:e}}function Sa(e,r,t,a){var n=+r,i;if(isNaN(n))return;if(!a)a=XLSBRecordEnum[n].p||(t||[]).length||0;i=1+(n>=128?1:0)+1;if(a>=128)++i;if(a>=16384)++i;if(a>=2097152)++i;var s=e.next(i);if(n<=127)s._W(1,n);else{s._W(1,(n&127)+128);s._W(1,n>>7)}for(var l=0;l!=4;++l){if(a>=128){s._W(1,(a&127)+128);a>>=7}else{s._W(1,a);break}}if(a>0&&na(t))e.push(t)}function Ca(e,r,t){var a=Cr(e);if(r.s){if(a.cRel)a.c+=r.s.c;if(a.rRel)a.r+=r.s.r}else{if(a.cRel)a.c+=r.c;if(a.rRel)a.r+=r.r}if(!t||t.biff<12){while(a.c>=256)a.c-=256;while(a.r>=65536)a.r-=65536}return a}function _a(e,r,t){var a=Cr(e);a.s=Ca(a.s,r.s,t);a.e=Ca(a.e,r.s,t);return a}function Aa(e,r){if(e.cRel&&e.c<0){e=Cr(e);while(e.c<0)e.c+=r>8?16384:256}if(e.rRel&&e.r<0){e=Cr(e);while(e.r<0)e.r+=r>8?1048576:r>5?65536:16384}var t=Ba(e);if(!e.cRel&&e.cRel!=null)t=Pa(t);if(!e.rRel&&e.rRel!=null)t=Da(t);return t}function Ea(e,r){if(e.s.r==0&&!e.s.rRel){if(e.e.r==(r.biff>=12?1048575:r.biff>=8?65536:16384)&&!e.e.rRel){return(e.s.cRel?"":"$")+Na(e.s.c)+":"+(e.e.cRel?"":"$")+Na(e.e.c)}}if(e.s.c==0&&!e.s.cRel){if(e.e.c==(r.biff>=12?16383:255)&&!e.e.cRel){return(e.s.rRel?"":"$")+Ta(e.s.r)+":"+(e.e.rRel?"":"$")+Ta(e.e.r)}}return Aa(e.s,r.biff)+":"+Aa(e.e,r.biff)}function Fa(e){return parseInt(Oa(e),10)-1}function Ta(e){return""+(e+1)}function Da(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function Oa(e){return e.replace(/\$(\d+)$/,"$1")}function Ma(e){var r=Ia(e),t=0,a=0;for(;a!==r.length;++a)t=26*t+r.charCodeAt(a)-64;return t-1}function Na(e){if(e<0)throw new Error("invalid column "+e);var r="";for(++e;e;e=Math.floor((e-1)/26))r=String.fromCharCode((e-1)%26+65)+r;return r}function Pa(e){return e.replace(/^([A-Z])/,"$$$1")}function Ia(e){return e.replace(/^\$([A-Z])/,"$1")}function Ra(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function La(e){var r=0,t=0;for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);if(n>=48&&n<=57)r=10*r+(n-48);else if(n>=65&&n<=90)t=26*t+(n-64)}return{c:t-1,r:r-1}}function Ba(e){var r=e.c+1;var t="";for(;r;r=(r-1)/26|0)t=String.fromCharCode((r-1)%26+65)+t;return t+(e.r+1)}function za(e){var r=e.indexOf(":");if(r==-1)return{s:La(e),e:La(e)};return{s:La(e.slice(0,r)),e:La(e.slice(r+1))}}function $a(e,r){if(typeof r==="undefined"||typeof r==="number"){return $a(e.s,e.e)}if(typeof e!=="string")e=Ba(e);if(typeof r!=="string")r=Ba(r);return e==r?e:e+":"+r}function Wa(e){var r=za(e);return"$"+Na(r.s.c)+"$"+Ta(r.s.r)+":$"+Na(r.e.c)+"$"+Ta(r.e.r)}function Ua(e,r){if(!e&&!(r&&r.biff<=5&&r.biff>=2))throw new Error("empty sheet name");if(/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e))return"'"+e.replace(/'/g,"''")+"'";return e}function ja(e){var r={s:{c:0,r:0},e:{c:0,r:0}};var t=0,a=0,n=0;var i=e.length;for(t=0;a<i;++a){if((n=e.charCodeAt(a)-64)<1||n>26)break;t=26*t+n}r.s.c=--t;for(t=0;a<i;++a){if((n=e.charCodeAt(a)-48)<0||n>9)break;t=10*t+n}r.s.r=--t;if(a===i||n!=10){r.e.c=r.s.c;r.e.r=r.s.r;return r}++a;for(t=0;a!=i;++a){if((n=e.charCodeAt(a)-64)<1||n>26)break;t=26*t+n}r.e.c=--t;for(t=0;a!=i;++a){if((n=e.charCodeAt(a)-48)<0||n>9)break;t=10*t+n}r.e.r=--t;return r}function Ha(e,r){var t=e.t=="d"&&r instanceof Date;if(e.z!=null)try{return e.w=je(e.z,t?dr(r):r)}catch(a){}try{return e.w=je((e.XF||{}).numFmtId||(t?14:0),t?dr(r):r)}catch(a){return""+r}}function Va(e,r,t){if(e==null||e.t==null||e.t=="z")return"";if(e.w!==undefined)return e.w;if(e.t=="d"&&!e.z&&t&&t.dateNF)e.z=t.dateNF;if(e.t=="e")return vn[e.v]||e.v;if(r==undefined)return Ha(e,e.v);return Ha(e,r)}function Xa(e,r){var t=r&&r.sheet?r.sheet:"Sheet1";var a={};a[t]=e;return{SheetNames:[t],Sheets:a}}function Ga(e,r,t){var a=t||{};var n=e?e["!data"]!=null:a.dense;if(b!=null&&n==null)n=b;var i=e||{};if(n&&!i["!data"])i["!data"]=[];var s=0,l=0;if(i&&a.origin!=null){if(typeof a.origin=="number")s=a.origin;else{var o=typeof a.origin=="string"?La(a.origin):a.origin;s=o.r;l=o.c}if(!i["!ref"])i["!ref"]="A1:A1"}var c={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var f=ja(i["!ref"]);c.s.c=f.s.c;c.s.r=f.s.r;c.e.c=Math.max(c.e.c,f.e.c);c.e.r=Math.max(c.e.r,f.e.r);if(s==-1)c.e.r=s=f.e.r+1}var u=[];for(var h=0;h!=r.length;++h){if(!r[h])continue;if(!Array.isArray(r[h]))throw new Error("aoa_to_sheet expects an array of arrays");var d=s+h,m=""+(d+1);if(n){if(!i["!data"][d])i["!data"][d]=[];u=i["!data"][d]}for(var p=0;p!=r[h].length;++p){if(typeof r[h][p]==="undefined")continue;var v={v:r[h][p]};var g=l+p;if(c.s.r>d)c.s.r=d;if(c.s.c>g)c.s.c=g;if(c.e.r<d)c.e.r=d;if(c.e.c<g)c.e.c=g;if(r[h][p]&&typeof r[h][p]==="object"&&!Array.isArray(r[h][p])&&!(r[h][p]instanceof Date))v=r[h][p];else{if(Array.isArray(v.v)){v.f=r[h][p][1];v.v=v.v[0]}if(v.v===null){if(v.f)v.t="n";else if(a.nullError){v.t="e";v.v=0}else if(!a.sheetStubs)continue;else v.t="z"}else if(typeof v.v==="number")v.t="n";else if(typeof v.v==="boolean")v.t="b";else if(v.v instanceof Date){v.z=a.dateNF||Z[14];if(a.cellDates){v.t="d";v.w=je(v.z,dr(v.v,a.date1904))}else{v.t="n";v.v=dr(v.v,a.date1904);v.w=je(v.z,v.v)}}else v.t="s"}if(n){if(u[g]&&u[g].z)v.z=u[g].z;u[g]=v}else{var w=Na(g)+m;if(i[w]&&i[w].z)v.z=i[w].z;i[w]=v}}}if(c.s.c<1e7)i["!ref"]=$a(c);return i}function Ya(e,r){return Ga(null,e,r)}var Ja=2;var Ka=3;var Za=11;var qa=12;var Qa=19;var en=64;var rn=65;var tn=71;var an=4108;var nn=4126;var sn=80;var ln=81;var on=[sn,ln];var cn={1:{n:"CodePage",t:Ja},2:{n:"Category",t:sn},3:{n:"PresentationFormat",t:sn},4:{n:"ByteCount",t:Ka},5:{n:"LineCount",t:Ka},6:{n:"ParagraphCount",t:Ka},7:{n:"SlideCount",t:Ka},8:{n:"NoteCount",t:Ka},9:{n:"HiddenCount",t:Ka},10:{n:"MultimediaClipCount",t:Ka},11:{n:"ScaleCrop",t:Za},12:{n:"HeadingPairs",t:an},13:{n:"TitlesOfParts",t:nn},14:{n:"Manager",t:sn},15:{n:"Company",t:sn},16:{n:"LinksUpToDate",t:Za},17:{n:"CharacterCount",t:Ka},19:{n:"SharedDoc",t:Za},22:{n:"HyperlinksChanged",t:Za},23:{n:"AppVersion",t:Ka,p:"version"},24:{n:"DigSig",t:rn},26:{n:"ContentType",t:sn},27:{n:"ContentStatus",t:sn},28:{n:"Language",t:sn},29:{n:"Version",t:sn},255:{},2147483648:{n:"Locale",t:Qa},2147483651:{n:"Behavior",t:Qa},1919054434:{}};var fn={1:{n:"CodePage",t:Ja},2:{n:"Title",t:sn},3:{n:"Subject",t:sn},4:{n:"Author",t:sn},5:{n:"Keywords",t:sn},6:{n:"Comments",t:sn},7:{n:"Template",t:sn},8:{n:"LastAuthor",t:sn},9:{n:"RevNumber",t:sn},10:{n:"EditTime",t:en},11:{n:"LastPrinted",t:en},12:{n:"CreatedDate",t:en},13:{n:"ModifiedDate",t:en},14:{n:"PageCount",t:Ka},15:{n:"WordCount",t:Ka},16:{n:"CharCount",t:Ka},17:{n:"Thumbnail",t:tn},18:{n:"Application",t:sn},19:{n:"DocSecurity",t:Ka},255:{},2147483648:{n:"Locale",t:Qa},2147483651:{n:"Behavior",t:Qa},1919054434:{}};var un={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"};var hn=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function dn(e){return e.map(function(e){return[e>>16&255,e>>8&255,e&255]})}var mn=dn([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,0,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]);var pn=Cr(mn);var vn={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"};var gn={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255};var bn=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];var wn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks",
"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};var kn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function yn(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function xn(e){var r=yn();if(!e||!e.match)return r;var t={};(e.match(Jr)||[]).forEach(function(e){var a=qr(e);switch(a[0].replace(Kr,"<")){case"<?xml":break;case"<Types":r.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":t[a.Extension.toLowerCase()]=a.ContentType;break;case"<Override":if(r[wn[a.ContentType]]!==undefined)r[wn[a.ContentType]].push(a.PartName);break;}});if(r.xmlns!==Nt.CT)throw new Error("Unknown Namespace: "+r.xmlns);r.calcchain=r.calcchains.length>0?r.calcchains[0]:"";r.sst=r.strs.length>0?r.strs[0]:"";r.style=r.styles.length>0?r.styles[0]:"";r.defaults=t;delete r.calcchains;return r}function Sn(e,r,t){var a=ur(wn);var n=[],i;if(!t){n[n.length]=Vr;n[n.length]=Ft("Types",null,{xmlns:Nt.CT,"xmlns:xsd":Nt.xsd,"xmlns:xsi":Nt.xsi});n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(e){return Ft("Default",null,{Extension:e[0],ContentType:e[1]})}))}var s=function(t){if(e[t]&&e[t].length>0){i=e[t][0];n[n.length]=Ft("Override",null,{PartName:(i[0]=="/"?"":"/")+i,ContentType:kn[t][r.bookType]||kn[t]["xlsx"]})}};var l=function(t){(e[t]||[]).forEach(function(e){n[n.length]=Ft("Override",null,{PartName:(e[0]=="/"?"":"/")+e,ContentType:kn[t][r.bookType]||kn[t]["xlsx"]})})};var o=function(r){(e[r]||[]).forEach(function(e){n[n.length]=Ft("Override",null,{PartName:(e[0]=="/"?"":"/")+e,ContentType:a[r][0]})})};s("workbooks");l("sheets");l("charts");o("themes");["strs","styles"].forEach(s);["coreprops","extprops","custprops"].forEach(o);o("vba");o("comments");o("threadedcomments");o("drawings");l("metadata");o("people");if(!t&&n.length>2){n[n.length]="</Types>";n[1]=n[1].replace("/>",">")}return n.join("")}var Cn={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",CONN:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/connections",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function _n(e){var r=e.lastIndexOf("/");return e.slice(0,r+1)+"_rels/"+e.slice(r+1)+".rels"}function An(e,r){var t={"!id":{}};if(!e)return t;if(r.charAt(0)!=="/"){r="/"+r}var a={};(e.match(Jr)||[]).forEach(function(e){var n=qr(e);if(n[0]==="<Relationship"){var i={};i.Type=n.Type;i.Target=tt(n.Target);i.Id=n.Id;if(n.TargetMode)i.TargetMode=n.TargetMode;var s=n.TargetMode==="External"?n.Target:Hr(n.Target,r);t[s]=i;a[n.Id]=i}});t["!id"]=a;return t}function En(e){var r=[Vr,Ft("Relationships",null,{xmlns:Nt.RELS})];lr(e["!id"]).forEach(function(t){r[r.length]=Ft("Relationship",null,e["!id"][t])});if(r.length>2){r[r.length]="</Relationships>";r[1]=r[1].replace("/>",">")}return r.join("")}function Fn(e,r,t,a,n,i){if(!n)n={};if(!e["!id"])e["!id"]={};if(!e["!idx"])e["!idx"]=1;if(r<0)for(r=e["!idx"];e["!id"]["rId"+r];++r){}e["!idx"]=r+1;n.Id="rId"+r;n.Type=a;n.Target=t;if(i)n.TargetMode=i;else if([Cn.HLINK,Cn.XPATH,Cn.XMISS].indexOf(n.Type)>-1)n.TargetMode="External";if(e["!id"][n.Id])throw new Error("Cannot rewrite rId "+r);e["!id"][n.Id]=n;e[("/"+n.Target).replace("//","/")]=n;return r}var Tn="application/vnd.oasis.opendocument.spreadsheet";function Dn(e,r){var t=Ot(e);var a;var n;while(a=Mt.exec(t))switch(a[3]){case"manifest":break;case"file-entry":n=qr(a[0],false);if(n.path=="/"&&n.type!==Tn)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":;case"algorithm":;case"start-key-generation":;case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(r&&r.WTF)throw a;}}function On(e){var r=[Vr];r.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n');r.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var t=0;t<e.length;++t)r.push('  <manifest:file-entry manifest:full-path="'+e[t][0]+'" manifest:media-type="'+e[t][1]+'"/>\n');r.push("</manifest:manifest>");return r.join("")}function Mn(e,r,t){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(t||"odf")+"#"+r+'"/>\n',"  </rdf:Description>\n"].join("")}function Nn(e,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")}function Pn(e){var r=[Vr];r.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var t=0;t!=e.length;++t){r.push(Mn(e[t][0],e[t][1]));r.push(Nn("",e[t][0]))}r.push(Mn("","Document","pkg"));r.push("</rdf:RDF>");return r.join("")}function In(r,t){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+e.version+"</meta:generator></office:meta></office:document-meta>"}var Rn=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];var Ln=function(){var e=new Array(Rn.length);for(var r=0;r<Rn.length;++r){var t=Rn[r];var a="(?:"+t[0].slice(0,t[0].indexOf(":"))+":)"+t[0].slice(t[0].indexOf(":")+1);e[r]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function Bn(e){var r={};e=gt(e);for(var t=0;t<Rn.length;++t){var a=Rn[t],n=e.match(Ln[t]);if(n!=null&&n.length>0)r[a[1]]=tt(n[1]);if(a[2]==="date"&&r[a[1]])r[a[1]]=xr(r[a[1]])}return r}function zn(e,r,t,a,n){if(n[e]!=null||r==null||r==="")return;n[e]=r;r=it(r);a[a.length]=t?Ft(e,r,t):At(e,r)}function $n(e,r){var t=r||{};var a=[Vr,Ft("cp:coreProperties",null,{"xmlns:cp":Nt.CORE_PROPS,"xmlns:dc":Nt.dc,"xmlns:dcterms":Nt.dcterms,"xmlns:dcmitype":Nt.dcmitype,"xmlns:xsi":Nt.xsi})],n={};if(!e&&!t.Props)return a.join("");if(e){if(e.CreatedDate!=null)zn("dcterms:created",typeof e.CreatedDate==="string"?e.CreatedDate:Tt(e.CreatedDate,t.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n);if(e.ModifiedDate!=null)zn("dcterms:modified",typeof e.ModifiedDate==="string"?e.ModifiedDate:Tt(e.ModifiedDate,t.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n)}for(var i=0;i!=Rn.length;++i){var s=Rn[i];var l=t.Props&&t.Props[s[1]]!=null?t.Props[s[1]]:e?e[s[1]]:null;if(l===true)l="1";else if(l===false)l="0";else if(typeof l=="number")l=String(l);if(l!=null)zn(s[0],l,null,a,n)}if(a.length>2){a[a.length]="</cp:coreProperties>";a[1]=a[1].replace("/>",">")}return a.join("")}var Wn=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];var Un=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function jn(e,r,t,a){var n=[];if(typeof e=="string")n=Ct(e,a);else for(var i=0;i<e.length;++i)n=n.concat(e[i].map(function(e){return{v:e}}));var s=typeof r=="string"?Ct(r,a).map(function(e){return e.v}):r;var l=0,o=0;if(s.length>0)for(var c=0;c!==n.length;c+=2){o=+n[c+1].v;switch(n[c].v){case"Worksheets":;case"工作表":;case"Листы":;case"أوراق العمل":;case"ワークシート":;case"גליונות עבודה":;case"Arbeitsblätter":;case"Çalışma Sayfaları":;case"Feuilles de calcul":;case"Fogli di lavoro":;case"Folhas de cálculo":;case"Planilhas":;case"Regneark":;case"Hojas de cálculo":;case"Werkbladen":t.Worksheets=o;t.SheetNames=s.slice(l,l+o);break;case"Named Ranges":;case"Rangos con nombre":;case"名前付き一覧":;case"Benannte Bereiche":;case"Navngivne områder":t.NamedRanges=o;t.DefinedNames=s.slice(l,l+o);break;case"Charts":;case"Diagramme":t.Chartsheets=o;t.ChartNames=s.slice(l,l+o);break;}l+=o}}function Hn(e,r,t){var a={};if(!r)r={};e=gt(e);Wn.forEach(function(t){var n=(e.match(wt(t[0]))||[])[1];switch(t[2]){case"string":if(n)r[t[1]]=tt(n);break;case"bool":r[t[1]]=n==="true";break;case"raw":var i=e.match(new RegExp("<"+t[0]+"[^>]*>([\\s\\S]*?)</"+t[0]+">"));if(i&&i.length>0)a[t[1]]=i[1];break;}});if(a.HeadingPairs&&a.TitlesOfParts)jn(a.HeadingPairs,a.TitlesOfParts,r,t);return r}function Vn(e){var r=[],t=Ft;if(!e)e={};e.Application="SheetJS";r[r.length]=Vr;r[r.length]=Ft("Properties",null,{xmlns:Nt.EXT_PROPS,"xmlns:vt":Nt.vt});Wn.forEach(function(a){if(e[a[1]]===undefined)return;var n;switch(a[2]){case"string":n=it(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false";break;}if(n!==undefined)r[r.length]=t(a[0],n)});r[r.length]=t("HeadingPairs",t("vt:vector",t("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+t("vt:variant",t("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"}));r[r.length]=t("TitlesOfParts",t("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+it(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"}));if(r.length>2){r[r.length]="</Properties>";r[1]=r[1].replace("/>",">")}return r.join("")}var Xn=/<[^>]+>[^<]*/g;function Gn(e,r){var t={},a="";var n=e.match(Xn);if(n)for(var i=0;i!=n.length;++i){var s=n[i],l=qr(s);switch(Qr(l[0])){case"<?xml":break;case"<Properties":break;case"<property":a=tt(l.name);break;case"</property>":a=null;break;default:if(s.indexOf("<vt:")===0){var o=s.split(">");var c=o[0].slice(4),f=o[1];switch(c){case"lpstr":;case"bstr":;case"lpwstr":t[a]=tt(f);break;case"bool":t[a]=ht(f);break;case"i1":;case"i2":;case"i4":;case"i8":;case"int":;case"uint":t[a]=parseInt(f,10);break;case"r4":;case"r8":;case"decimal":t[a]=parseFloat(f);break;case"filetime":;case"date":t[a]=xr(f);break;case"cy":;case"error":t[a]=tt(f);break;default:if(c.slice(-1)=="/")break;if(r.WTF&&typeof console!=="undefined")console.warn("Unexpected",s,c,o);}}else if(s.slice(0,2)==="</"){}else if(r.WTF)throw new Error(s);}}return t}function Yn(e){var r=[Vr,Ft("Properties",null,{xmlns:Nt.CUST_PROPS,"xmlns:vt":Nt.vt})];if(!e)return r.join("");var t=1;lr(e).forEach(function a(n){++t;r[r.length]=Ft("property",Dt(e[n],true),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:t,name:it(n)})});if(r.length>2){r[r.length]="</Properties>";r[1]=r[1].replace("/>",">")}return r.join("")}var Jn=[2,3,48,49,131,139,140,245];var Kn=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969};var n=cr({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function i(r,t){var n=[];var i=E(1);switch(t.type){case"base64":i=T(S(r));break;case"binary":i=T(r);break;case"buffer":;case"array":i=r;break;}ba(i,0);var s=i._R(1);var l=!!(s&136);var o=false,c=false;switch(s){case 2:break;case 3:break;case 48:o=true;l=true;break;case 49:o=true;l=true;break;case 131:break;case 139:break;case 140:c=true;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+s.toString(16));}var f=0,u=521;if(s==2)f=i._R(2);i.l+=3;if(s!=2)f=i._R(4);if(f>1048576)f=1e6;if(s!=2)u=i._R(2);var h=i._R(2);var d=t.codepage||1252;if(s!=2){i.l+=16;i._R(1);if(i[i.l]!==0)d=e[i[i.l]];i.l+=1;i.l+=2}if(c)i.l+=36;var m=[],p={};var v=Math.min(i.length,s==2?521:u-10-(o?264:0));var g=c?32:11;while(i.l<v&&i[i.l]!=13){p={};p.name=(typeof a!=="undefined"?a.utils.decode(d,i.slice(i.l,i.l+g)):O(i.slice(i.l,i.l+g))).replace(/[\u0000\r\n].*$/g,"");i.l+=g;p.type=String.fromCharCode(i._R(1));if(s!=2&&!c)p.offset=i._R(4);p.len=i._R(1);if(s==2)p.offset=i._R(2);p.dec=i._R(1);if(p.name.length)m.push(p);if(s!=2)i.l+=c?13:14;switch(p.type){case"B":if((!o||p.len!=8)&&t.WTF)console.log("Skipping "+p.name+":"+p.type);break;case"G":;case"P":if(t.WTF)console.log("Skipping "+p.name+":"+p.type);break;case"+":;case"0":;case"@":;case"C":;case"D":;case"F":;case"I":;case"L":;case"M":;case"N":;case"O":;case"T":;case"Y":break;default:throw new Error("Unknown Field Type: "+p.type);}}if(i[i.l]!==13)i.l=u-1;if(i._R(1)!==13)throw new Error("DBF Terminator not found "+i.l+" "+i[i.l]);i.l=u;var b=0,w=0;n[0]=[];for(w=0;w!=m.length;++w)n[0][w]=m[w].name;while(f-- >0){if(i[i.l]===42){i.l+=h;continue}++i.l;n[++b]=[];w=0;for(w=0;w!=m.length;++w){var k=i.slice(i.l,i.l+m[w].len);i.l+=m[w].len;ba(k,0);var y=typeof a!=="undefined"?a.utils.decode(d,k):O(k);switch(m[w].type){case"C":if(y.trim().length)n[b][w]=y.replace(/\s+$/,"");break;case"D":if(y.length===8)n[b][w]=new Date(+y.slice(0,4),+y.slice(4,6)-1,+y.slice(6,8));else n[b][w]=y;break;case"F":n[b][w]=parseFloat(y.trim());break;case"+":;case"I":n[b][w]=c?k._R(-4,"i")^2147483648:k._R(4,"i");break;case"L":switch(y.trim().toUpperCase()){case"Y":;case"T":n[b][w]=true;break;case"N":;case"F":n[b][w]=false;break;case"":;case"?":break;default:throw new Error("DBF Unrecognized L:|"+y+"|");}break;case"M":if(!l)throw new Error("DBF Unexpected MEMO for type "+s.toString(16));n[b][w]="##MEMO##"+(c?parseInt(y.trim(),10):k._R(4));break;case"N":y=y.replace(/\u0000/g,"").trim();if(y&&y!=".")n[b][w]=+y||0;break;case"@":n[b][w]=new Date(k._R(-8,"f")-621356832e5);break;case"T":n[b][w]=new Date((k._R(4)-2440588)*864e5+k._R(4));break;case"Y":n[b][w]=k._R(4,"i")/1e4+k._R(4,"i")/1e4*Math.pow(2,32);break;case"O":n[b][w]=-k._R(-8,"f");break;case"B":if(o&&m[w].len==8){n[b][w]=k._R(8,"f");break};case"G":;case"P":k.l+=m[w].len;break;case"0":if(m[w].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+m[w].type);}}}if(s!=2)if(i.l<i.length&&i[i.l++]!=26)throw new Error("DBF EOF Marker missing "+(i.l-1)+" of "+i.length+" "+i[i.l-1].toString(16));if(t&&t.sheetRows)n=n.slice(0,t.sheetRows);t.DBF=m;return n}function s(e,r){var t=r||{};if(!t.dateNF)t.dateNF="yyyymmdd";var a=Ya(i(e,t),t);a["!cols"]=t.DBF.map(function(e){return{wch:e.len,DBF:e}});delete t.DBF;return a}function l(e,r){try{var t=Xa(s(e,r),r);t.bookType="dbf";return t}catch(a){if(r&&r.WTF)throw a}return{SheetNames:[],Sheets:{}}}var c={B:8,C:250,L:1,D:8,"?":0,"":0};function f(i,s){var l=s||{};var f=r;if(+l.codepage>=0)o(+l.codepage);if(l.type=="string")throw new Error("Cannot write DBF to JS string");var u=xa();var h=ic(i,{header:1,raw:true,cellDates:true});var d=h[0],m=h.slice(1),p=i["!cols"]||[];var v=0,g=0,b=0,w=1;for(v=0;v<d.length;++v){if(((p[v]||{}).DBF||{}).name){d[v]=p[v].DBF.name;++b;continue}if(d[v]==null)continue;++b;if(typeof d[v]==="number")d[v]=d[v].toString(10);if(typeof d[v]!=="string")throw new Error("DBF Invalid column name "+d[v]+" |"+typeof d[v]+"|");if(d.indexOf(d[v])!==v)for(g=0;g<1024;++g)if(d.indexOf(d[v]+"_"+g)==-1){d[v]+="_"+g;break}}var k=ja(i["!ref"]);var y=[];var x=[];var S=[];for(v=0;v<=k.e.c-k.s.c;++v){var C="",_="",A=0;var E=[];for(g=0;g<m.length;++g){if(m[g][v]!=null)E.push(m[g][v])}if(E.length==0||d[v]==null){y[v]="?";continue}for(g=0;g<E.length;++g){switch(typeof E[g]){case"number":_="B";break;case"string":_="C";break;case"boolean":_="L";break;case"object":_=E[g]instanceof Date?"D":"C";break;default:_="C";}A=Math.max(A,(typeof a!=="undefined"&&typeof E[g]=="string"?a.utils.encode(t,E[g]):String(E[g])).length);C=C&&C!=_?"C":_}if(A>250)A=250;_=((p[v]||{}).DBF||{}).type;if(_=="C"){if(p[v].DBF.len>A)A=p[v].DBF.len}if(C=="B"&&_=="N"){C="N";S[v]=p[v].DBF.dec;A=p[v].DBF.len}x[v]=C=="C"||_=="N"?A:c[C]||0;w+=x[v];y[v]=C}var F=u.next(32);F._W(4,318902576);F._W(4,m.length);F._W(2,296+32*b);F._W(2,w);for(v=0;v<4;++v)F._W(4,0);var T=+n[r]||3;F._W(4,0|T<<8);if(e[T]!=+l.codepage){if(l.codepage)console.error("DBF Unsupported codepage "+r+", using 1252");r=1252}for(v=0,g=0;v<d.length;++v){if(d[v]==null)continue;var D=u.next(32);var O=(d[v].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);D._W(1,O,"sbcs");D._W(1,y[v]=="?"?"C":y[v],"sbcs");D._W(4,g);D._W(1,x[v]||c[y[v]]||0);D._W(1,S[v]||0);D._W(1,2);D._W(4,0);D._W(1,0);D._W(4,0);D._W(4,0);g+=x[v]||c[y[v]]||0}var M=u.next(264);M._W(4,13);for(v=0;v<65;++v)M._W(4,0);for(v=0;v<m.length;++v){var N=u.next(w);N._W(1,0);for(g=0;g<d.length;++g){if(d[g]==null)continue;switch(y[g]){case"L":N._W(1,m[v][g]==null?63:m[v][g]?84:70);break;case"B":N._W(8,m[v][g]||0,"f");break;case"N":var P="0";if(typeof m[v][g]=="number")P=m[v][g].toFixed(S[g]||0);if(P.length>x[g])P=P.slice(0,x[g]);for(b=0;b<x[g]-P.length;++b)N._W(1,32);N._W(1,P,"sbcs");break;case"D":if(!m[v][g])N._W(8,"00000000","sbcs");else{N._W(4,("0000"+m[v][g].getFullYear()).slice(-4),"sbcs");N._W(2,("00"+(m[v][g].getMonth()+1)).slice(-2),"sbcs");N._W(2,("00"+m[v][g].getDate()).slice(-2),"sbcs")}break;case"C":var I=N.l;var R=String(m[v][g]!=null?m[v][g]:"").slice(0,x[g]);N._W(1,R,"cpstr");I+=x[g]-N.l;for(b=0;b<I;++b)N._W(1,32);break;}}}r=f;u.next(1)._W(1,26);return u.end()}return{to_workbook:l,to_sheet:s,from_sheet:f}}();var Zn=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223};var r=new RegExp("N("+lr(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm");var t=function(r,t){var a=e[t];return typeof a=="number"?v(a):a};var n=function(e,r,t){var a=r.charCodeAt(0)-32<<4|t.charCodeAt(0)-48;return a==59?e:v(a)};e["|"]=254;function i(e,r){switch(r.type){case"base64":return s(S(e),r);case"binary":return s(e,r);case"buffer":return s(C&&Buffer.isBuffer(e)?e.toString("binary"):O(e),r);case"array":return s(Sr(e),r);}throw new Error("Unrecognized type "+r.type)}function s(e,i){var s=e.split(/[\n\r]+/),l=-1,c=-1,f=0,u=0,h=[];var d=[];var m=null;var p={},v=[],g=[],b=[];var w=0,k;var y={Workbook:{WBProps:{},Names:[]}};if(+i.codepage>=0)o(+i.codepage);for(;f!==s.length;++f){w=0;var x=s[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(r,t);var S=x.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")});var C=S[0],_;if(x.length>0)switch(C){case"ID":break;case"E":break;case"B":break;case"O":for(u=1;u<S.length;++u)switch(S[u].charAt(0)){case"V":{var A=parseInt(S[u].slice(1),10);if(A>=1&&A<=4)y.Workbook.WBProps.date1904=true}break;}break;case"W":break;case"P":switch(S[1].charAt(0)){case"P":d.push(x.slice(3).replace(/;;/g,";"));break;}break;case"NN":{var E={Sheet:0};for(u=1;u<S.length;++u)switch(S[u].charAt(0)){case"N":E.Name=S[u].slice(1);break;case"E":E.Ref=(i&&i.sheet||"Sheet1")+"!"+_s(S[u].slice(1));break;}y.Workbook.Names.push(E)}break;case"C":var F=false,T=false,D=false,O=false,M=-1,N=-1,P="",I="z";for(u=1;u<S.length;++u)switch(S[u].charAt(0)){case"A":break;case"X":c=parseInt(S[u].slice(1),10)-1;T=true;break;case"Y":l=parseInt(S[u].slice(1),10)-1;if(!T)c=0;for(k=h.length;k<=l;++k)h[k]=[];break;case"K":_=S[u].slice(1);if(_.charAt(0)==='"'){_=_.slice(1,_.length-1);I="s"}else if(_==="TRUE"||_==="FALSE"){_=_==="TRUE";I="b"}else if(!isNaN(Ar(_))){_=Ar(_);I="n";if(m!==null&&Be(m)&&i.cellDates){_=gr(y.Workbook.WBProps.date1904?_+1462:_);I="d"}}else if(!isNaN(Dr(_).getDate())){_=xr(_);I="d";if(!i.cellDates){I="n";_=dr(_,y.Workbook.WBProps.date1904)}}if(typeof a!=="undefined"&&typeof _=="string"&&(i||{}).type!="string"&&(i||{}).codepage)_=a.utils.decode(i.codepage,_);F=true;break;case"E":O=true;P=_s(S[u].slice(1),{r:l,c:c});break;case"S":D=true;break;case"G":break;case"R":M=parseInt(S[u].slice(1),10)-1;break;case"C":N=parseInt(S[u].slice(1),10)-1;break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+x);}if(F){if(!h[l][c])h[l][c]={t:I,v:_};else{h[l][c].t=I;h[l][c].v=_}if(m)h[l][c].z=m;if(i.cellText!==false&&m)h[l][c].w=je(h[l][c].z,h[l][c].v,{date1904:y.Workbook.WBProps.date1904});m=null}if(D){if(O)throw new Error("SYLK shared formula cannot have own formula");var R=M>-1&&h[M][N];if(!R||!R[1])throw new Error("SYLK shared formula cannot find base");P=Fs(R[1],{r:l-M,c:c-N})}if(P){if(!h[l][c])h[l][c]={t:"n",f:P};else h[l][c].f=P}break;case"F":var L=0;for(u=1;u<S.length;++u)switch(S[u].charAt(0)){case"X":c=parseInt(S[u].slice(1),10)-1;++L;break;case"Y":l=parseInt(S[u].slice(1),10)-1;for(k=h.length;k<=l;++k)h[k]=[];break;case"M":w=parseInt(S[u].slice(1),10)/20;break;case"F":break;case"G":break;case"P":m=d[parseInt(S[u].slice(1),10)];break;case"S":break;case"D":break;case"N":break;case"W":b=S[u].slice(1).split(" ");for(k=parseInt(b[0],10);k<=parseInt(b[1],10);++k){w=parseInt(b[2],10);g[k-1]=w===0?{hidden:true}:{wch:w}}break;case"C":c=parseInt(S[u].slice(1),10)-1;if(!g[c])g[c]={};break;case"R":l=parseInt(S[u].slice(1),10)-1;if(!v[l])v[l]={};if(w>0){v[l].hpt=w;v[l].hpx=Ni(w)}else if(w===0)v[l].hidden=true;
break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+x);}if(L<1)m=null;break;default:if(i&&i.WTF)throw new Error("SYLK bad record "+x);}}if(v.length>0)p["!rows"]=v;if(g.length>0)p["!cols"]=g;g.forEach(function(e){Ti(e)});if(i&&i.sheetRows)h=h.slice(0,i.sheetRows);return[h,p,y]}function l(e,r){var t=i(e,r);var a=t[0],n=t[1],s=t[2];var l=Cr(r);l.date1904=(((s||{}).Workbook||{}).WBProps||{}).date1904;var o=Ya(a,l);lr(n).forEach(function(e){o[e]=n[e]});var c=Xa(o,r);lr(s).forEach(function(e){c[e]=s[e]});c.bookType="sylk";return c}function c(e,r,t,a){var n="C;Y"+(t+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0;if(e.f&&!e.F)n+=";E"+Es(e.f,{r:t,c:a});break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+(e.v==null?"":String(e.v)).replace(/"/g,"").replace(/;/g,";;")+'"';break;}return n}function f(e,r){r.forEach(function(r,t){var a="F;W"+(t+1)+" "+(t+1)+" ";if(r.hidden)a+="0";else{if(typeof r.width=="number"&&!r.wpx)r.wpx=Ci(r.width);if(typeof r.wpx=="number"&&!r.wch)r.wch=_i(r.wpx);if(typeof r.wch=="number")a+=Math.round(r.wch)}if(a.charAt(a.length-1)!=" ")e.push(a)})}function u(e,r){r.forEach(function(r,t){var a="F;";if(r.hidden)a+="M0;";else if(r.hpt)a+="M"+20*r.hpt+";";else if(r.hpx)a+="M"+20*Mi(r.hpx)+";";if(a.length>2)e.push(a+"R"+(t+1))})}function h(e,r,t){var a=["ID;PSheetJS;N;E"],n=[];var i=ja(e["!ref"]),s;var l=e["!data"]!=null;var o="\r\n";var h=(((t||{}).Workbook||{}).WBProps||{}).date1904;a.push("P;PGeneral");a.push("F;P0;DG0G8;M255");if(e["!cols"])f(a,e["!cols"]);if(e["!rows"])u(a,e["!rows"]);a.push("B;Y"+(i.e.r-i.s.r+1)+";X"+(i.e.c-i.s.c+1)+";D"+[i.s.c,i.s.r,i.e.c,i.e.r].join(" "));a.push("O;L;D;B"+(h?";V4":"")+";K47;G100 0.001");for(var d=i.s.r;d<=i.e.r;++d){if(l&&!e["!data"][d])continue;var m=[];for(var p=i.s.c;p<=i.e.c;++p){s=l?e["!data"][d][p]:e[Na(p)+Ta(d)];if(!s||s.v==null&&(!s.f||s.F))continue;m.push(c(s,e,d,p,r))}n.push(m.join(o))}return a.join(o)+o+n.join(o)+o+"E"+o}return{to_workbook:l,from_sheet:h}}();var qn=function(){function e(e,t){switch(t.type){case"base64":return r(S(e),t);case"binary":return r(e,t);case"buffer":return r(C&&Buffer.isBuffer(e)?e.toString("binary"):O(e),t);case"array":return r(Sr(e),t);}throw new Error("Unrecognized type "+t.type)}function r(e,r){var t=e.split("\n"),a=-1,n=-1,i=0,s=[];for(;i!==t.length;++i){if(t[i].trim()==="BOT"){s[++a]=[];n=0;continue}if(a<0)continue;var l=t[i].trim().split(",");var o=l[0],c=l[1];++i;var f=t[i]||"";while((f.match(/["]/g)||[]).length&1&&i<t.length-1)f+="\n"+t[++i];f=f.trim();switch(+o){case-1:if(f==="BOT"){s[++a]=[];n=0;continue}else if(f!=="EOD")throw new Error("Unrecognized DIF special command "+f);break;case 0:if(f==="TRUE")s[a][n]=true;else if(f==="FALSE")s[a][n]=false;else if(!isNaN(Ar(c)))s[a][n]=Ar(c);else if(!isNaN(Dr(c).getDate()))s[a][n]=xr(c);else s[a][n]=c;++n;break;case 1:f=f.slice(1,f.length-1);f=f.replace(/""/g,'"');if(w&&f&&f.match(/^=".*"$/))f=f.slice(2,-1);s[a][n++]=f!==""?f:null;break;}if(f==="EOD")break}if(r&&r.sheetRows)s=s.slice(0,r.sheetRows);return s}function t(r,t){return Ya(e(r,t),t)}function a(e,r){var a=Xa(t(e,r),r);a.bookType="dif";return a}function n(e,r){return"0,"+String(e)+"\r\n"+r}function i(e){return'1,0\r\n"'+e.replace(/"/g,'""')+'"'}function s(e){var r=w;var t=ja(e["!ref"]);var a=e["!data"]!=null;var s=['TABLE\r\n0,1\r\n"sheetjs"\r\n',"VECTORS\r\n0,"+(t.e.r-t.s.r+1)+'\r\n""\r\n',"TUPLES\r\n0,"+(t.e.c-t.s.c+1)+'\r\n""\r\n','DATA\r\n0,0\r\n""\r\n'];for(var l=t.s.r;l<=t.e.r;++l){var o=a?e["!data"][l]:[];var c="-1,0\r\nBOT\r\n";for(var f=t.s.c;f<=t.e.c;++f){var u=a?o&&o[f]:e[Ba({r:l,c:f})];if(u==null){c+='1,0\r\n""\r\n';continue}switch(u.t){case"n":if(r){if(u.w!=null)c+="0,"+u.w+"\r\nV";else if(u.v!=null)c+=n(u.v,"V");else if(u.f!=null&&!u.F)c+=i("="+u.f);else c+='1,0\r\n""'}else{if(u.v==null)c+='1,0\r\n""';else c+=n(u.v,"V")}break;case"b":c+=u.v?n(1,"TRUE"):n(0,"FALSE");break;case"s":c+=i(!r||isNaN(+u.v)?u.v:'="'+u.v+'"');break;case"d":if(!u.w)u.w=je(u.z||Z[14],dr(xr(u.v)));if(r)c+=n(u.w,"V");else c+=i(u.w);break;default:c+='1,0\r\n""';}c+="\r\n"}s.push(c)}return s.join("")+"-1,0\r\nEOD"}return{to_workbook:a,to_sheet:t,from_sheet:s}}();var Qn=function(){function e(e){return e.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n")}function r(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(r,t){var a=r.split("\n"),n=-1,i=-1,s=0,l=[];for(;s!==a.length;++s){var o=a[s].trim().split(":");if(o[0]!=="cell")continue;var c=La(o[1]);if(l.length<=c.r)for(n=l.length;n<=c.r;++n)if(!l[n])l[n]=[];n=c.r;i=c.c;switch(o[2]){case"t":l[n][i]=e(o[3]);break;case"v":l[n][i]=+o[3];break;case"vtf":var f=o[o.length-1];case"vtc":switch(o[3]){case"nl":l[n][i]=+o[4]?true:false;break;default:l[n][i]=+o[4];break;}if(o[2]=="vtf")l[n][i]=[l[n][i],f];}}if(t&&t.sheetRows)l=l.slice(0,t.sheetRows);return l}function a(e,r){return Ya(t(e,r),r)}function n(e,r){return Xa(a(e,r),r)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n");var s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n";var l=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n");var o="--SocialCalcSpreadsheetControlSave--";function c(e){if(!e||!e["!ref"])return"";var t=[],a=[],n,i="";var s=za(e["!ref"]);var l=e["!data"]!=null;for(var o=s.s.r;o<=s.e.r;++o){for(var c=s.s.c;c<=s.e.c;++c){i=Ba({r:o,c:c});n=l?(e["!data"][o]||[])[c]:e[i];if(!n||n.v==null||n.t==="z")continue;a=["cell",i,"t"];switch(n.t){case"s":;case"str":a.push(r(n.v));break;case"n":if(!n.f){a[2]="v";a[3]=n.v}else{a[2]="vtf";a[3]="n";a[4]=n.v;a[5]=r(n.f)}break;case"b":a[2]="vt"+(n.f?"f":"c");a[3]="nl";a[4]=n.v?"1":"0";a[5]=r(n.f||(n.v?"TRUE":"FALSE"));break;case"d":var f=dr(xr(n.v));a[2]="vtc";a[3]="nd";a[4]=""+f;a[5]=n.w||je(n.z||Z[14],f);break;case"e":continue;}t.push(a.join(":"))}}t.push("sheet:c:"+(s.e.c-s.s.c+1)+":r:"+(s.e.r-s.s.r+1)+":tvf:1");t.push("valueformat:1:text-wiki");return t.join("\n")}function f(e){return[i,s,l,s,c(e),o].join("\n")}return{to_workbook:n,to_sheet:a,from_sheet:f}}();var ei=function(){function e(e,r,t,a,n){if(n.raw)r[t][a]=e;else if(e===""){}else if(e==="TRUE")r[t][a]=true;else if(e==="FALSE")r[t][a]=false;else if(!isNaN(Ar(e)))r[t][a]=Ar(e);else if(!isNaN(Dr(e).getDate()))r[t][a]=xr(e);else r[t][a]=e}function r(r,t){var a=t||{};var n=[];if(!r||r.length===0)return n;var i=r.split(/[\r\n]/);var s=i.length-1;while(s>=0&&i[s].length===0)--s;var l=10,o=0;var c=0;for(;c<=s;++c){o=i[c].indexOf(" ");if(o==-1)o=i[c].length;else o++;l=Math.max(l,o)}for(c=0;c<=s;++c){n[c]=[];var f=0;e(i[c].slice(0,l).trim(),n,c,f,a);for(f=1;f<=(i[c].length-l)/10+1;++f)e(i[c].slice(l+(f-1)*10,l+f*10).trim(),n,c,f,a)}if(a.sheetRows)n=n.slice(0,a.sheetRows);return n}var t={44:",",9:"\t",59:";",124:"|"};var n={44:3,9:2,59:1,124:0};function i(e){var r={},a=false,i=0,s=0;for(;i<e.length;++i){if((s=e.charCodeAt(i))==34)a=!a;else if(!a&&s in t)r[s]=(r[s]||0)+1}s=[];for(i in r)if(Object.prototype.hasOwnProperty.call(r,i)){s.push([r[i],i])}if(!s.length){r=n;for(i in r)if(Object.prototype.hasOwnProperty.call(r,i)){s.push([r[i],i])}}s.sort(function(e,r){return e[0]-r[0]||n[e[1]]-n[r[1]]});return t[s.pop()[1]]||44}function s(e,r){var t=r||{};var a="";if(b!=null&&t.dense==null)t.dense=b;var n={};if(t.dense)n["!data"]=[];var s={s:{c:0,r:0},e:{c:0,r:0}};if(e.slice(0,4)=="sep="){if(e.charCodeAt(5)==13&&e.charCodeAt(6)==10){a=e.charAt(4);e=e.slice(7)}else if(e.charCodeAt(5)==13||e.charCodeAt(5)==10){a=e.charAt(4);e=e.slice(6)}else a=i(e.slice(0,1024))}else if(t&&t.FS)a=t.FS;else a=i(e.slice(0,1024));var l=0,o=0,c=0;var f=0,u=0,h=a.charCodeAt(0),d=false,m=0,p=e.charCodeAt(0);var v=t.dateNF!=null?Ke(t.dateNF):null;function g(){var r=e.slice(f,u);if(r.slice(-1)=="\r")r=r.slice(0,-1);var a={};if(r.charAt(0)=='"'&&r.charAt(r.length-1)=='"')r=r.slice(1,-1).replace(/""/g,'"');if(r.length===0)a.t="z";else if(t.raw){a.t="s";a.v=r}else if(r.trim().length===0){a.t="s";a.v=r}else if(r.charCodeAt(0)==61){if(r.charCodeAt(1)==34&&r.charCodeAt(r.length-1)==34){a.t="s";a.v=r.slice(2,-1).replace(/""/g,'"')}else if(Ds(r)){a.t="n";a.f=r.slice(1)}else{a.t="s";a.v=r}}else if(r=="TRUE"){a.t="b";a.v=true}else if(r=="FALSE"){a.t="b";a.v=false}else if(!isNaN(c=Ar(r))){a.t="n";if(t.cellText!==false)a.w=r;a.v=c}else if(!isNaN((c=Dr(r)).getDate())||v&&r.match(v)){a.z=t.dateNF||Z[14];var i=0;if(v&&r.match(v)){r=Ze(r,t.dateNF,r.match(v)||[]);i=1;c=xr(r,i)}if(t.cellDates){a.t="d";a.v=c}else{a.t="n";a.v=dr(c)}if(t.cellText!==false)a.w=je(a.z,a.v instanceof Date?dr(a.v):a.v);if(!t.cellNF)delete a.z}else{a.t="s";a.v=r}if(a.t=="z"){}else if(t.dense){if(!n["!data"][l])n["!data"][l]=[];n["!data"][l][o]=a}else n[Ba({c:o,r:l})]=a;f=u+1;p=e.charCodeAt(f);if(s.e.c<o)s.e.c=o;if(s.e.r<l)s.e.r=l;if(m==h)++o;else{o=0;++l;if(t.sheetRows&&t.sheetRows<=l)return true}}e:for(;u<e.length;++u)switch(m=e.charCodeAt(u)){case 34:if(p===34)d=!d;break;case 13:if(d)break;if(e.charCodeAt(u+1)==10)++u;case h:;case 10:if(!d&&g())break e;break;default:break;}if(u-f>0)g();n["!ref"]=$a(s);return n}function l(e,t){if(!(t&&t.PRN))return s(e,t);if(t.FS)return s(e,t);if(e.slice(0,4)=="sep=")return s(e,t);if(e.indexOf("\t")>=0||e.indexOf(",")>=0||e.indexOf(";")>=0)return s(e,t);return Ya(r(e,t),t)}function o(e,r){var t="",n=r.type=="string"?[0,0,0,0]:No(e,r);switch(r.type){case"base64":t=S(e);break;case"binary":t=e;break;case"buffer":if(r.codepage==65001)t=e.toString("utf8");else if(r.codepage&&typeof a!=="undefined")t=a.utils.decode(r.codepage,e);else t=C&&Buffer.isBuffer(e)?e.toString("binary"):O(e);break;case"array":t=Sr(e);break;case"string":t=e;break;default:throw new Error("Unrecognized type "+r.type);}if(n[0]==239&&n[1]==187&&n[2]==191)t=gt(t.slice(3));else if(r.type!="string"&&r.type!="buffer"&&r.codepage==65001)t=gt(t);else if(r.type=="binary"&&typeof a!=="undefined"&&r.codepage)t=a.utils.decode(r.codepage,a.utils.encode(28591,t));if(t.slice(0,19)=="socialcalc:version:")return Qn.to_sheet(r.type=="string"?t:gt(t),r);return l(t,r)}function c(e,r){return Xa(o(e,r),r)}function f(e){var r=[];var t=ja(e["!ref"]),a;var n=e["!data"]!=null;for(var i=t.s.r;i<=t.e.r;++i){var s=[];for(var l=t.s.c;l<=t.e.c;++l){var o=Ba({r:i,c:l});a=n?(e["!data"][i]||[])[l]:e[o];if(!a||a.v==null){s.push("          ");continue}var c=(a.w||(Va(a),a.w)||"").slice(0,10);while(c.length<10)c+=" ";s.push(c+(l===0?" ":""))}r.push(s.join(""))}return r.join("\n")}return{to_workbook:c,to_sheet:o,from_sheet:f}}();function ri(e,r){var t=r||{},a=!!t.WTF;t.WTF=true;try{var n=Zn.to_workbook(e,t);t.WTF=a;return n}catch(i){t.WTF=a;if(!i.message.match(/SYLK bad record ID/)&&a)throw i;return ei.to_workbook(e,r)}}function ti(e){var r={},t=e.match(Jr),a=0;var n=false;if(t)for(;a!=t.length;++a){var s=qr(t[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":;case"<shadow/>":r.shadow=1;break;case"</shadow>":break;case"<charset":if(s.val=="1")break;r.cp=i[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":;case"<outline/>":r.outline=1;break;case"</outline>":break;case"<rFont":r.name=s.val;break;case"<sz":r.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":;case"<strike/>":r.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":r.uval="double";break;case"singleAccounting":r.uval="single-accounting";break;case"doubleAccounting":r.uval="double-accounting";break;};case"<u>":;case"<u/>":r.u=1;break;case"</u>":break;case"<b":if(s.val=="0")break;case"<b>":;case"<b/>":r.b=1;break;case"</b>":break;case"<i":if(s.val=="0")break;case"<i>":;case"<i/>":r.i=1;break;case"</i>":break;case"<color":if(s.rgb)r.color=s.rgb.slice(2,8);break;case"<color>":;case"<color/>":;case"</color>":break;case"<family":r.family=s.val;break;case"<family>":;case"<family/>":;case"</family>":break;case"<vertAlign":r.valign=s.val;break;case"<vertAlign>":;case"<vertAlign/>":;case"</vertAlign>":break;case"<scheme":break;case"<scheme>":;case"<scheme/>":;case"</scheme>":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":n=true;break;case"</ext>":n=false;break;default:if(s[0].charCodeAt(1)!==47&&!n)throw new Error("Unrecognized rich format "+s[0]);}}return r}var ai=function(){var e=wt("t"),r=wt("rPr");function t(t){var a=t.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:tt(a[1])};var i=t.match(r);if(i)n.s=ti(i[1]);return n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function i(e){return e.replace(a,"").split(n).map(t).filter(function(e){return e.v})}}();var ni=function Wc(){var e=/(\r\n|\n)/g;function r(e,r,t){var a=[];if(e.u)a.push("text-decoration: underline;");if(e.uval)a.push("text-underline-style:"+e.uval+";");if(e.sz)a.push("font-size:"+e.sz+"pt;");if(e.outline)a.push("text-effect: outline;");if(e.shadow)a.push("text-shadow: auto;");r.push('<span style="'+a.join("")+'">');if(e.b){r.push("<b>");t.push("</b>")}if(e.i){r.push("<i>");t.push("</i>")}if(e.strike){r.push("<s>");t.push("</s>")}var n=e.valign||"";if(n=="superscript"||n=="super")n="sup";else if(n=="subscript")n="sub";if(n!=""){r.push("<"+n+">");t.push("</"+n+">")}t.push("</span>");return e}function t(t){var a=[[],t.v,[]];if(!t.v)return"";if(t.s)r(t.s,a[0],a[2]);return a[0].join("")+a[1].replace(e,"<br/>")+a[2].join("")}return function a(e){return e.map(t).join("")}}();var ii=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,si=/<(?:\w+:)?r\b[^>]*>/;var li=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function oi(e,r){var t=r?r.cellHTML:true;var a={};if(!e)return{t:""};if(e.match(/^\s*<(?:\w+:)?t[^>]*>/)){a.t=tt(gt(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||""),true);a.r=gt(e);if(t)a.h=ot(a.t)}else if(e.match(si)){a.r=gt(e);a.t=tt(gt((e.replace(li,"").match(ii)||[]).join("").replace(Jr,"")),true);if(t)a.h=ni(ai(a.r))}return a}var ci=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/;var fi=/<(?:\w+:)?(?:si|sstItem)>/g;var ui=/<\/(?:\w+:)?(?:si|sstItem)>/;function hi(e,r){var t=[],a="";if(!e)return t;var n=e.match(ci);if(n){a=n[2].replace(fi,"").split(ui);for(var i=0;i!=a.length;++i){var s=oi(a[i].trim(),r);if(s!=null)t[t.length]=s}n=qr(n[1]);t.Count=n.count;t.Unique=n.uniqueCount}return t}var di=/^\s|\s$|[\t\n\r]/;function mi(e,r){if(!r.bookSST)return"";var t=[Vr];t[t.length]=Ft("sst",null,{xmlns:Pt[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a){if(e[a]==null)continue;var n=e[a];var i="<si>";if(n.r)i+=n.r;else{i+="<t";if(!n.t)n.t="";if(typeof n.t!=="string")n.t=String(n.t);if(n.t.match(di))i+=' xml:space="preserve"';i+=">"+it(n.t)+"</t>"}i+="</si>";t[t.length]=i}if(t.length>2){t[t.length]="</sst>";t[1]=t[1].replace("/>",">")}return t.join("")}function pi(e){var r=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(r.slice(0,2),16),parseInt(r.slice(2,4),16),parseInt(r.slice(4,6),16)]}function vi(e){for(var r=0,t=1;r!=3;++r)t=t*256+(e[r]>255?255:e[r]<0?0:e[r]);return t.toString(16).toUpperCase().slice(1)}function gi(e){var r=e[0]/255,t=e[1]/255,a=e[2]/255;var n=Math.max(r,t,a),i=Math.min(r,t,a),s=n-i;if(s===0)return[0,0,r];var l=0,o=0,c=n+i;o=s/(c>1?2-c:c);switch(n){case r:l=((t-a)/s+6)%6;break;case t:l=(a-r)/s+2;break;case a:l=(r-t)/s+4;break;}return[l/6,o,c/2]}function bi(e){var r=e[0],t=e[1],a=e[2];var n=t*2*(a<.5?a:1-a),i=a-n/2;var s=[i,i,i],l=6*r;var o;if(t!==0)switch(l|0){case 0:;case 6:o=n*l;s[0]+=n;s[1]+=o;break;case 1:o=n*(2-l);s[0]+=o;s[1]+=n;break;case 2:o=n*(l-2);s[1]+=n;s[2]+=o;break;case 3:o=n*(4-l);s[1]+=o;s[2]+=n;break;case 4:o=n*(l-4);s[2]+=n;s[0]+=o;break;case 5:o=n*(6-l);s[2]+=o;s[0]+=n;break;}for(var c=0;c!=3;++c)s[c]=Math.round(s[c]*255);return s}function wi(e,r){if(r===0)return e;var t=gi(pi(e));if(r<0)t[2]=t[2]*(1+r);else t[2]=1-(1-t[2])*(1-r);return vi(bi(t))}var ki=6,yi=15,xi=1,Si=ki;function Ci(e){return Math.floor((e+Math.round(128/Si)/256)*Si)}function _i(e){return Math.floor((e-5)/Si*100+.5)/100}function Ai(e){return Math.round((e*Si+5)/Si*256)/256}function Ei(e){return Ai(_i(Ci(e)))}function Fi(e){var r=Math.abs(e-Ei(e)),t=Si;if(r>.005)for(Si=xi;Si<yi;++Si)if(Math.abs(e-Ei(e))<=r){r=Math.abs(e-Ei(e));t=Si}Si=t}function Ti(e){if(e.width){e.wpx=Ci(e.width);e.wch=_i(e.wpx);e.MDW=Si}else if(e.wpx){e.wch=_i(e.wpx);e.width=Ai(e.wch);e.MDW=Si}else if(typeof e.wch=="number"){e.width=Ai(e.wch);e.wpx=Ci(e.width);e.MDW=Si}if(e.customWidth)delete e.customWidth}var Di=96,Oi=Di;function Mi(e){return e*96/Oi}function Ni(e){return e*Oi/96}var Pi={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function Ii(e,r,t,a){r.Borders=[];var n={};var i=false;(e[0].match(Jr)||[]).forEach(function(e){var t=qr(e);switch(Qr(t[0])){case"<borders":;case"<borders>":;case"</borders>":break;case"<border":;case"<border>":;case"<border/>":n={};if(t.diagonalUp)n.diagonalUp=ht(t.diagonalUp);if(t.diagonalDown)n.diagonalDown=ht(t.diagonalDown);r.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":;case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":;case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":;case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":;case"<bottom>":break;case"</bottom>":break;case"<diagonal":;case"<diagonal>":;case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":;case"<horizontal>":;case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":;case"<vertical>":;case"<vertical/>":break;case"</vertical>":break;case"<start":;case"<start>":;case"<start/>":break;case"</start>":break;case"<end":;case"<end>":;case"<end/>":break;case"</end>":break;case"<color":;case"<color>":break;case"<color/>":;case"</color>":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":i=true;break;case"</ext>":i=false;break;default:if(a&&a.WTF){if(!i)throw new Error("unrecognized "+t[0]+" in borders")};}})}function Ri(e,r,t,a){r.Fills=[];var n={};var i=false;(e[0].match(Jr)||[]).forEach(function(e){var t=qr(e);switch(Qr(t[0])){case"<fills":;case"<fills>":;case"</fills>":break;case"<fill>":;case"<fill":;case"<fill/>":n={};r.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":;case"</gradientFill>":r.Fills.push(n);n={};break;case"<patternFill":;case"<patternFill>":if(t.patternType)n.patternType=t.patternType;break;case"<patternFill/>":;case"</patternFill>":break;case"<bgColor":if(!n.bgColor)n.bgColor={};if(t.indexed)n.bgColor.indexed=parseInt(t.indexed,10);if(t.theme)n.bgColor.theme=parseInt(t.theme,10);if(t.tint)n.bgColor.tint=parseFloat(t.tint);if(t.rgb)n.bgColor.rgb=t.rgb.slice(-6);break;case"<bgColor/>":;case"</bgColor>":break;case"<fgColor":if(!n.fgColor)n.fgColor={};if(t.theme)n.fgColor.theme=parseInt(t.theme,10);if(t.tint)n.fgColor.tint=parseFloat(t.tint);if(t.rgb!=null)n.fgColor.rgb=t.rgb.slice(-6);break;case"<fgColor/>":;case"</fgColor>":break;case"<stop":;case"<stop/>":break;case"</stop>":break;case"<color":;case"<color/>":break;case"</color>":break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":i=true;break;case"</ext>":i=false;break;default:if(a&&a.WTF){if(!i)throw new Error("unrecognized "+t[0]+" in fills")};}})}function Li(e,r,t,a){r.Fonts=[];var n={};var s=false;(e[0].match(Jr)||[]).forEach(function(e){var l=qr(e);switch(Qr(l[0])){case"<fonts":;case"<fonts>":;case"</fonts>":break;case"<font":;case"<font>":break;case"</font>":;case"<font/>":r.Fonts.push(n);n={};break;case"<name":if(l.val)n.name=gt(l.val);break;case"<name/>":;case"</name>":break;case"<b":n.bold=l.val?ht(l.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=l.val?ht(l.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(l.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34;break;}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=l.val?ht(l.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=l.val?ht(l.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=l.val?ht(l.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=l.val?ht(l.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=l.val?ht(l.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":if(l.val)n.sz=+l.val;break;case"<sz/>":;case"</sz>":break;case"<vertAlign":if(l.val)n.vertAlign=l.val;break;case"<vertAlign/>":;case"</vertAlign>":break;case"<family":if(l.val)n.family=parseInt(l.val,10);break;case"<family/>":;case"</family>":break;case"<scheme":if(l.val)n.scheme=l.val;break;case"<scheme/>":;case"</scheme>":break;case"<charset":if(l.val=="1")break;l.codepage=i[parseInt(l.val,10)];break;case"<color":if(!n.color)n.color={};if(l.auto)n.color.auto=ht(l.auto);if(l.rgb)n.color.rgb=l.rgb.slice(-6);else if(l.indexed){n.color.index=parseInt(l.indexed,10);var o=pn[n.color.index];if(n.color.index==81)o=pn[1];if(!o)o=pn[1];n.color.rgb=o[0].toString(16)+o[1].toString(16)+o[2].toString(16)}else if(l.theme){n.color.theme=parseInt(l.theme,10);if(l.tint)n.color.tint=parseFloat(l.tint);if(l.theme&&t.themeElements&&t.themeElements.clrScheme){n.color.rgb=wi(t.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)}}break;case"<color/>":;case"</color>":break;case"<AlternateContent":s=true;break;case"</AlternateContent>":s=false;break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":s=true;break;case"</ext>":s=false;break;default:if(a&&a.WTF){if(!s)throw new Error("unrecognized "+l[0]+" in fonts")};}})}function Bi(e,r,t){r.NumberFmt=[];var a=lr(Z);for(var n=0;n<a.length;++n)r.NumberFmt[a[n]]=Z[a[n]];var i=e[0].match(Jr);if(!i)return;for(n=0;n<i.length;++n){var s=qr(i[n]);switch(Qr(s[0])){case"<numFmts":;case"</numFmts>":;case"<numFmts/>":;case"<numFmts>":break;case"<numFmt":{var l=tt(gt(s.formatCode)),o=parseInt(s.numFmtId,10);r.NumberFmt[o]=l;if(o>0){if(o>392){for(o=392;o>60;--o)if(r.NumberFmt[o]==null)break;r.NumberFmt[o]=l}Qe(l,o)}}break;case"</numFmt>":break;default:if(t.WTF)throw new Error("unrecognized "+s[0]+" in numFmts");}}}function zi(e){var r=["<numFmts>"];[[5,8],[23,26],[41,44],[50,392]].forEach(function(t){for(var a=t[0];a<=t[1];++a)if(e[a]!=null)r[r.length]=Ft("numFmt",null,{numFmtId:a,formatCode:it(e[a])})});if(r.length===1)return"";r[r.length]="</numFmts>";r[0]=Ft("numFmts",null,{count:r.length-2}).replace("/>",">");return r.join("")}var $i=["numFmtId","fillId","fontId","borderId","xfId"];var Wi=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function Ui(e,r,t){r.CellXf=[];var a;var n=false;(e[0].match(Jr)||[]).forEach(function(e){var i=qr(e),s=0;switch(Qr(i[0])){case"<cellXfs":;case"<cellXfs>":;case"<cellXfs/>":;case"</cellXfs>":break;case"<xf":;case"<xf/>":a=i;delete a[0];for(s=0;s<$i.length;++s)if(a[$i[s]])a[$i[s]]=parseInt(a[$i[s]],10);for(s=0;s<Wi.length;++s)if(a[Wi[s]])a[Wi[s]]=ht(a[Wi[s]]);if(r.NumberFmt&&a.numFmtId>392){for(s=392;s>60;--s)if(r.NumberFmt[a.numFmtId]==r.NumberFmt[s]){a.numFmtId=s;break}}r.CellXf.push(a);break;case"</xf>":break;case"<alignment":;case"<alignment/>":var l={};if(i.vertical)l.vertical=i.vertical;if(i.horizontal)l.horizontal=i.horizontal;if(i.textRotation!=null)l.textRotation=i.textRotation;if(i.indent)l.indent=i.indent;if(i.wrapText)l.wrapText=ht(i.wrapText);a.alignment=l;break;case"</alignment>":break;case"<protection":break;case"</protection>":;case"<protection/>":break;case"<AlternateContent":n=true;break;case"</AlternateContent>":n=false;break;case"<extLst":;case"<extLst>":;case"</extLst>":break;case"<ext":n=true;break;case"</ext>":n=false;break;default:if(t&&t.WTF){if(!n)throw new Error("unrecognized "+i[0]+" in cellXfs")};}})}function ji(e){var r=[];r[r.length]=Ft("cellXfs",null);e.forEach(function(e){r[r.length]=Ft("xf",null,e)});r[r.length]="</cellXfs>";if(r.length===2)return"";r[0]=Ft("cellXfs",null,{count:r.length-2}).replace("/>",">");return r.join("")}var Hi=function Uc(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/;var r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/;var t=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/;var a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/;var n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function i(s,l,o){var c={};if(!s)return c;s=s.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var f;if(f=s.match(e))Bi(f,c,o);if(f=s.match(a))Li(f,c,l,o);if(f=s.match(t))Ri(f,c,l,o);if(f=s.match(n))Ii(f,c,l,o);if(f=s.match(r))Ui(f,c,o);return c}}();function Vi(e,r){var t=[Vr,Ft("styleSheet",null,{xmlns:Pt[0],"xmlns:vt":Nt.vt})],a;if(e.SSF&&(a=zi(e.SSF))!=null)t[t.length]=a;t[t.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>';t[t.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>';t[t.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>';t[t.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>';if(a=ji(r.cellXfs))t[t.length]=a;t[t.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>';t[t.length]='<dxfs count="0"/>';t[t.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>';if(t.length>2){t[t.length]="</styleSheet>";t[1]=t[1].replace("/>",">")}return t.join("")}var Xi=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Gi(e,r,t){r.themeElements.clrScheme=[];var a={};(e[0].match(Jr)||[]).forEach(function(e){var n=qr(e);switch(n[0]){case"<a:clrScheme":;case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":;case"</a:dk1>":;case"<a:lt1>":;case"</a:lt1>":;case"<a:dk2>":;case"</a:dk2>":;case"<a:lt2>":;case"</a:lt2>":;case"<a:accent1>":;case"</a:accent1>":;case"<a:accent2>":;case"</a:accent2>":;case"<a:accent3>":;case"</a:accent3>":;case"<a:accent4>":;case"</a:accent4>":;case"<a:accent5>":;case"</a:accent5>":;case"<a:accent6>":;case"</a:accent6>":;case"<a:hlink>":;case"</a:hlink>":;case"<a:folHlink>":;case"</a:folHlink>":if(n[0].charAt(1)==="/"){r.themeElements.clrScheme[Xi.indexOf(n[0])]=a;a={}}else{a.name=n[0].slice(3,n[0].length-1)}break;default:if(t&&t.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme");}})}function Yi(){}function Ji(){}var Ki=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/;var Zi=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/;var qi=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function Qi(e,r,t){r.themeElements={};var a;[["clrScheme",Ki,Gi],["fontScheme",Zi,Yi],["fmtScheme",qi,Ji]].forEach(function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,r,t)})}var es=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function rs(e,r){if(!e||e.length===0)e=ts();var t;var a={};if(!(t=e.match(es)))throw new Error("themeElements not found in theme");Qi(t[0],a,r);a.raw=e;return a}function ts(e,r){if(r&&r.themeXLSX)return r.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var t=[Vr];t[t.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">';t[t.length]="<a:themeElements>";t[t.length]='<a:clrScheme name="Office">';t[t.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>';t[t.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>';t[t.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>';t[t.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>';t[t.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>';t[t.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>';t[t.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>';t[t.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>';t[t.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>';t[t.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>';t[t.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>';t[t.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>';t[t.length]="</a:clrScheme>";t[t.length]='<a:fontScheme name="Office">';t[t.length]="<a:majorFont>";t[t.length]='<a:latin typeface="Cambria"/>';t[t.length]='<a:ea typeface=""/>';t[t.length]='<a:cs typeface=""/>';t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>';t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>';t[t.length]='<a:font script="Hans" typeface="宋体"/>';t[t.length]='<a:font script="Hant" typeface="新細明體"/>';t[t.length]='<a:font script="Arab" typeface="Times New Roman"/>';t[t.length]='<a:font script="Hebr" typeface="Times New Roman"/>';t[t.length]='<a:font script="Thai" typeface="Tahoma"/>';t[t.length]='<a:font script="Ethi" typeface="Nyala"/>';t[t.length]='<a:font script="Beng" typeface="Vrinda"/>';t[t.length]='<a:font script="Gujr" typeface="Shruti"/>';t[t.length]='<a:font script="Khmr" typeface="MoolBoran"/>';t[t.length]='<a:font script="Knda" typeface="Tunga"/>';t[t.length]='<a:font script="Guru" typeface="Raavi"/>';t[t.length]='<a:font script="Cans" typeface="Euphemia"/>';t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>';t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>';t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>';t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>';t[t.length]='<a:font script="Deva" typeface="Mangal"/>';t[t.length]='<a:font script="Telu" typeface="Gautami"/>';t[t.length]='<a:font script="Taml" typeface="Latha"/>';t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>';t[t.length]='<a:font script="Orya" typeface="Kalinga"/>';t[t.length]='<a:font script="Mlym" typeface="Kartika"/>';t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>';t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>';t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>';t[t.length]='<a:font script="Viet" typeface="Times New Roman"/>';t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>';t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>';t[t.length]="</a:majorFont>";t[t.length]="<a:minorFont>";t[t.length]='<a:latin typeface="Calibri"/>';t[t.length]='<a:ea typeface=""/>';t[t.length]='<a:cs typeface=""/>';t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>';t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>';t[t.length]='<a:font script="Hans" typeface="宋体"/>';t[t.length]='<a:font script="Hant" typeface="新細明體"/>';t[t.length]='<a:font script="Arab" typeface="Arial"/>';t[t.length]='<a:font script="Hebr" typeface="Arial"/>';t[t.length]='<a:font script="Thai" typeface="Tahoma"/>';t[t.length]='<a:font script="Ethi" typeface="Nyala"/>';t[t.length]='<a:font script="Beng" typeface="Vrinda"/>';t[t.length]='<a:font script="Gujr" typeface="Shruti"/>';t[t.length]='<a:font script="Khmr" typeface="DaunPenh"/>';t[t.length]='<a:font script="Knda" typeface="Tunga"/>';t[t.length]='<a:font script="Guru" typeface="Raavi"/>';t[t.length]='<a:font script="Cans" typeface="Euphemia"/>';t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>';t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>';t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>';t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>';t[t.length]='<a:font script="Deva" typeface="Mangal"/>';t[t.length]='<a:font script="Telu" typeface="Gautami"/>';t[t.length]='<a:font script="Taml" typeface="Latha"/>';t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>';
t[t.length]='<a:font script="Orya" typeface="Kalinga"/>';t[t.length]='<a:font script="Mlym" typeface="Kartika"/>';t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>';t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>';t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>';t[t.length]='<a:font script="Viet" typeface="Arial"/>';t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>';t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>';t[t.length]="</a:minorFont>";t[t.length]="</a:fontScheme>";t[t.length]='<a:fmtScheme name="Office">';t[t.length]="<a:fillStyleLst>";t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>';t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:lin ang="16200000" scaled="1"/>';t[t.length]="</a:gradFill>";t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:lin ang="16200000" scaled="0"/>';t[t.length]="</a:gradFill>";t[t.length]="</a:fillStyleLst>";t[t.length]="<a:lnStyleLst>";t[t.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>';t[t.length]="</a:lnStyleLst>";t[t.length]="<a:effectStyleLst>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]="</a:effectStyle>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]="</a:effectStyle>";t[t.length]="<a:effectStyle>";t[t.length]="<a:effectLst>";t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>';t[t.length]="</a:effectLst>";t[t.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>';t[t.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>';t[t.length]="</a:effectStyle>";t[t.length]="</a:effectStyleLst>";t[t.length]="<a:bgFillStyleLst>";t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>';t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>';t[t.length]="</a:gradFill>";t[t.length]='<a:gradFill rotWithShape="1">';t[t.length]="<a:gsLst>";t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>';t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>';t[t.length]="</a:gsLst>";t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>';t[t.length]="</a:gradFill>";t[t.length]="</a:bgFillStyleLst>";t[t.length]="</a:fmtScheme>";t[t.length]="</a:themeElements>";t[t.length]="<a:objectDefaults>";t[t.length]="<a:spDef>";t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>';t[t.length]="</a:spDef>";t[t.length]="<a:lnDef>";t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>';t[t.length]="</a:lnDef>";t[t.length]="</a:objectDefaults>";t[t.length]="<a:extraClrSchemeLst/>";t[t.length]="</a:theme>";return t.join("")}function as(e,r,t){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n=false;var i=2;var s;e.replace(Jr,function(e){var r=qr(e);switch(Qr(r[0])){case"<?xml":break;case"<metadata":;case"</metadata>":break;case"<metadataTypes":;case"</metadataTypes>":break;case"<metadataType":a.Types.push({name:r.name});break;case"</metadataType>":break;case"<futureMetadata":for(var l=0;l<a.Types.length;++l)if(a.Types[l].name==r.name)s=a.Types[l];break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":if(i==1)a.Cell.push({type:a.Types[r.t-1].name,index:+r.v});else if(i==0)a.Value.push({type:a.Types[r.t-1].name,index:+r.v});break;case"</rc>":break;case"<cellMetadata":i=1;break;case"</cellMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"</valueMetadata>":i=2;break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":n=true;break;case"</ext>":n=false;break;case"<rvb":if(!s)break;if(!s.offsets)s.offsets=[];s.offsets.push(+r.i);break;default:if(!n&&(t==null?void 0:t.WTF))throw new Error("unrecognized "+r[0]+" in metadata");}return e});return a}function ns(){var e=[Vr];e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>');return e.join("")}function is(){}function ss(e,r,t,a){if(!e)return e;var n=a||{};var i=false,s=false;ya(e,function l(e,r,t){if(s)return;switch(t){case 359:;case 363:;case 364:;case 366:;case 367:;case 368:;case 369:;case 370:;case 371:;case 472:;case 577:;case 578:;case 579:;case 580:;case 581:;case 582:;case 583:;case 584:;case 585:;case 586:;case 587:break;case 35:i=true;break;case 36:i=false;break;default:if(r.T){}else if(!i||n.WTF)throw new Error("Unexpected record 0x"+t.toString(16));}},n)}function ls(e,r){if(!e)return"??";var t=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return r["!id"][t].Target}function os(e,r){var t=[21600,21600];var a=["m0,0l0",t[1],t[0],t[1],t[0],"0xe"].join(",");var n=[Ft("xml",null,{"xmlns:v":It.v,"xmlns:o":It.o,"xmlns:x":It.x,"xmlns:mv":It.mv}).replace(/\/>/,">"),Ft("o:shapelayout",Ft("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"})];var i=65536*e;var s=r||[];if(s.length>0)n.push(Ft("v:shapetype",[Ft("v:stroke",null,{joinstyle:"miter"}),Ft("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202",coordsize:t.join(","),"o:spt":202,path:a}));s.forEach(function(e){++i;n.push(cs(e,i))});n.push("</xml>");return n.join("")}function cs(e,r){var t=La(e[0]);var a={color2:"#BEFF82",type:"gradient"};if(a.type=="gradient")a.angle="-180";var n=a.type=="gradient"?Ft("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null;var i=Ft("v:fill",n,a);var s={on:"t",obscured:"t"};return["<v:shape"+Et({id:"_x0000_s"+r,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,Ft("v:shadow",null,s),Ft("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",At("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),At("x:AutoFill","False"),At("x:Row",String(t.r)),At("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"].join("")}function fs(e,r,t,a){var n=e["!data"]!=null;var i;r.forEach(function(r){var s=La(r.ref);if(s.r<0||s.c<0)return;if(n){if(!e["!data"][s.r])e["!data"][s.r]=[];i=e["!data"][s.r][s.c]}else i=e[r.ref];if(!i){i={t:"z"};if(n)e["!data"][s.r][s.c]=i;else e[r.ref]=i;var l=ja(e["!ref"]||"BDWGO1000001:A1");if(l.s.r>s.r)l.s.r=s.r;if(l.e.r<s.r)l.e.r=s.r;if(l.s.c>s.c)l.s.c=s.c;if(l.e.c<s.c)l.e.c=s.c;var o=$a(l);e["!ref"]=o}if(!i.c)i.c=[];var c={a:r.author,t:r.t,r:r.r,T:t};if(r.h)c.h=r.h;for(var f=i.c.length-1;f>=0;--f){if(!t&&i.c[f].T)return;if(t&&!i.c[f].T)i.c.splice(f,1)}if(t&&a)for(f=0;f<a.length;++f){if(c.a==a[f].id){c.a=a[f].name||c.a;break}}i.c.push(c)})}function us(e,r){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var t=[];var a=[];var n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);if(n&&n[1])n[1].split(/<\/\w*:?author>/).forEach(function(e){if(e===""||e.trim()==="")return;var r=e.match(/<(?:\w+:)?author[^>]*>(.*)/);if(r)t.push(r[1])});var i=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);if(i&&i[1])i[1].split(/<\/\w*:?comment>/).forEach(function(e){if(e===""||e.trim()==="")return;var n=e.match(/<(?:\w+:)?comment[^>]*>/);if(!n)return;var i=qr(n[0]);var s={author:i.authorId&&t[i.authorId]||"sheetjsghost",ref:i.ref,guid:i.guid};var l=La(i.ref);if(r.sheetRows&&r.sheetRows<=l.r)return;var o=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/);var c=!!o&&!!o[1]&&oi(o[1])||{r:"",t:"",h:""};s.r=c.r;if(c.r=="<t></t>")c.t=c.h="";s.t=(c.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n");if(r.cellHTML)s.h=c.h;a.push(s)});return a}function hs(e){var r=[Vr,Ft("comments",null,{xmlns:Pt[0]})];var t=[];r.push("<authors>");e.forEach(function(e){e[1].forEach(function(e){var a=it(e.a);if(t.indexOf(a)==-1){t.push(a);r.push("<author>"+a+"</author>")}if(e.T&&e.ID&&t.indexOf("tc="+e.ID)==-1){t.push("tc="+e.ID);r.push("<author>"+"tc="+e.ID+"</author>")}})});if(t.length==0){t.push("SheetJ5");r.push("<author>SheetJ5</author>")}r.push("</authors>");r.push("<commentList>");e.forEach(function(e){var a=0,n=[],i=0;if(e[1][0]&&e[1][0].T&&e[1][0].ID)a=t.indexOf("tc="+e[1][0].ID);e[1].forEach(function(e){if(e.a)a=t.indexOf(it(e.a));if(e.T)++i;n.push(e.t==null?"":it(e.t))});if(i===0){e[1].forEach(function(a){r.push('<comment ref="'+e[0]+'" authorId="'+t.indexOf(it(a.a))+'"><text>');r.push(At("t",a.t==null?"":it(a.t)));r.push("</text></comment>")})}else{r.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>');var s="Comment:\n    "+n[0]+"\n";for(var l=1;l<n.length;++l)s+="Reply:\n    "+n[l]+"\n";r.push(At("t",it(s)));r.push("</text></comment>")}});r.push("</commentList>");if(r.length>2){r[r.length]="</comments>";r[1]=r[1].replace("/>",">")}return r.join("")}function ds(e,r){var t=[];var a=false,n={},i=0;e.replace(Jr,function s(l,o){var c=qr(l);switch(Qr(c[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":n={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":if(n.t!=null)t.push(n);break;case"<text>":;case"<text":i=o+l.length;break;case"</text>":n.t=e.slice(i,o).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":;case"<mentions>":a=true;break;case"</mentions>":a=false;break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":a=true;break;case"</ext>":a=false;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+c[0]+" in threaded comments");}return l});return t}function ms(e,r,t){var a=[Vr,Ft("ThreadedComments",null,{xmlns:Nt.TCMNT}).replace(/[\/]>/,">")];e.forEach(function(e){var n="";(e[1]||[]).forEach(function(i,s){if(!i.T){delete i.ID;return}if(i.a&&r.indexOf(i.a)==-1)r.push(i.a);var l={ref:e[0],id:"{54EE7951-**************-"+("000000000000"+t.tcid++).slice(-12)+"}"};if(s==0)n=l.id;else l.parentId=n;i.ID=l.id;if(i.a)l.personId="{54EE7950-**************-"+("000000000000"+r.indexOf(i.a)).slice(-12)+"}";a.push(Ft("threadedComment",At("text",i.t||""),l))})});a.push("</ThreadedComments>");return a.join("")}function ps(e,r){var t=[];var a=false;e.replace(Jr,function n(e){var n=qr(e);switch(Qr(n[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":t.push({name:n.displayname,id:n.id});break;case"</person>":break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":a=true;break;case"</ext>":a=false;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+n[0]+" in threaded comments");}return e});return t}function vs(e){var r=[Vr,Ft("personList",null,{xmlns:Nt.TCMNT,"xmlns:x":Pt[0]}).replace(/[\/]>/,">")];e.forEach(function(e,t){r.push(Ft("person",null,{displayName:e,id:"{54EE7950-**************-"+("000000000000"+t).slice(-12)+"}",userId:e,providerId:"None"}))});r.push("</personList>");return r.join("")}var gs="application/vnd.ms-office.vbaProject";function bs(e){var r=rr.utils.cfb_new({root:"R"});e.FullPaths.forEach(function(t,a){if(t.slice(-1)==="/"||!t.match(/_VBA_PROJECT_CUR/))return;var n=t.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");rr.utils.cfb_add(r,n,e.FileIndex[a].content)});return rr.write(r)}function ws(e,r){r.FullPaths.forEach(function(t,a){if(a==0)return;var n=t.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");if(n.slice(-1)!=="/")rr.utils.cfb_add(e,n,r.FileIndex[a].content)})}var ks=["xlsb","xlsm","xlam","biff8","xla"];function ys(){return{"!type":"dialog"}}function xs(){return{"!type":"dialog"}}function Ss(){return{"!type":"macro"}}function Cs(){return{"!type":"macro"}}var _s=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g;var r={r:0,c:0};function t(e,t,a,n){var i=false,s=false;if(a.length==0)s=true;else if(a.charAt(0)=="["){s=true;a=a.slice(1,-1)}if(n.length==0)i=true;else if(n.charAt(0)=="["){i=true;n=n.slice(1,-1)}var l=a.length>0?parseInt(a,10)|0:0,o=n.length>0?parseInt(n,10)|0:0;if(i)o+=r.c;else--o;if(s)l+=r.r;else--l;return t+(i?"":"$")+Na(o)+(s?"":"$")+Ta(l)}return function a(n,i){r=i;return n.replace(e,t)}}();var As=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g;var Es=function(){return function e(r,t){return r.replace(As,function(e,r,a,n,i,s){var l=Ma(n)-(a?0:t.c);var o=Fa(s)-(i?0:t.r);var c=i=="$"?o+1:o==0?"":"["+o+"]";var f=a=="$"?l+1:l==0?"":"["+l+"]";return r+"R"+c+"C"+f})}}();function Fs(e,r){return e.replace(As,function(e,t,a,n,i,s){return t+(a=="$"?a+n:Na(Ma(n)+r.c))+(i=="$"?i+s:Ta(Fa(s)+r.r))})}function Ts(e,r,t){var a=za(r),n=a.s,i=La(t);var s={r:i.r-n.r,c:i.c-n.c};return Fs(e,s)}function Ds(e){if(e.length==1)return false;return true}function Os(e){return e.replace(/_xlfn\./g,"")}function Ms(e){if(e.slice(0,3)=="of:")e=e.slice(3);if(e.charCodeAt(0)==61){e=e.slice(1);if(e.charCodeAt(0)==61)e=e.slice(1)}e=e.replace(/COM\.MICROSOFT\./g,"");e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,r){return r.replace(/\./g,"")});e=e.replace(/\$'([^']|'')+'/g,function(e){return e.slice(1)});e=e.replace(/\$([^\]\. #$]+)/g,function(e,r){return r.match(/^([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])?(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})?$/)?e:r});e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1");return e.replace(/[;~]/g,",").replace(/\|/g,";")}function Ns(e){var r="of:="+e.replace(As,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return r.replace(/;/g,"|").replace(/,/g,";")}function Ps(e){e=e.replace(/\$'([^']|'')+'/g,function(e){return e.slice(1)});e=e.replace(/\$([^\]\. #$]+)/g,function(e,r){return r.match(/^([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])?(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})?$/)?e:r});var r=e.split(":");var t=r[0].split(".")[0];return[t,r[0].split(".")[1]+(r.length>1?":"+(r[1].split(".")[1]||r[1].split(".")[0]):"")]}function Is(e){return e.replace(/!/,".")}var Rs={};var Ls={};var Bs=typeof Map!=="undefined";function zs(e,r,t){var a=0,n=e.length;if(t){if(Bs?t.has(r):Object.prototype.hasOwnProperty.call(t,r)){var i=Bs?t.get(r):t[r];for(;a<i.length;++a){if(e[i[a]].t===r){e.Count++;return i[a]}}}}else for(;a<n;++a){if(e[a].t===r){e.Count++;return a}}e[n]={t:r};e.Count++;e.Unique++;if(t){if(Bs){if(!t.has(r))t.set(r,[]);t.get(r).push(n)}else{if(!Object.prototype.hasOwnProperty.call(t,r))t[r]=[];t[r].push(n)}}return n}function $s(e,r){var t={min:e+1,max:e+1};var a=-1;if(r.MDW)Si=r.MDW;if(r.width!=null)t.customWidth=1;else if(r.wpx!=null)a=_i(r.wpx);else if(r.wch!=null)a=r.wch;if(a>-1){t.width=Ai(a);t.customWidth=1}else if(r.width!=null)t.width=r.width;if(r.hidden)t.hidden=true;if(r.level!=null){t.outlineLevel=t.level=r.level}return t}function Ws(e,r){if(!e)return;var t=[.7,.7,.75,.75,.3,.3];if(r=="xlml")t=[1,1,1,1,.5,.5];if(e.left==null)e.left=t[0];if(e.right==null)e.right=t[1];if(e.top==null)e.top=t[2];if(e.bottom==null)e.bottom=t[3];if(e.header==null)e.header=t[4];if(e.footer==null)e.footer=t[5]}function Us(e,r,t){var a=t.revssf[r.z!=null?r.z:"General"];var n=60,i=e.length;if(a==null&&t.ssf){for(;n<392;++n)if(t.ssf[n]==null){Qe(r.z,n);t.ssf[n]=r.z;t.revssf[r.z]=a=n;break}}for(n=0;n!=i;++n)if(e[n].numFmtId===a)return n;e[i]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1};return i}function js(e,r,t,a,n,i){try{if(a.cellNF)e.z=Z[r]}catch(s){if(a.WTF)throw s}if(e.t==="z"&&!a.cellStyles)return;if(e.t==="d"&&typeof e.v==="string")e.v=xr(e.v);if((!a||a.cellText!==false)&&e.t!=="z")try{if(Z[r]==null)Qe(Ye[r]||"General",r);if(e.t==="e")e.w=e.w||vn[e.v];else if(r===0){if(e.t==="n"){if((e.v|0)===e.v)e.w=e.v.toString(10);else e.w=fe(e.v)}else if(e.t==="d"){var l=dr(e.v);if((l|0)===l)e.w=l.toString(10);else e.w=fe(l)}else if(e.v===undefined)return"";else e.w=ue(e.v,Ls)}else if(e.t==="d")e.w=je(r,dr(e.v),Ls);else e.w=je(r,e.v,Ls)}catch(s){if(a.WTF)throw s}if(!a.cellStyles)return;if(t!=null)try{e.s=i.Fills[t];if(e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb){e.s.fgColor.rgb=wi(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0);if(a.WTF)e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb}if(e.s.bgColor&&e.s.bgColor.theme){e.s.bgColor.rgb=wi(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0);if(a.WTF)e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb}}catch(s){if(a.WTF&&i.Fills)throw s}}function Hs(e,r,t){if(e&&e["!ref"]){var a=ja(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+t+"): "+e["!ref"])}}function Vs(e,r){var t=ja(r);if(t.s.r<=t.e.r&&t.s.c<=t.e.c&&t.s.r>=0&&t.s.c>=0)e["!ref"]=$a(t)}var Xs=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g;var Gs=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/;var Ys=/<(?:\w:)?hyperlink [^>]*>/gm;var Js=/"(\w*:\w*)"/;var Ks=/<(?:\w:)?col\b[^>]*[\/]?>/g;var Zs=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g;var qs=/<(?:\w:)?pageMargins[^>]*\/>/g;var Qs=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/;var el=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/;var rl=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function tl(e,r,t,a,n,i,s){if(!e)return e;if(!a)a={"!id":{}};if(b!=null&&r.dense==null)r.dense=b;var l={};if(r.dense)l["!data"]=[];var o={s:{r:2e6,c:2e6},e:{r:0,c:0}};var c="",f="";var u=e.match(Gs);if(u){c=e.slice(0,u.index);f=e.slice(u.index+u[0].length)}else c=f=e;var h=c.match(Qs);if(h)nl(h[0],l,n,t);else if(h=c.match(el))il(h[0],h[1]||"",l,n,t,s,i);var d=(c.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var m=c.slice(d,d+50).match(Js);if(m&&!(r&&r.nodim))Vs(l,m[1])}var p=c.match(rl);if(p&&p[1])bl(p[1],n);var v=[];if(r.cellStyles){var g=c.match(Ks);if(g)dl(v,g)}if(u)yl(u[1],l,r,o,i,s);var w=f.match(Zs);if(w)l["!autofilter"]=pl(w[0]);var k=[];var y=f.match(Xs);if(y)for(d=0;d!=y.length;++d)k[d]=ja(y[d].slice(y[d].indexOf('"')+1));var x=f.match(Ys);if(x)fl(l,x,a);var S=f.match(qs);if(S)l["!margins"]=ul(qr(S[0]));if(r&&r.nodim)o.s.c=o.s.r=0;if(!l["!ref"]&&o.e.c>=o.s.c&&o.e.r>=o.s.r)l["!ref"]=$a(o);if(r.sheetRows>0&&l["!ref"]){var C=ja(l["!ref"]);if(r.sheetRows<=+C.e.r){C.e.r=r.sheetRows-1;if(C.e.r>o.e.r)C.e.r=o.e.r;if(C.e.r<C.s.r)C.s.r=C.e.r;if(C.e.c>o.e.c)C.e.c=o.e.c;if(C.e.c<C.s.c)C.s.c=C.e.c;l["!fullref"]=l["!ref"];l["!ref"]=$a(C)}}if(v.length>0)l["!cols"]=v;if(k.length>0)l["!merges"]=k;return l}function al(e){if(e.length===0)return"";var r='<mergeCells count="'+e.length+'">';for(var t=0;t!=e.length;++t)r+='<mergeCell ref="'+$a(e[t])+'"/>';return r+"</mergeCells>"}function nl(e,r,t,a){var n=qr(e);if(!t.Sheets[a])t.Sheets[a]={};if(n.codeName)t.Sheets[a].CodeName=tt(gt(n.codeName))}function il(e,r,t,a,n){nl(e.slice(0,e.indexOf(">")),t,a,n)}function sl(e,r,t,a,n){var i=false;var s={},l=null;if(a.bookType!=="xlsx"&&r.vbaraw){var o=r.SheetNames[t];try{if(r.Workbook)o=r.Workbook.Sheets[t].CodeName||o}catch(c){}i=true;s.codeName=bt(it(o))}if(e&&e["!outline"]){var f={summaryBelow:1,summaryRight:1};if(e["!outline"].above)f.summaryBelow=0;if(e["!outline"].left)f.summaryRight=0;l=(l||"")+Ft("outlinePr",null,f)}if(!i&&!l)return;n[n.length]=Ft("sheetPr",l,s)}var ll=["objects","scenarios","selectLockedCells","selectUnlockedCells"];var ol=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function cl(e){var r={sheet:1};ll.forEach(function(t){if(e[t]!=null&&e[t])r[t]="1"});ol.forEach(function(t){if(e[t]!=null&&!e[t])r[t]="0"});if(e.password)r.password=crypto_CreatePasswordVerifier_Method1(e.password).toString(16).toUpperCase();return Ft("sheetProtection",null,r)}function fl(e,r,t){var a=e["!data"]!=null;for(var n=0;n!=r.length;++n){var i=qr(gt(r[n]),true);if(!i.ref)return;var s=((t||{})["!id"]||[])[i.id];if(s){i.Target=s.Target;if(i.location)i.Target+="#"+tt(i.location)}else{i.Target="#"+tt(i.location);s={Target:i.Target,TargetMode:"Internal"}}i.Rel=s;if(i.tooltip){i.Tooltip=i.tooltip;delete i.tooltip}var l=ja(i.ref);for(var o=l.s.r;o<=l.e.r;++o)for(var c=l.s.c;c<=l.e.c;++c){var f=Na(c)+Ta(o);if(a){if(!e["!data"][o])e["!data"][o]=[];if(!e["!data"][o][c])e["!data"][o][c]={t:"z",v:undefined};e["!data"][o][c].l=i}else{if(!e[f])e[f]={t:"z",v:undefined};e[f].l=i}}}}function ul(e){var r={};["left","right","top","bottom","header","footer"].forEach(function(t){if(e[t])r[t]=parseFloat(e[t])});return r}function hl(e){Ws(e);return Ft("pageMargins",null,e)}function dl(e,r){var t=false;for(var a=0;a!=r.length;++a){var n=qr(r[a],true);if(n.hidden)n.hidden=ht(n.hidden);var i=parseInt(n.min,10)-1,s=parseInt(n.max,10)-1;if(n.outlineLevel)n.level=+n.outlineLevel||0;delete n.min;delete n.max;n.width=+n.width;if(!t&&n.width){t=true;Fi(n.width)}Ti(n);while(i<=s)e[i++]=Cr(n)}}function ml(e,r){var t=["<cols>"],a;for(var n=0;n!=r.length;++n){if(!(a=r[n]))continue;t[t.length]=Ft("col",null,$s(n,a))}t[t.length]="</cols>";return t.join("")}function pl(e){var r={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return r}function vl(e,r,t,a){var n=typeof e.ref=="string"?e.ref:$a(e.ref);if(!t.Workbook)t.Workbook={Sheets:[]};if(!t.Workbook.Names)t.Workbook.Names=[];var i=t.Workbook.Names;var s=za(n);if(s.s.r==s.e.r){s.e.r=za(r["!ref"]).e.r;n=$a(s)}for(var l=0;l<i.length;++l){var o=i[l];if(o.Name!="_xlnm._FilterDatabase")continue;if(o.Sheet!=a)continue;o.Ref=Ua(t.SheetNames[a])+"!"+Wa(n);break}if(l==i.length)i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+t.SheetNames[a]+"'!"+n});return Ft("autoFilter",null,{ref:n})}var gl=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/g;function bl(e,r){if(!r.Views)r.Views=[{}];(e.match(gl)||[]).forEach(function(e,t){var a=qr(e);if(!r.Views[t])r.Views[t]={};if(+a.zoomScale)r.Views[t].zoom=+a.zoomScale;if(a.rightToLeft&&ht(a.rightToLeft))r.Views[t].RTL=true})}function wl(e,r,t,a){var n={workbookViewId:"0"};if((((a||{}).Workbook||{}).Views||[])[0])n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0";return Ft("sheetViews",Ft("sheetView",null,n),{})}function kl(e,r,t,a){if(e.c)t["!comments"].push([r,e.c]);if((e.v===undefined||e.t==="z"&&!(a||{}).sheetStubs)&&typeof e.f!=="string"&&typeof e.z=="undefined")return"";var n="";var i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=vn[e.v];break;case"d":if(a&&a.cellDates)n=xr(e.v,-1).toISOString();else{e=Cr(e);e.t="n";n=""+(e.v=dr(xr(e.v)))}if(typeof e.z==="undefined")e.z=Z[14];break;default:n=e.v;break;}var l=e.t=="z"||e.v==null?"":At("v",it(n)),o={r:r};var c=Us(a.cellXfs,e,a);if(c!==0)o.s=c;switch(e.t){case"n":break;case"d":o.t="d";break;case"b":o.t="b";break;case"e":o.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){l=At("v",""+zs(a.Strings,e.v,a.revStrings));o.t="s";break}else o.t="str";break;}if(e.t!=i){e.t=i;e.v=s}if(typeof e.f=="string"&&e.f){var f=e.F&&e.F.slice(0,r.length)==r?{t:"array",ref:e.F}:null;l=Ft("f",it(e.f),f)+(e.v!=null?l:"")}if(e.l){e.l.display=it(n);t["!links"].push([r,e.l])}if(e.D)o.cm=1;return Ft("c",l,o)}var yl=function(){var e=/<(?:\w+:)?c[ \/>]/,r=/<\/(?:\w+:)?row>/;var t=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/;var n=/ref=["']([^"']*)["']/;var i=wt("v"),s=wt("f");return function l(o,c,f,u,h,d){var m=0,p="",v=[],g=[],b=0,w=0,k=0,y="",x;var S,C=0,_=0;var A,E;var F=0,T=0;var D=Array.isArray(d.CellXf),O;var M=[];var N=[];var P=c["!data"]!=null;var I=[],R={},L=false;var B=!!f.sheetStubs;for(var z=o.split(r),$=0,W=z.length;$!=W;++$){p=z[$].trim();var U=p.length;if(U===0)continue;var j=0;e:for(m=0;m<U;++m)switch(p[m]){case">":if(p[m-1]!="/"){++m;break e}if(f&&f.cellStyles){S=qr(p.slice(j,m),true);C=S.r!=null?parseInt(S.r,10):C+1;_=-1;if(f.sheetRows&&f.sheetRows<C)continue;R={};L=false;if(S.ht){L=true;R.hpt=parseFloat(S.ht);R.hpx=Ni(R.hpt)}if(S.hidden&&ht(S.hidden)){L=true;R.hidden=true}if(S.outlineLevel!=null){L=true;R.level=+S.outlineLevel}if(L)I[C-1]=R}break;case"<":j=m;break;}if(j>=m)break;S=qr(p.slice(j,m),true);C=S.r!=null?parseInt(S.r,10):C+1;_=-1;if(f.sheetRows&&f.sheetRows<C)continue;if(!f.nodim){if(u.s.r>C-1)u.s.r=C-1;if(u.e.r<C-1)u.e.r=C-1}if(f&&f.cellStyles){R={};L=false;if(S.ht){L=true;R.hpt=parseFloat(S.ht);R.hpx=Ni(R.hpt)}if(S.hidden&&ht(S.hidden)){L=true;R.hidden=true}if(S.outlineLevel!=null){L=true;R.level=+S.outlineLevel}if(L)I[C-1]=R}v=p.slice(m).split(e);for(var H=0;H!=v.length;++H)if(v[H].trim().charAt(0)!="<")break;v=v.slice(H);for(m=0;m!=v.length;++m){p=v[m].trim();if(p.length===0)continue;g=p.match(t);b=m;w=0;k=0;p="<c "+(p.slice(0,1)=="<"?">":"")+p;if(g!=null&&g.length===2){b=0;y=g[1];for(w=0;w!=y.length;++w){if((k=y.charCodeAt(w)-64)<1||k>26)break;b=26*b+k}--b;_=b}else++_;for(w=0;w!=p.length;++w)if(p.charCodeAt(w)===62)break;++w;S=qr(p.slice(0,w),true);if(!S.r)S.r=Ba({r:C-1,c:_});y=p.slice(w);x={t:""};if((g=y.match(i))!=null&&g[1]!=="")x.v=tt(g[1]);if(f.cellFormula){if((g=y.match(s))!=null&&g[1]!==""){x.f=tt(gt(g[1]),true);if(!f.xlfn)x.f=Os(x.f);if(g[0].indexOf('t="array"')>-1){x.F=(y.match(n)||[])[1];if(x.F.indexOf(":")>-1)M.push([ja(x.F),x.F])}else if(g[0].indexOf('t="shared"')>-1){E=qr(g[0]);var V=tt(gt(g[1]));if(!f.xlfn)V=Os(V);N[parseInt(E.si,10)]=[E,V,S.r]}}else if(g=y.match(/<f[^>]*\/>/)){E=qr(g[0]);if(N[E.si])x.f=Ts(N[E.si][1],N[E.si][2],S.r)}var X=La(S.r);for(w=0;w<M.length;++w)if(X.r>=M[w][0].s.r&&X.r<=M[w][0].e.r)if(X.c>=M[w][0].s.c&&X.c<=M[w][0].e.c)x.F=M[w][1]}if(S.t==null&&x.v===undefined){if(x.f||x.F){x.v=0;x.t="n"}else if(!B)continue;else x.t="z"}else x.t=S.t||"n";if(u.s.c>_)u.s.c=_;if(u.e.c<_)u.e.c=_;switch(x.t){case"n":if(x.v==""||x.v==null){if(!B)continue;x.t="z"}else x.v=parseFloat(x.v);break;case"s":if(typeof x.v=="undefined"){if(!B)continue;x.t="z"}else{A=Rs[parseInt(x.v,10)];x.v=A.t;x.r=A.r;if(f.cellHTML)x.h=A.h}break;case"str":x.t="s";x.v=x.v!=null?tt(gt(x.v),true):"";if(f.cellHTML)x.h=ot(x.v);break;case"inlineStr":g=y.match(a);x.t="s";if(g!=null&&(A=oi(g[1]))){x.v=A.t;if(f.cellHTML)x.h=A.h}else x.v="";break;case"b":x.v=ht(x.v);break;case"d":if(f.cellDates)x.v=xr(x.v,1);else{x.v=dr(xr(x.v,1));x.t="n"}break;case"e":if(!f||f.cellText!==false)x.w=x.v;x.v=gn[x.v];break;}F=T=0;O=null;if(D&&S.s!==undefined){O=d.CellXf[S.s];if(O!=null){if(O.numFmtId!=null)F=O.numFmtId;if(f.cellStyles){if(O.fillId!=null)T=O.fillId}}}js(x,F,T,f,h,d);if(f.cellDates&&D&&x.t=="n"&&Be(Z[F])){x.t="d";x.v=gr(x.v)}if(S.cm&&f.xlmeta){var G=(f.xlmeta.Cell||[])[+S.cm-1];if(G&&G.type=="XLDAPR")x.D=true}var Y;if(f.nodim){Y=La(S.r);if(u.s.r>Y.r)u.s.r=Y.r;if(u.e.r<Y.r)u.e.r=Y.r}if(P){Y=La(S.r);if(!c["!data"][Y.r])c["!data"][Y.r]=[];c["!data"][Y.r][Y.c]=x}else c[S.r]=x}}if(I.length>0)c["!rows"]=I}}();function xl(e,r,t,a){var n=[],i=[],s=ja(e["!ref"]),l="",o,c="",f=[],u=0,h=0,d=e["!rows"];var m=e["!data"]!=null;var p={r:c},v,g=-1;for(h=s.s.c;h<=s.e.c;++h)f[h]=Na(h);for(u=s.s.r;u<=s.e.r;++u){i=[];c=Ta(u);for(h=s.s.c;h<=s.e.c;++h){o=f[h]+c;var b=m?(e["!data"][u]||[])[h]:e[o];if(b===undefined)continue;if((l=kl(b,o,e,r,t,a))!=null)i.push(l)}if(i.length>0||d&&d[u]){p={r:c};if(d&&d[u]){v=d[u];if(v.hidden)p.hidden=1;g=-1;if(v.hpx)g=Mi(v.hpx);else if(v.hpt)g=v.hpt;if(g>-1){p.ht=g;p.customHeight=1}if(v.level){p.outlineLevel=v.level}}n[n.length]=Ft("row",i.join(""),p)}}if(d)for(;u<d.length;++u){if(d&&d[u]){p={r:u+1};v=d[u];if(v.hidden)p.hidden=1;g=-1;if(v.hpx)g=Mi(v.hpx);else if(v.hpt)g=v.hpt;if(g>-1){p.ht=g;p.customHeight=1}if(v.level){p.outlineLevel=v.level}n[n.length]=Ft("row","",p)}}return n.join("")}function Sl(e,r,t,a){var n=[Vr,Ft("worksheet",null,{xmlns:Pt[0],"xmlns:r":Nt.r})];var i=t.SheetNames[e],s=0,l="";var o=t.Sheets[i];if(o==null)o={};var c=o["!ref"]||"A1";var f=ja(c);if(f.e.c>16383||f.e.r>1048575){if(r.WTF)throw new Error("Range "+c+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383);f.e.r=Math.min(f.e.c,1048575);c=$a(f)}if(!a)a={};o["!comments"]=[];var u=[];sl(o,t,e,r,n);n[n.length]=Ft("dimension",null,{ref:c});n[n.length]=wl(o,r,e,t);if(r.sheetFormat)n[n.length]=Ft("sheetFormatPr",null,{defaultRowHeight:r.sheetFormat.defaultRowHeight||"16",baseColWidth:r.sheetFormat.baseColWidth||"10",outlineLevelRow:r.sheetFormat.outlineLevelRow||"7"});if(o["!cols"]!=null&&o["!cols"].length>0)n[n.length]=ml(o,o["!cols"]);n[s=n.length]="<sheetData/>";o["!links"]=[];if(o["!ref"]!=null){l=xl(o,r,e,t,a);if(l.length>0)n[n.length]=l}if(n.length>s+1){n[n.length]="</sheetData>";
n[s]=n[s].replace("/>",">")}if(o["!protect"])n[n.length]=cl(o["!protect"]);if(o["!autofilter"]!=null)n[n.length]=vl(o["!autofilter"],o,t,e);if(o["!merges"]!=null&&o["!merges"].length>0)n[n.length]=al(o["!merges"]);var h=-1,d,m=-1;if(o["!links"].length>0){n[n.length]="<hyperlinks>";o["!links"].forEach(function(e){if(!e[1].Target)return;d={ref:e[0]};if(e[1].Target.charAt(0)!="#"){m=Fn(a,-1,it(e[1].Target).replace(/#.*$/,""),Cn.HLINK);d["r:id"]="rId"+m}if((h=e[1].Target.indexOf("#"))>-1)d.location=it(e[1].Target.slice(h+1));if(e[1].Tooltip)d.tooltip=it(e[1].Tooltip);d.display=e[1].display;n[n.length]=Ft("hyperlink",null,d)});n[n.length]="</hyperlinks>"}delete o["!links"];if(o["!margins"]!=null)n[n.length]=hl(o["!margins"]);if(!r||r.ignoreEC||r.ignoreEC==void 0)n[n.length]=At("ignoredErrors",Ft("ignoredError",null,{numberStoredAsText:1,sqref:c}));if(u.length>0){m=Fn(a,-1,"../drawings/drawing"+(e+1)+".xml",Cn.DRAW);n[n.length]=Ft("drawing",null,{"r:id":"rId"+m});o["!drawing"]=u}if(o["!comments"].length>0){m=Fn(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Cn.VML);n[n.length]=Ft("legacyDrawing",null,{"r:id":"rId"+m});o["!legacy"]=m}if(n.length>1){n[n.length]="</worksheet>";n[1]=n[1].replace("/>",">")}return n.join("")}function Cl(e){var r=[];var t=e.match(/^<c:numCache>/);var a;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach(function(e){var a=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);if(!a)return;r[+a[1]]=t?+a[2]:a[2]});var n=tt((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);(e.match(/<c:f>(.*?)<\/c:f>/gm)||[]).forEach(function(e){a=e.replace(/<.*?>/g,"")});return[r,n,a]}function _l(e,r,t,a,n,i){var s=i||{"!type":"chart"};if(!e)return i;var l=0,o=0,c="A";var f={s:{r:2e6,c:2e6},e:{r:0,c:0}};(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(e){var r=Cl(e);f.s.r=f.s.c=0;f.e.c=l;c=Na(l);r[0].forEach(function(e,t){if(s["!data"]){if(!s["!data"][t])s["!data"][t]=[];s["!data"][t][l]={t:"n",v:e,z:r[1]}}else s[c+Ta(t)]={t:"n",v:e,z:r[1]};o=t});if(f.e.r<o)f.e.r=o;++l});if(l>0)s["!ref"]=$a(f);return s}function Al(e,r,t,a,n){if(!e)return e;if(!a)a={"!id":{}};var i={"!type":"chart","!drawel":null,"!rel":""};var s;var l=e.match(Qs);if(l)nl(l[0],i,n,t);if(s=e.match(/drawing r:id="(.*?)"/))i["!rel"]=s[1];if(a["!id"][i["!rel"]])i["!drawel"]=a["!id"][i["!rel"]];return i}var El=[["allowRefreshQuery",false,"bool"],["autoCompressPictures",true,"bool"],["backupFile",false,"bool"],["checkCompatibility",false,"bool"],["CodeName",""],["date1904",false,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",false,"bool"],["hidePivotFieldList",false,"bool"],["promptedSolutions",false,"bool"],["publishItems",false,"bool"],["refreshAllConnections",false,"bool"],["saveExternalLinkValues",true,"bool"],["showBorderUnselectedTables",true,"bool"],["showInkAnnotation",true,"bool"],["showObjects","all"],["showPivotChartFilter",false,"bool"],["updateLinks","userSet"]];var Fl=[["activeTab",0,"int"],["autoFilterDateGrouping",true,"bool"],["firstSheet",0,"int"],["minimized",false,"bool"],["showHorizontalScroll",true,"bool"],["showSheetTabs",true,"bool"],["showVerticalScroll",true,"bool"],["tabRatio",600,"int"],["visibility","visible"]];var Tl=[];var Dl=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function Ol(e,r){for(var t=0;t!=e.length;++t){var a=e[t];for(var n=0;n!=r.length;++n){var i=r[n];if(a[i[0]]==null)a[i[0]]=i[1];else switch(i[2]){case"bool":if(typeof a[i[0]]=="string")a[i[0]]=ht(a[i[0]]);break;case"int":if(typeof a[i[0]]=="string")a[i[0]]=parseInt(a[i[0]],10);break;}}}}function Ml(e,r){for(var t=0;t!=r.length;++t){var a=r[t];if(e[a[0]]==null)e[a[0]]=a[1];else switch(a[2]){case"bool":if(typeof e[a[0]]=="string")e[a[0]]=ht(e[a[0]]);break;case"int":if(typeof e[a[0]]=="string")e[a[0]]=parseInt(e[a[0]],10);break;}}}function Nl(e){Ml(e.WBProps,El);Ml(e.CalcPr,Dl);Ol(e.WBView,Fl);Ol(e.Sheets,Tl);Ls.date1904=ht(e.WBProps.date1904)}function Pl(e){if(!e.Workbook)return"false";if(!e.Workbook.WBProps)return"false";return ht(e.Workbook.WBProps.date1904)?"true":"false"}var Il=":][*?/\\".split("");function Rl(e,r){try{if(e=="")throw new Error("Sheet name cannot be blank");if(e.length>31)throw new Error("Sheet name cannot exceed 31 chars");if(e.charCodeAt(0)==39||e.charCodeAt(e.length-1)==39)throw new Error("Sheet name cannot start or end with apostrophe (')");if(e.toLowerCase()=="history")throw new Error("Sheet name cannot be 'History'");Il.forEach(function(r){if(e.indexOf(r)==-1)return;throw new Error("Sheet name cannot contain : \\ / ? * [ ]")})}catch(t){if(r)return false;throw t}return true}function Ll(e,r,t){e.forEach(function(a,n){Rl(a);for(var i=0;i<n;++i)if(a==e[i])throw new Error("Duplicate Sheet Name: "+a);if(t){var s=r&&r[n]&&r[n].CodeName||a;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function Bl(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var r=e.Workbook&&e.Workbook.Sheets||[];Ll(e.SheetNames,r,!!e.vbaraw);for(var t=0;t<e.SheetNames.length;++t)Hs(e.Sheets[e.SheetNames[t]],e.SheetNames[t],t);e.SheetNames.forEach(function(r,t){var a=e.Sheets[r];if(!a||!a["!autofilter"])return;var n;if(!e.Workbook)e.Workbook={};if(!e.Workbook.Names)e.Workbook.Names=[];e.Workbook.Names.forEach(function(e){if(e.Name=="_xlnm._FilterDatabase"&&e.Sheet==t)n=e});var i=Ua(r)+"!"+Wa(a["!autofilter"].ref);if(n)n.Ref=i;else e.Workbook.Names.push({Name:"_xlnm._FilterDatabase",Sheet:t,Ref:i})})}var zl=/<\w+:workbook/;function $l(e,r){if(!e)throw new Error("Could not find file");var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""};var a=false,n="xmlns";var i={},s=0;e.replace(Jr,function l(o,c){var f=qr(o);switch(Qr(f[0])){case"<?xml":break;case"<workbook":if(o.match(zl))n="xmlns"+o.match(/<(\w+):/)[1];t.xmlns=f[n];break;case"</workbook>":break;case"<fileVersion":delete f[0];t.AppVersion=f;break;case"<fileVersion/>":;case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":;case"<workbookPr/>":El.forEach(function(e){if(f[e[0]]==null)return;switch(e[2]){case"bool":t.WBProps[e[0]]=ht(f[e[0]]);break;case"int":t.WBProps[e[0]]=parseInt(f[e[0]],10);break;default:t.WBProps[e[0]]=f[e[0]];}});if(f.codeName)t.WBProps.CodeName=gt(f.codeName);break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":;case"<bookViews>":;case"</bookViews>":break;case"<workbookView":;case"<workbookView/>":delete f[0];t.WBView.push(f);break;case"</workbookView>":break;case"<sheets":;case"<sheets>":;case"</sheets>":break;case"<sheet":switch(f.state){case"hidden":f.Hidden=1;break;case"veryHidden":f.Hidden=2;break;default:f.Hidden=0;}delete f.state;f.name=tt(gt(f.name));delete f[0];t.Sheets.push(f);break;case"</sheet>":break;case"<functionGroups":;case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":;case"</externalReferences>":;case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":;case"<definedNames":a=true;break;case"</definedNames>":a=false;break;case"<definedName":{i={};i.Name=gt(f.name);if(f.comment)i.Comment=f.comment;if(f.localSheetId)i.Sheet=+f.localSheetId;if(ht(f.hidden||"0"))i.Hidden=true;s=c+o.length}break;case"</definedName>":{i.Ref=tt(gt(e.slice(s,c)));t.Names.push(i)}break;case"<definedName/>":break;case"<calcPr":delete f[0];t.CalcPr=f;break;case"<calcPr/>":delete f[0];t.CalcPr=f;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":;case"</customWorkbookViews>":;case"<customWorkbookViews":break;case"<customWorkbookView":;case"</customWorkbookView>":break;case"<pivotCaches>":;case"</pivotCaches>":;case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":;case"<smartTagPr/>":break;case"<smartTagTypes":;case"<smartTagTypes>":;case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":;case"<webPublishing/>":break;case"<fileRecoveryPr":;case"<fileRecoveryPr/>":break;case"<webPublishObjects>":;case"<webPublishObjects":;case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":;case"<extLst>":;case"</extLst>":;case"<extLst/>":break;case"<ext":a=true;break;case"</ext>":a=false;break;case"<ArchID":break;case"<AlternateContent":;case"<AlternateContent>":a=true;break;case"</AlternateContent>":a=false;break;case"<revisionPtr":break;default:if(!a&&r.WTF)throw new Error("unrecognized "+f[0]+" in workbook");}return o});if(Pt.indexOf(t.xmlns)===-1)throw new Error("Unknown Namespace: "+t.xmlns);Nl(t);return t}function Wl(e){var r=[Vr];r[r.length]=Ft("workbook",null,{xmlns:Pt[0],"xmlns:r":Nt.r});var t=e.Workbook&&(e.Workbook.Names||[]).length>0;var a={codeName:"ThisWorkbook"};if(e.Workbook&&e.Workbook.WBProps){El.forEach(function(r){if(e.Workbook.WBProps[r[0]]==null)return;if(e.Workbook.WBProps[r[0]]==r[1])return;a[r[0]]=e.Workbook.WBProps[r[0]]});if(e.Workbook.WBProps.CodeName){a.codeName=e.Workbook.WBProps.CodeName;delete a.CodeName}}r[r.length]=Ft("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[];var i=0;if(n&&n[0]&&!!n[0].Hidden){r[r.length]="<bookViews>";for(i=0;i!=e.SheetNames.length;++i){if(!n[i])break;if(!n[i].Hidden)break}if(i==e.SheetNames.length)i=0;r[r.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>';r[r.length]="</bookViews>"}r[r.length]="<sheets>";for(i=0;i!=e.SheetNames.length;++i){var s={name:it(e.SheetNames[i].slice(0,31))};s.sheetId=""+(i+1);s["r:id"]="rId"+(i+1);if(n[i])switch(n[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break;}r[r.length]=Ft("sheet",null,s)}r[r.length]="</sheets>";if(t){r[r.length]="<definedNames>";if(e.Workbook&&e.Workbook.Names)e.Workbook.Names.forEach(function(e){var t={name:e.Name};if(e.Comment)t.comment=e.Comment;if(e.Sheet!=null)t.localSheetId=""+e.Sheet;if(e.Hidden)t.hidden="1";if(!e.Ref)return;r[r.length]=Ft("definedName",it(e.Ref),t)});r[r.length]="</definedNames>"}if(r.length>2){r[r.length]="</workbook>";r[1]=r[1].replace("/>",">")}return r.join("")}function Ul(e,r,t){if(r.slice(-4)===".bin")return parse_wb_bin(e,t);return $l(e,t)}function jl(e,r,t,a,n,i,s,l){if(r.slice(-4)===".bin")return parse_ws_bin(e,a,t,n,i,s,l);return tl(e,a,t,n,i,s,l)}function Hl(e,r,t,a,n,i,s,l){if(r.slice(-4)===".bin")return parse_cs_bin(e,a,t,n,i,s,l);return Al(e,a,t,n,i,s,l)}function Vl(e,r,t,a,n,i,s,l){if(r.slice(-4)===".bin")return Ss(e,a,t,n,i,s,l);return Cs(e,a,t,n,i,s,l)}function Xl(e,r,t,a,n,i,s,l){if(r.slice(-4)===".bin")return ys(e,a,t,n,i,s,l);return xs(e,a,t,n,i,s,l)}function Gl(e,r,t,a){if(r.slice(-4)===".bin")return parse_sty_bin(e,t,a);return Hi(e,t,a)}function Yl(e,r,t){if(r.slice(-4)===".bin")return parse_sst_bin(e,t);return hi(e,t)}function Jl(e,r,t){if(r.slice(-4)===".bin")return parse_comments_bin(e,t);return us(e,t)}function Kl(e,r,t){if(r.slice(-4)===".bin")return parse_cc_bin(e,r,t);return parse_cc_xml(e,r,t)}function Zl(e,r,t,a){if(t.slice(-4)===".bin")return ss(e,r,t,a);return is(e,r,t,a)}function ql(e,r,t){if(r.slice(-4)===".bin")return parse_xlmeta_bin(e,r,t);return as(e,r,t)}function Ql(e,r){var t=r||{};var a=t.dense!=null?t.dense:b;var n={};if(a)n["!data"]=[];e=e.replace(/<!--.*?-->/g,"");var i=e.match(/<table/i);if(!i)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i);var l=i.index,o=s&&s.index||e.length;var c=Or(e.slice(l,o),/(:?<tr[^>]*>)/i,"<tr>");var f=-1,u=0,h=0,d=0;var m={s:{r:1e7,c:1e7},e:{r:0,c:0}};var p=[];for(l=0;l<c.length;++l){var v=c[l].trim();var g=v.slice(0,3).toLowerCase();if(g=="<tr"){++f;if(t.sheetRows&&t.sheetRows<=f){--f;break}u=0;continue}if(g!="<td"&&g!="<th")continue;var w=v.split(/<\/t[dh]>/i);for(o=0;o<w.length;++o){var k=w[o].trim();if(!k.match(/<t[dh]/i))continue;var y=k,x=0;while(y.charAt(0)=="<"&&(x=y.indexOf(">"))>-1)y=y.slice(x+1);for(var S=0;S<p.length;++S){var C=p[S];if(C.s.c==u&&C.s.r<f&&f<=C.e.r){u=C.e.c+1;S=-1}}var _=qr(k.slice(0,k.indexOf(">")));d=_.colspan?+_.colspan:1;if((h=+_.rowspan)>1||d>1)p.push({s:{r:f,c:u},e:{r:f+(h||1)-1,c:u+d-1}});var A=_.t||_["data-t"]||"";if(!y.length){u+=d;continue}y=kt(y);if(m.s.r>f)m.s.r=f;if(m.e.r<f)m.e.r=f;if(m.s.c>u)m.s.c=u;if(m.e.c<u)m.e.c=u;if(!y.length){u+=d;continue}var E={t:"s",v:y};if(t.raw||!y.trim().length||A=="s"){}else if(y==="TRUE")E={t:"b",v:true};else if(y==="FALSE")E={t:"b",v:false};else if(!isNaN(Ar(y)))E={t:"n",v:Ar(y)};else if(!isNaN(Dr(y).getDate())){E={t:"d",v:xr(y)};if(!t.cellDates)E={t:"n",v:dr(E.v)};E.z=t.dateNF||Z[14]}if(a){if(!n["!data"][f])n["!data"][f]=[];n["!data"][f][u]=E}else n[Ba({r:f,c:u})]=E;u+=d}}n["!ref"]=$a(m);if(p.length)n["!merges"]=p;return n}function eo(e,r,t,a){var n=e["!merges"]||[];var i=[];var s={};var l=e["!data"]!=null;for(var o=r.s.c;o<=r.e.c;++o){var c=0,f=0;for(var u=0;u<n.length;++u){if(n[u].s.r>t||n[u].s.c>o)continue;if(n[u].e.r<t||n[u].e.c<o)continue;if(n[u].s.r<t||n[u].s.c<o){c=-1;break}c=n[u].e.r-n[u].s.r+1;f=n[u].e.c-n[u].s.c+1;break}if(c<0)continue;var h=Na(o)+Ta(t);var d=l?(e["!data"][t]||[])[o]:e[h];var m=d&&d.v!=null&&(d.h||ot(d.w||(Va(d),d.w)||""))||"";s={};if(c>1)s.rowspan=c;if(f>1)s.colspan=f;if(a.editable)m='<span contenteditable="true">'+m+"</span>";else if(d){s["data-t"]=d&&d.t||"z";if(d.v!=null)s["data-v"]=d.v;if(d.z!=null)s["data-z"]=d.z;if(d.l&&(d.l.Target||"#").charAt(0)!="#")m='<a href="'+ot(d.l.Target)+'">'+m+"</a>"}s.id=(a.id||"sjs")+"-"+h;i.push(Ft("td",m,s))}var p="<tr>";return p+i.join("")+"</tr>"}var ro='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>';var to="</body></html>";function ao(e,r){var t=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!t||t.length==0)throw new Error("Invalid HTML: could not find <table>");if(t.length==1){var a=Xa(Ql(t[0],r),r);a.bookType="html";return a}var n=pc();t.forEach(function(e,t){vc(n,Ql(e,r),"Sheet"+(t+1))});n.bookType="html";return n}function no(e,r,t){var a=[];return a.join("")+"<table"+(t&&t.id?' id="'+t.id+'"':"")+">"}function io(e,r){var t=r||{};var a=t.header!=null?t.header:ro;var n=t.footer!=null?t.footer:to;var i=[a];var s=za(e["!ref"]);i.push(no(e,s,t));for(var l=s.s.r;l<=s.e.r;++l)i.push(eo(e,s,l,t));i.push("</table>"+n);return i.join("")}function so(e,r,t){var a=r.rows;if(!a){throw"Unsupported origin when "+r.tagName+" is not a TABLE"}var n=t||{};var i=e["!data"]!=null;var s=0,l=0;if(n.origin!=null){if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?La(n.origin):n.origin;s=o.r;l=o.c}}var c=Math.min(n.sheetRows||1e7,a.length);var f={s:{r:0,c:0},e:{r:s,c:l}};if(e["!ref"]){var u=za(e["!ref"]);f.s.r=Math.min(f.s.r,u.s.r);f.s.c=Math.min(f.s.c,u.s.c);f.e.r=Math.max(f.e.r,u.e.r);f.e.c=Math.max(f.e.c,u.e.c);if(s==-1)f.e.r=s=u.e.r+1}var h=[],d=0;var m=e["!rows"]||(e["!rows"]=[]);var p=0,v=0,g=0,b=0,w=0,k=0;if(!e["!cols"])e["!cols"]=[];for(;p<a.length&&v<c;++p){var y=a[p];if(co(y)){if(n.display)continue;m[v]={hidden:true}}var x=y.cells;for(g=b=0;g<x.length;++g){var S=x[g];if(n.display&&co(S))continue;var C=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):kt(S.innerHTML);var _=S.getAttribute("data-z")||S.getAttribute("z");for(d=0;d<h.length;++d){var A=h[d];if(A.s.c==b+l&&A.s.r<v+s&&v+s<=A.e.r){b=A.e.c+1-l;d=-1}}k=+S.getAttribute("colspan")||1;if((w=+S.getAttribute("rowspan")||1)>1||k>1)h.push({s:{r:v+s,c:b+l},e:{r:v+s+(w||1)-1,c:b+l+(k||1)-1}});var E={t:"s",v:C};var F=S.getAttribute("data-t")||S.getAttribute("t")||"";if(C!=null){if(C.length==0)E.t=F||"z";else if(n.raw||C.trim().length==0||F=="s"){}else if(C==="TRUE")E={t:"b",v:true};else if(C==="FALSE")E={t:"b",v:false};else if(!isNaN(Ar(C)))E={t:"n",v:Ar(C)};else if(!isNaN(Dr(C).getDate())){E={t:"d",v:xr(C)};if(!n.cellDates)E={t:"n",v:dr(E.v)};E.z=n.dateNF||Z[14]}}if(E.z===undefined&&_!=null)E.z=_;var T="",D=S.getElementsByTagName("A");if(D&&D.length)for(var O=0;O<D.length;++O)if(D[O].hasAttribute("href")){T=D[O].getAttribute("href");if(T.charAt(0)!="#")break}if(T&&T.charAt(0)!="#"&&T.slice(0,11).toLowerCase()!="javascript:")E.l={Target:T};if(i){if(!e["!data"][v+s])e["!data"][v+s]=[];e["!data"][v+s][b+l]=E}else e[Ba({c:b+l,r:v+s})]=E;if(f.e.c<b+l)f.e.c=b+l;b+=k}++v}if(h.length)e["!merges"]=(e["!merges"]||[]).concat(h);f.e.r=Math.max(f.e.r,v-1+s);e["!ref"]=$a(f);if(v>=c)e["!fullref"]=$a((f.e.r=a.length-p+v-1+s,f));return e}function lo(e,r){var t=r||{};var a={};if(t.dense)a["!data"]=[];return so(a,e,r)}function oo(e,r){var t=Xa(lo(e,r),r);return t}function co(e){var r="";var t=fo(e);if(t)r=t(e).getPropertyValue("display");if(!r)r=e.style&&e.style.display;return r==="none"}function fo(e){if(e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle==="function")return e.ownerDocument.defaultView.getComputedStyle;if(typeof getComputedStyle==="function")return getComputedStyle;return null}function uo(e){var r=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,r){return Array(parseInt(r,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n");var t=tt(r.replace(/<[^>]*>/g,""));return[t]}function ho(e,r,t){var a=t||{};var n=Ot(e);Mt.lastIndex=0;n=n.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var i,s,l="",o="",c,f=0,u=-1,h=false,d="";while(i=Mt.exec(n)){switch(i[3]=i[3].replace(/_.*$/,"")){case"number-style":;case"currency-style":;case"percentage-style":;case"date-style":;case"time-style":;case"text-style":if(i[1]==="/"){h=false;if(s["truncate-on-overflow"]=="false"){if(l.match(/h/))l=l.replace(/h+/,"[$&]");else if(l.match(/m/))l=l.replace(/m+/,"[$&]");else if(l.match(/s/))l=l.replace(/s+/,"[$&]")}a[s.name]=l;l=""}else if(i[0].charAt(i[0].length-2)!=="/"){h=true;l="";s=qr(i[0],false)}break;case"boolean-style":if(i[1]==="/"){h=false;a[s.name]="General";l=""}else if(i[0].charAt(i[0].length-2)!=="/"){h=true;l="";s=qr(i[0],false)}break;case"boolean":l+="General";break;case"text":if(i[1]==="/"){d=n.slice(u,Mt.lastIndex-i[0].length);if(d=="%"&&s[0]=="<number:percentage-style")l+="%";else l+='"'+d.replace(/"/g,'""')+'"'}else if(i[0].charAt(i[0].length-2)!=="/"){u=Mt.lastIndex}break;case"day":{c=qr(i[0],false);switch(c["style"]){case"short":l+="d";break;case"long":l+="dd";break;default:l+="dd";break;}}break;case"day-of-week":{c=qr(i[0],false);switch(c["style"]){case"short":l+="ddd";break;case"long":l+="dddd";break;default:l+="ddd";break;}}break;case"era":{c=qr(i[0],false);switch(c["style"]){case"short":l+="ee";break;case"long":l+="eeee";break;default:l+="eeee";break;}}break;case"hours":{c=qr(i[0],false);switch(c["style"]){case"short":l+="h";break;case"long":l+="hh";break;default:l+="hh";break;}}break;case"minutes":{c=qr(i[0],false);switch(c["style"]){case"short":l+="m";break;case"long":l+="mm";break;default:l+="mm";break;}}break;case"month":{c=qr(i[0],false);if(c["textual"])l+="mm";switch(c["style"]){case"short":l+="m";break;case"long":l+="mm";break;default:l+="m";break;}}break;case"seconds":{c=qr(i[0],false);switch(c["style"]){case"short":l+="s";break;case"long":l+="ss";break;default:l+="ss";break;}if(c["decimal-places"])l+="."+_r("0",+c["decimal-places"])}break;case"year":{c=qr(i[0],false);switch(c["style"]){case"short":l+="yy";break;case"long":l+="yyyy";break;default:l+="yy";break;}}break;case"am-pm":l+="AM/PM";break;case"week-of-year":;case"quarter":console.error("Excel does not support ODS format token "+i[3]);break;case"fill-character":if(i[1]==="/"){d=n.slice(u,Mt.lastIndex-i[0].length);l+='"'+d.replace(/"/g,'""')+'"*'}else if(i[0].charAt(i[0].length-2)!=="/"){u=Mt.lastIndex}break;case"scientific-number":c=qr(i[0],false);l+="0."+_r("0",+c["min-decimal-places"]||+c["decimal-places"]||2)+_r("?",+c["decimal-places"]-+c["min-decimal-places"]||0)+"E"+(ht(c["forced-exponent-sign"])?"+":"")+_r("0",+c["min-exponent-digits"]||2);break;case"fraction":c=qr(i[0],false);if(!+c["min-integer-digits"])l+="#";else l+=_r("0",+c["min-integer-digits"]);l+=" ";l+=_r("?",+c["min-numerator-digits"]||1);l+="/";if(+c["denominator-value"])l+=c["denominator-value"];else l+=_r("?",+c["min-denominator-digits"]||1);break;case"currency-symbol":if(i[1]==="/"){l+='"'+n.slice(u,Mt.lastIndex-i[0].length).replace(/"/g,'""')+'"'}else if(i[0].charAt(i[0].length-2)!=="/"){u=Mt.lastIndex}else l+="$";break;case"text-properties":c=qr(i[0],false);switch((c["color"]||"").toLowerCase().replace("#","")){case"ff0000":;case"red":l="[Red]"+l;break;}break;case"text-content":l+="@";break;case"map":c=qr(i[0],false);if(tt(c["condition"])=="value()>=0")l=a[c["apply-style-name"]]+";"+l;else console.error("ODS number format may be incorrect: "+c["condition"]);break;case"number":if(i[1]==="/")break;c=qr(i[0],false);o="";o+=_r("0",+c["min-integer-digits"]||1);if(ht(c["grouping"]))o=me(_r("#",Math.max(0,4-o.length))+o);if(+c["min-decimal-places"]||+c["decimal-places"])o+=".";if(+c["min-decimal-places"])o+=_r("0",+c["min-decimal-places"]||1);if(+c["decimal-places"]-(+c["min-decimal-places"]||0))o+=_r("0",+c["decimal-places"]-(+c["min-decimal-places"]||0));l+=o;break;case"embedded-text":if(i[1]==="/"){if(f==0)l+='"'+n.slice(u,Mt.lastIndex-i[0].length).replace(/"/g,'""')+'"';else l=l.slice(0,f)+'"'+n.slice(u,Mt.lastIndex-i[0].length).replace(/"/g,'""')+'"'+l.slice(f)}else if(i[0].charAt(i[0].length-2)!=="/"){u=Mt.lastIndex;f=-+qr(i[0],false)["position"]||0}break;}}return a}function mo(e,r,t){var a=r||{};if(b!=null&&a.dense==null)a.dense=b;var n=Ot(e);var i=[],s;var l;var o,c="",f=0;var u;var h;var d={},m=[];var p={};if(a.dense)p["!data"]=[];var v,g;var w={value:""};var k="",y=0,x;var S=[];var C=-1,_=-1,A={s:{r:1e6,c:1e7},e:{r:0,c:0}};var E=0;var F=t||{},T={};var D=[],O={},M=0,N=0;var P=[],I=1,R=1;var L=[];var B={Names:[],WBProps:{}};var z={};var $=["",""];var W=[],U={};var j="",H=0;var V=false,X=false;var G=0;var Y=0;Mt.lastIndex=0;n=n.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");while(v=Mt.exec(n))switch(v[3]=v[3].replace(/_.*$/,"")){case"table":;case"工作表":if(v[1]==="/"){if(A.e.c>=A.s.c&&A.e.r>=A.s.r)p["!ref"]=$a(A);else p["!ref"]="A1:A1";if(a.sheetRows>0&&a.sheetRows<=A.e.r){p["!fullref"]=p["!ref"];A.e.r=a.sheetRows-1;p["!ref"]=$a(A)}if(D.length)p["!merges"]=D;if(P.length)p["!rows"]=P;u.name=u["名称"]||u.name;if(typeof JSON!=="undefined")JSON.stringify(u);m.push(u.name);d[u.name]=p;X=false}else if(v[0].charAt(v[0].length-2)!=="/"){u=qr(v[0],false);C=_=-1;A.s.r=A.s.c=1e7;A.e.r=A.e.c=0;p={};if(a.dense)p["!data"]=[];D=[];P=[];X=true}break;case"table-row-group":if(v[1]==="/")--E;else++E;break;case"table-row":;case"行":if(v[1]==="/"){C+=I;I=1;break}h=qr(v[0],false);if(h["行号"])C=h["行号"]-1;else if(C==-1)C=0;I=+h["number-rows-repeated"]||1;if(I<10)for(G=0;G<I;++G)if(E>0)P[C+G]={level:E};_=-1;break;case"covered-table-cell":if(v[1]!=="/")++_;if(a.sheetStubs){if(a.dense){if(!p["!data"][C])p["!data"][C]=[];p["!data"][C][_]={t:"z"}}else p[Ba({r:C,c:_})]={t:"z"}}k="";S=[];break;case"table-cell":;case"数据":if(v[0].charAt(v[0].length-2)==="/"){++_;w=qr(v[0],false);R=parseInt(w["number-columns-repeated"]||"1",10);g={t:"z",v:null};if(w.formula&&a.cellFormula!=false)g.f=Ms(tt(w.formula));if(w["style-name"]&&T[w["style-name"]])g.z=T[w["style-name"]];if((w["数据类型"]||w["value-type"])=="string"){g.t="s";g.v=tt(w["string-value"]||"");if(a.dense){if(!p["!data"][C])p["!data"][C]=[];p["!data"][C][_]=g}else{p[Na(_)+Ta(C)]=g}}_+=R-1}else if(v[1]!=="/"){++_;k="";y=0;S=[];R=1;var J=I?C+I-1:C;if(_>A.e.c)A.e.c=_;if(_<A.s.c)A.s.c=_;if(C<A.s.r)A.s.r=C;if(J>A.e.r)A.e.r=J;w=qr(v[0],false);W=[];U={};g={t:w["数据类型"]||w["value-type"],v:null};if(w["style-name"]&&T[w["style-name"]])g.z=T[w["style-name"]];if(a.cellFormula){if(w.formula)w.formula=tt(w.formula);if(w["number-matrix-columns-spanned"]&&w["number-matrix-rows-spanned"]){M=parseInt(w["number-matrix-rows-spanned"],10)||0;N=parseInt(w["number-matrix-columns-spanned"],10)||0;O={s:{r:C,c:_},e:{r:C+M-1,c:_+N-1}};g.F=$a(O);L.push([O,g.F])}if(w.formula)g.f=Ms(w.formula);else for(G=0;G<L.length;++G)if(C>=L[G][0].s.r&&C<=L[G][0].e.r)if(_>=L[G][0].s.c&&_<=L[G][0].e.c)g.F=L[G][1]}if(w["number-columns-spanned"]||w["number-rows-spanned"]){M=parseInt(w["number-rows-spanned"],10)||0;N=parseInt(w["number-columns-spanned"],10)||0;O={s:{r:C,c:_},e:{r:C+M-1,c:_+N-1}};D.push(O)}if(w["number-columns-repeated"])R=parseInt(w["number-columns-repeated"],10);switch(g.t){case"boolean":g.t="b";g.v=ht(w["boolean-value"])||+w["boolean-value"]>=1;break;case"float":g.t="n";g.v=parseFloat(w.value);break;case"percentage":g.t="n";g.v=parseFloat(w.value);break;case"currency":g.t="n";g.v=parseFloat(w.value);break;case"date":g.t="d";g.v=xr(w["date-value"]);if(!a.cellDates){g.t="n";g.v=dr(g.v,B.WBProps.date1904)-Y}if(!g.z)g.z="m/d/yy";break;case"time":g.t="n";g.v=br(w["time-value"])/86400;if(a.cellDates){g.t="d";g.v=gr(g.v)}if(!g.z)g.z="HH:MM:SS";break;case"number":g.t="n";g.v=parseFloat(w["数据数值"]);break;default:if(g.t==="string"||g.t==="text"||!g.t){g.t="s";if(w["string-value"]!=null){k=tt(w["string-value"]);S=[]}}else throw new Error("Unsupported value type "+g.t);}}else{V=false;if(g.t==="s"){g.v=k||"";if(S.length)g.R=S;V=y==0}if(z.Target)g.l=z;if(W.length>0){g.c=W;W=[]}if(k&&a.cellText!==false)g.w=k;if(V){g.t="z";delete g.v}if(!V||a.sheetStubs){if(!(a.sheetRows&&a.sheetRows<=C)){for(var K=0;K<I;++K){R=parseInt(w["number-columns-repeated"]||"1",10);if(a.dense){if(!p["!data"][C+K])p["!data"][C+K]=[];p["!data"][C+K][_]=K==0?g:Cr(g);while(--R>0)p["!data"][C+K][_+R]=Cr(g)}else{p[Ba({r:C+K,c:_})]=g;while(--R>0)p[Ba({r:C+K,c:_+R})]=Cr(g)}if(A.e.c<=_)A.e.c=_}}}R=parseInt(w["number-columns-repeated"]||"1",10);_+=R-1;R=0;g={};k="";S=[]}z={};break;case"document":;case"document-content":;case"电子表格文档":;case"spreadsheet":;case"主体":;case"scripts":;case"styles":;case"font-face-decls":;case"master-styles":if(v[1]==="/"){if((s=i.pop())[0]!==v[3])throw"Bad state: "+s}else if(v[0].charAt(v[0].length-2)!=="/")i.push([v[3],true]);break;case"annotation":if(v[1]==="/"){if((s=i.pop())[0]!==v[3])throw"Bad state: "+s;U.t=k;if(S.length)U.R=S;U.a=j;W.push(U)}else if(v[0].charAt(v[0].length-2)!=="/"){i.push([v[3],false])}j="";H=0;k="";y=0;S=[];break;case"creator":if(v[1]==="/"){j=n.slice(H,v.index)}else H=v.index+v[0].length;break;case"meta":;case"元数据":;case"settings":;case"config-item-set":;case"config-item-map-indexed":;case"config-item-map-entry":;case"config-item-map-named":;case"shapes":;case"frame":;case"text-box":;case"image":;case"data-pilot-tables":;case"list-style":;case"form":;case"dde-links":;case"event-listeners":;case"chart":if(v[1]==="/"){if((s=i.pop())[0]!==v[3])throw"Bad state: "+s}else if(v[0].charAt(v[0].length-2)!=="/")i.push([v[3],false]);k="";y=0;S=[];break;case"scientific-number":;case"currency-symbol":;case"fill-character":break;case"text-style":;case"boolean-style":;case"number-style":;case"currency-style":;case"percentage-style":;case"date-style":;case"time-style":if(v[1]==="/"){var Z=Mt.lastIndex;ho(n.slice(o,Mt.lastIndex),r,F);Mt.lastIndex=Z}else if(v[0].charAt(v[0].length-2)!=="/"){o=Mt.lastIndex-v[0].length}break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":;case"page-layout":break;case"style":{var q=qr(v[0],false);if(q["family"]=="table-cell"&&F[q["data-style-name"]])T[q["name"]]=F[q["data-style-name"]]}break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":break;case"fraction":break;case"day":;case"month":;case"year":;case"era":;case"day-of-week":;case"week-of-year":;case"quarter":;case"hours":;case"minutes":;case"seconds":;case"am-pm":break;case"boolean":break;case"text":if(v[0].slice(-2)==="/>")break;else if(v[1]==="/")switch(i[i.length-1][0]){case"number-style":;case"date-style":;case"time-style":c+=n.slice(f,v.index);break;}else f=v.index+v[0].length;break;case"named-range":l=qr(v[0],false);$=Ps(l["cell-range-address"]);var Q={Name:l.name,Ref:$[0]+"!"+$[1]};if(X)Q.Sheet=m.length;B.Names.push(Q);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":;case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":l=qr(v[0],false);switch(l["date-value"]){case"1904-01-01":B.WBProps.date1904=true;case"1900-01-01":Y=0;}break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":;case"文本串":if(["master-styles"].indexOf(i[i.length-1][0])>-1)break;if(v[1]==="/"&&(!w||!w["string-value"])){var ee=uo(n.slice(y,v.index),x);k=(k.length>0?k+"\n":"")+ee[0]}else{x=qr(v[0],false);y=v.index+v[0].length}break;case"s":break;case"database-range":if(v[1]==="/")break;try{$=Ps(qr(v[0])["target-range-address"]);d[$[0]]["!autofilter"]={ref:$[1]}}catch(re){}break;case"date":break;case"object":break;case"title":;case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":;case"sender-lastname":;case"sender-initials":;case"sender-title":;case"sender-position":;case"sender-email":;case"sender-phone-private":;case"sender-fax":;case"sender-company":;case"sender-phone-work":;case"sender-street":;case"sender-city":;case"sender-postal-code":;case"sender-country":;case"sender-state-or-province":;case"author-name":;case"author-initials":;case"chapter":;case"file-name":;case"template-name":;case"sheet-name":break;case"event-listener":break;case"initial-creator":;case"creation-date":;case"print-date":;case"generator":;case"document-statistic":;case"user-defined":;case"editing-duration":;case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":;case"source-cell-range":;case"source-service":;case"data-pilot-field":;case"data-pilot-level":;case"data-pilot-subtotals":;case"data-pilot-subtotal":;case"data-pilot-members":;case"data-pilot-member":;case"data-pilot-display-info":;case"data-pilot-sort-info":;case"data-pilot-layout-info":;case"data-pilot-field-reference":;case"data-pilot-groups":;case"data-pilot-group":;case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":;case"dde-connection-decl":;case"dde-link":;case"dde-source":break;case"properties":break;case"property":break;case"a":if(v[1]!=="/"){z=qr(v[0],false);if(!z.href)break;z.Target=tt(z.href);delete z.href;if(z.Target.charAt(0)=="#"&&z.Target.indexOf(".")>-1){$=Ps(z.Target.slice(1));z.Target="#"+$[0]+"!"+$[1]}else if(z.Target.match(/^\.\.[\\\/]/))z.Target=z.Target.slice(3)}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(v[2]){case"dc:":;case"calcext:":;case"loext:":;case"ooo:":;case"chartooo:":;case"draw:":;case"style:":;case"chart:":;case"form:":;case"uof:":;case"表:":;case"字:":break;default:if(a.WTF)throw new Error(v);};}var te={Sheets:d,SheetNames:m,Workbook:B};if(a.bookSheets)delete te.Sheets;return te;
}function po(e,r){r=r||{};if(Ir(e,"META-INF/manifest.xml"))Dn(Lr(e,"META-INF/manifest.xml"),r);var t=Br(e,"styles.xml");var a=t&&ho(gt(t),r);var n=Br(e,"content.xml");if(!n)throw new Error("Missing content.xml in ODS / UOF file");var i=mo(gt(n),r,a);if(Ir(e,"meta.xml"))i.Props=Bn(Lr(e,"meta.xml"));i.bookType="ods";return i}function vo(e,r){var t=mo(e,r);t.bookType="fods";return t}var go=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join("");var r="<office:document-styles "+Et({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function t(){return Vr+r}}();function bo(e,r){var t="number",a="",n={"style:name":r},i="",s=0;e=e.replace(/"[$]"/g,"$");e:{if(e.indexOf(";")>-1){console.error("Unsupported ODS Style Map exported.  Using first branch of "+e);e=e.slice(0,e.indexOf(";"))}if(e=="@"){t="text";a="<number:text-content/>";break e}if(e.indexOf(/\$/)>-1){t="currency"}if(e[s]=='"'){i="";while(e[++s]!='"'||e[++s]=='"')i+=e[s];--s;if(e[s+1]=="*"){s++;a+="<number:fill-character>"+it(i.replace(/""/g,'"'))+"</number:fill-character>"}else{a+="<number:text>"+it(i.replace(/""/g,'"'))+"</number:text>"}e=e.slice(s+1);s=0}var l=e.match(/# (\?+)\/(\?+)/);if(l){a+=Ft("number:fraction",null,{"number:min-integer-digits":0,"number:min-numerator-digits":l[1].length,"number:max-denominator-value":Math.max(+l[1].replace(/./g,"9"),+l[2].replace(/./g,"9"))});break e}if(l=e.match(/# (\?+)\/(\d+)/)){a+=Ft("number:fraction",null,{"number:min-integer-digits":0,"number:min-numerator-digits":l[1].length,"number:denominator-value":+l[2]});break e}if(l=e.match(/(\d+)(|\.\d+)%/)){t="percentage";a+=Ft("number:number",null,{"number:decimal-places":l[2]&&l.length-1||0,"number:min-decimal-places":l[2]&&l.length-1||0,"number:min-integer-digits":l[1].length})+"<number:text>%</number:text>";break e}var o=false;if(["y","m","d"].indexOf(e[0])>-1){t="date";r:for(;s<e.length;++s)switch(i=e[s].toLowerCase()){case"h":;case"s":o=true;--s;break r;case"m":t:for(var c=s+1;c<e.length;++c)switch(e[c]){case"y":;case"d":break t;case"h":;case"s":o=true;--s;break r;};case"y":;case"d":while((e[++s]||"").toLowerCase()==i[0])i+=i[0];--s;switch(i){case"y":;case"yy":a+="<number:year/>";break;case"yyy":;case"yyyy":a+='<number:year number:style="long"/>';break;case"mmmmm":console.error("ODS has no equivalent of format |mmmmm|");case"m":;case"mm":;case"mmm":;case"mmmm":a+='<number:month number:style="'+(i.length%2?"short":"long")+'" number:textual="'+(i.length>=3?"true":"false")+'"/>';break;case"d":;case"dd":a+='<number:day number:style="'+(i.length%2?"short":"long")+'"/>';break;case"ddd":;case"dddd":a+='<number:day-of-week number:style="'+(i.length%2?"short":"long")+'"/>';break;}break;case'"':while(e[++s]!='"'||e[++s]=='"')i+=e[s];--s;a+="<number:text>"+it(i.slice(1).replace(/""/g,'"'))+"</number:text>";break;case"/":a+="<number:text>"+it(i)+"</number:text>";break;default:console.error("unrecognized character "+i+" in ODF format "+e);}if(!o)break e;e=e.slice(s+1);s=0}if(e.match(/^\[?[hms]/)){if(t=="number")t="time";if(e.match(/\[/)){e=e.replace(/[\[\]]/g,"");n["number:truncate-on-overflow"]="false"}for(;s<e.length;++s)switch(i=e[s].toLowerCase()){case"h":;case"m":;case"s":while((e[++s]||"").toLowerCase()==i[0])i+=i[0];--s;switch(i){case"h":;case"hh":a+='<number:hours number:style="'+(i.length%2?"short":"long")+'"/>';break;case"m":;case"mm":a+='<number:minutes number:style="'+(i.length%2?"short":"long")+'"/>';break;case"s":;case"ss":if(e[s+1]==".")do{i+=e[s+1];++s}while(e[s+1]=="0");a+='<number:seconds number:style="'+(i.match("ss")?"long":"short")+'"'+(i.match(/\./)?' number:decimal-places="'+(i.match(/0+/)||[""])[0].length+'"':"")+"/>";break;}break;case'"':while(e[++s]!='"'||e[++s]=='"')i+=e[s];--s;a+="<number:text>"+it(i.slice(1).replace(/""/g,'"'))+"</number:text>";break;case"/":a+="<number:text>"+it(i)+"</number:text>";break;case"a":if(e.slice(s,s+3).toLowerCase()=="a/p"){a+="<number:am-pm/>";s+=2;break}if(e.slice(s,s+5).toLowerCase()=="am/pm"){a+="<number:am-pm/>";s+=4;break};default:console.error("unrecognized character "+i+" in ODF format "+e);}break e}if(e.indexOf(/\$/)>-1){t="currency"}if(e[0]=="$"){a+='<number:currency-symbol number:language="en" number:country="US">$</number:currency-symbol>';e=e.slice(1);s=0}s=0;if(e[s]=='"'){while(e[++s]!='"'||e[++s]=='"')i+=e[s];--s;if(e[s+1]=="*"){s++;a+="<number:fill-character>"+it(i.replace(/""/g,'"'))+"</number:fill-character>"}else{a+="<number:text>"+it(i.replace(/""/g,'"'))+"</number:text>"}e=e.slice(s+1);s=0}var f=e.match(/([#0][0#,]*)(\.[0#]*|)(E[+]?0*|)/i);if(!f||!f[0])console.error("Could not find numeric part of "+e);else{var u=f[1].replace(/,/g,"");a+="<number:"+(f[3]?"scientific-":"")+"number"+' number:min-integer-digits="'+(u.indexOf("0")==-1?"0":u.length-u.indexOf("0"))+'"'+(f[0].indexOf(",")>-1?' number:grouping="true"':"")+(f[2]&&' number:decimal-places="'+(f[2].length-1)+'"'||' number:decimal-places="0"')+(f[3]&&f[3].indexOf("+")>-1?' number:forced-exponent-sign="true"':"")+(f[3]?' number:min-exponent-digits="'+f[3].match(/0+/)[0].length+'"':"")+">"+"</number:"+(f[3]?"scientific-":"")+"number>";s=f.index+f[0].length}if(e[s]=='"'){i="";while(e[++s]!='"'||e[++s]=='"')i+=e[s];--s;a+="<number:text>"+it(i.replace(/""/g,'"'))+"</number:text>"}}if(!a){console.error("Could not generate ODS number format for |"+e+"|");return""}return Ft("number:"+t+"-style",a,n)}function wo(e,r,t){var a=e.filter(function(e){return e.Sheet==(t==-1?null:t)});if(!a.length)return"";return"      <table:named-expressions>\n"+a.map(function(e){var r=Is(e.Ref);return"        "+Ft("table:named-range",null,{"table:name":e.Name,"table:cell-range-address":r,"table:base-cell-address":r.replace(/[\.]?[^\.]*$/,".$A$1")})}).join("\n")+"\n      </table:named-expressions>\n"}var ko=function(){var e=function(e){return it(e).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")};var r="          <table:table-cell />\n";var t="          <table:covered-table-cell/>\n";var a=function(a,n,i,s,l){var o=[];o.push('      <table:table table:name="'+it(n.SheetNames[i])+'" table:style-name="ta1">\n');var c=0,f=0,u=za(a["!ref"]||"A1");var h=a["!merges"]||[],d=0;var m=a["!data"]!=null;if(a["!cols"]){for(f=0;f<=u.e.c;++f)o.push("        <table:table-column"+(a["!cols"][f]?' table:style-name="co'+a["!cols"][f].ods+'"':"")+"></table:table-column>\n")}var p="",v=a["!rows"]||[];for(c=0;c<u.s.r;++c){p=v[c]?' table:style-name="ro'+v[c].ods+'"':"";o.push("        <table:table-row"+p+"></table:table-row>\n")}for(;c<=u.e.r;++c){p=v[c]?' table:style-name="ro'+v[c].ods+'"':"";o.push("        <table:table-row"+p+">\n");for(f=0;f<u.s.c;++f)o.push(r);for(;f<=u.e.c;++f){var g=false,b={},w="";for(d=0;d!=h.length;++d){if(h[d].s.c>f)continue;if(h[d].s.r>c)continue;if(h[d].e.c<f)continue;if(h[d].e.r<c)continue;if(h[d].s.c!=f||h[d].s.r!=c)g=true;b["table:number-columns-spanned"]=h[d].e.c-h[d].s.c+1;b["table:number-rows-spanned"]=h[d].e.r-h[d].s.r+1;break}if(g){o.push(t);continue}var k=Ba({r:c,c:f}),y=m?(a["!data"][c]||[])[f]:a[k];if(y&&y.f){b["table:formula"]=it(Ns(y.f));if(y.F){if(y.F.slice(0,k.length)==k){var x=za(y.F);b["table:number-matrix-columns-spanned"]=x.e.c-x.s.c+1;b["table:number-matrix-rows-spanned"]=x.e.r-x.s.r+1}}}if(!y){o.push(r);continue}switch(y.t){case"b":w=y.v?"TRUE":"FALSE";b["office:value-type"]="boolean";b["office:boolean-value"]=y.v?"true":"false";break;case"n":w=y.w||String(y.v||0);b["office:value-type"]="float";b["office:value"]=y.v||0;break;case"s":;case"str":w=y.v==null?"":y.v;b["office:value-type"]="string";break;case"d":w=y.w||xr(y.v).toISOString();b["office:value-type"]="date";b["office:date-value"]=xr(y.v).toISOString();b["table:style-name"]="ce1";break;default:o.push(r);continue;}var S=e(w);if(y.l&&y.l.Target){var C=y.l.Target;C=C.charAt(0)=="#"?"#"+Is(C.slice(1)):C;if(C.charAt(0)!="#"&&!C.match(/^\w+:/))C="../"+C;S=Ft("text:a",S,{"xlink:href":C.replace(/&/g,"&amp;")})}if(l[y.z])b["table:style-name"]="ce"+l[y.z].slice(1);o.push("          "+Ft("table:table-cell",Ft("text:p",S,{}),b)+"\n")}o.push("        </table:table-row>\n")}if((n.Workbook||{}).Names)o.push(wo(n.Workbook.Names,n.SheetNames,i));o.push("      </table:table>\n");return o.join("")};var n=function(e,r){e.push(" <office:automatic-styles>\n");var t=0;r.SheetNames.map(function(e){return r.Sheets[e]}).forEach(function(r){if(!r)return;if(r["!cols"]){for(var a=0;a<r["!cols"].length;++a)if(r["!cols"][a]){var n=r["!cols"][a];if(n.width==null&&n.wpx==null&&n.wch==null)continue;Ti(n);n.ods=t;var i=r["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+t+'" style:family="table-column">\n');e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+i+'"/>\n');e.push("  </style:style>\n");++t}}});var a=0;r.SheetNames.map(function(e){return r.Sheets[e]}).forEach(function(r){if(!r)return;if(r["!rows"]){for(var t=0;t<r["!rows"].length;++t)if(r["!rows"][t]){r["!rows"][t].ods=a;var n=r["!rows"][t].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n');e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n');e.push("  </style:style>\n");++a}}});e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n');e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n');e.push("  </style:style>\n");e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n');e.push('   <number:month number:style="long"/>\n');e.push("   <number:text>/</number:text>\n");e.push('   <number:day number:style="long"/>\n');e.push("   <number:text>/</number:text>\n");e.push("   <number:year/>\n");e.push("  </number:date-style>\n");var n={};var i=69;r.SheetNames.map(function(e){return r.Sheets[e]}).forEach(function(r){if(!r)return;var t=r["!data"]!=null;var a=za(r["!ref"]);for(var s=0;s<=a.e.r;++s)for(var l=0;l<=a.e.c;++l){var o=t?(r["!data"][s]||[])[l]:r[Ba({r:s,c:l})];if(!o||!o.z||o.z.toLowerCase()=="general")continue;if(!n[o.z]){var c=bo(o.z,"N"+i);if(c){n[o.z]="N"+i;++i;e.push(c+"\n")}}}});e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n');lr(n).forEach(function(r){e.push('<style:style style:name="ce'+n[r].slice(1)+'" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="'+n[r]+'"/>\n')});e.push(" </office:automatic-styles>\n");return n};return function i(e,r){var t=[Vr];var i=Et({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"});var s=Et({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});if(r.bookType=="fods"){t.push("<office:document"+i+s+">\n");t.push(In().replace(/<office:document-meta.*?>/,"").replace(/<\/office:document-meta>/,"")+"\n")}else t.push("<office:document-content"+i+">\n");var l=n(t,e);t.push("  <office:body>\n");t.push("    <office:spreadsheet>\n");if(((e.Workbook||{}).WBProps||{}).date1904)t.push('      <table:calculation-settings table:case-sensitive="false" table:search-criteria-must-apply-to-whole-cell="true" table:use-wildcards="true" table:use-regular-expressions="false" table:automatic-find-labels="false">\n        <table:null-date table:date-value="1904-01-01"/>\n      </table:calculation-settings>\n');for(var o=0;o!=e.SheetNames.length;++o)t.push(a(e.Sheets[e.SheetNames[o]],e,o,r,l));if((e.Workbook||{}).Names)t.push(wo(e.Workbook.Names,e.SheetNames,-1));t.push("    </office:spreadsheet>\n");t.push("  </office:body>\n");if(r.bookType=="fods")t.push("</office:document>");else t.push("</office:document-content>");return t.join("")}}();function yo(e,r){if(r.bookType=="fods")return ko(e,r);var t=Ur();var a="";var n=[];var i=[];a="mimetype";Wr(t,a,"application/vnd.oasis.opendocument.spreadsheet");a="content.xml";Wr(t,a,ko(e,r));n.push([a,"text/xml"]);i.push([a,"ContentFile"]);a="styles.xml";Wr(t,a,go(e,r));n.push([a,"text/xml"]);i.push([a,"StylesFile"]);a="meta.xml";Wr(t,a,Vr+In());n.push([a,"text/xml"]);i.push([a,"MetadataFile"]);a="manifest.rdf";Wr(t,a,Pn(i));n.push([a,"application/rdf+xml"]);a="META-INF/manifest.xml";Wr(t,a,On(n));return t}function xo(e){return function r(t){for(var a=0;a!=e.length;++a){var n=e[a];if(t[n[0]]===undefined)t[n[0]]=n[1];if(n[2]==="n")t[n[0]]=Number(t[n[0]])}}}function So(e){xo([["cellNF",false],["cellHTML",true],["cellFormula",true],["cellStyles",false],["cellText",true],["cellDates",false],["sheetStubs",false],["sheetRows",0,"n"],["bookDeps",false],["bookSheets",false],["bookProps",false],["bookFiles",false],["bookVBA",false],["password",""],["WTF",false]])(e)}function Co(e){xo([["cellDates",false],["bookSST",false],["bookType","xlsx"],["compression",false],["WTF",false]])(e)}function _o(e){if(Cn.WS.indexOf(e)>-1)return"sheet";if(Cn.CS&&e==Cn.CS)return"chart";if(Cn.DS&&e==Cn.DS)return"dialog";if(Cn.MS&&e==Cn.MS)return"macro";return e&&e.length?e:"sheet"}function Ao(e,r){if(!e)return 0;try{e=r.map(function a(r){if(!r.id)r.id=r.strRelID;return[r.name,e["!id"][r.id].Target,_o(e["!id"][r.id].Type)]})}catch(t){return null}return!e||e.length===0?null:e}function Eo(e,r,t,a,n,i,s,l,o,c,f,u){try{i[a]=An(Br(e,t,true),r);var h=Lr(e,r);var d;switch(l){case"sheet":d=jl(h,r,n,o,i[a],c,f,u);break;case"chart":d=Hl(h,r,n,o,i[a],c,f,u);if(!d||!d["!drawel"])break;var m=Hr(d["!drawel"].Target,r);var p=_n(m);var v=ls(Br(e,m,true),An(Br(e,p,true),m));var g=Hr(v,m);var b=_n(g);d=_l(Br(e,g,true),g,o,An(Br(e,b,true),g),c,d);break;case"macro":d=Vl(h,r,n,o,i[a],c,f,u);break;case"dialog":d=Xl(h,r,n,o,i[a],c,f,u);break;default:throw new Error("Unrecognized sheet type "+l);}s[a]=d;var w=[],k=[];if(i&&i[a])lr(i[a]).forEach(function(t){var n="";if(i[a][t].Type==Cn.CMNT){n=Hr(i[a][t].Target,r);w=Jl(Lr(e,n,true),n,o);if(!w||!w.length)return;fs(d,w,false)}if(i[a][t].Type==Cn.TCMNT){n=Hr(i[a][t].Target,r);k=k.concat(ds(Lr(e,n,true),o))}});if(k&&k.length)fs(d,k,true,o.people||[])}catch(y){if(o.WTF)throw y}}function Fo(e){return e.charAt(0)=="/"?e.slice(1):e}function To(e,r){Xe();r=r||{};So(r);if(Ir(e,"META-INF/manifest.xml"))return po(e,r);if(Ir(e,"objectdata.xml"))return po(e,r);if(Ir(e,"Index/Document.iwa")){if(typeof Uint8Array=="undefined")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof parse_numbers_iwa!="undefined"){if(e.FileIndex)return parse_numbers_iwa(e,r);var t=rr.utils.cfb_new();$r(e).forEach(function(r){Wr(t,r,zr(e,r))});return parse_numbers_iwa(t,r)}throw new Error("Unsupported NUMBERS file")}if(!Ir(e,"[Content_Types].xml")){if(Ir(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(Ir(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");var a=rr.find(e,"Index.zip");if(a){r=Cr(r);delete r.type;if(typeof a.content=="string")r.type="binary";if(typeof Bun!=="undefined"&&Buffer.isBuffer(a.content))return Wo(new Uint8Array(a.content),r);return Wo(a.content,r)}throw new Error("Unsupported ZIP file")}var n=$r(e);var i=xn(Br(e,"[Content_Types].xml"));var s=false;var l,o;if(i.workbooks.length===0){o="xl/workbook.xml";if(Lr(e,o,true))i.workbooks.push(o)}if(i.workbooks.length===0){o="xl/workbook.bin";if(!Lr(e,o,true))throw new Error("Could not find workbook");i.workbooks.push(o);s=true}if(i.workbooks[0].slice(-3)=="bin")s=true;var c={};var f={};if(!r.bookSheets&&!r.bookProps){Rs=[];if(i.sst)try{Rs=Yl(Lr(e,Fo(i.sst)),i.sst,r)}catch(u){if(r.WTF)throw u}if(r.cellStyles&&i.themes.length)c=rs(Br(e,i.themes[0].replace(/^\//,""),true)||"",r);if(i.style)f=Gl(Lr(e,Fo(i.style)),i.style,c,r)}i.links.map(function(t){try{var a=An(Br(e,_n(Fo(t))),t);return Zl(Lr(e,Fo(t)),a,t,r)}catch(n){}});var h=Ul(Lr(e,Fo(i.workbooks[0])),i.workbooks[0],r);var d={},m="";if(i.coreprops.length){m=Lr(e,Fo(i.coreprops[0]),true);if(m)d=Bn(m);if(i.extprops.length!==0){m=Lr(e,Fo(i.extprops[0]),true);if(m)Hn(m,d,r)}}var p={};if(!r.bookSheets||r.bookProps){if(i.custprops.length!==0){m=Br(e,Fo(i.custprops[0]),true);if(m)p=Gn(m,r)}}var v={};if(r.bookSheets||r.bookProps){if(h.Sheets)l=h.Sheets.map(function M(e){return e.name});else if(d.Worksheets&&d.SheetNames.length>0)l=d.SheetNames;if(r.bookProps){v.Props=d;v.Custprops=p}if(r.bookSheets&&typeof l!=="undefined")v.SheetNames=l;if(r.bookSheets?v.SheetNames:r.bookProps)return v}l={};var g={};if(r.bookDeps&&i.calcchain)g=Kl(Lr(e,Fo(i.calcchain)),i.calcchain,r);var b=0;var w={};var k,y;{var x=h.Sheets;d.Worksheets=x.length;d.SheetNames=[];for(var S=0;S!=x.length;++S){d.SheetNames[S]=x[S].name}}var C=s?"bin":"xml";var _=i.workbooks[0].lastIndexOf("/");var A=(i.workbooks[0].slice(0,_+1)+"_rels/"+i.workbooks[0].slice(_+1)+".rels").replace(/^\//,"");if(!Ir(e,A))A="xl/_rels/workbook."+C+".rels";var E=An(Br(e,A,true),A.replace(/_rels.*/,"s5s"));if((i.metadata||[]).length>=1){r.xlmeta=ql(Lr(e,Fo(i.metadata[0])),i.metadata[0],r)}if((i.people||[]).length>=1){r.people=ps(Lr(e,Fo(i.people[0])),r)}if(E)E=Ao(E,h.Sheets);var F=Lr(e,"xl/worksheets/sheet.xml",true)?1:0;e:for(b=0;b!=d.Worksheets;++b){var T="sheet";if(E&&E[b]){k="xl/"+E[b][1].replace(/[\/]?xl\//,"");if(!Ir(e,k))k=E[b][1];if(!Ir(e,k))k=A.replace(/_rels\/.*$/,"")+E[b][1];T=E[b][2]}else{k="xl/worksheets/sheet"+(b+1-F)+"."+C;k=k.replace(/sheet0\./,"sheet.")}y=k.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels");if(r&&r.sheets!=null)switch(typeof r.sheets){case"number":if(b!=r.sheets)continue e;break;case"string":if(d.SheetNames[b].toLowerCase()!=r.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(r.sheets)){var D=false;for(var O=0;O!=r.sheets.length;++O){if(typeof r.sheets[O]=="number"&&r.sheets[O]==b)D=1;if(typeof r.sheets[O]=="string"&&r.sheets[O].toLowerCase()==d.SheetNames[b].toLowerCase())D=1}if(!D)continue e};}Eo(e,k,y,d.SheetNames[b],b,w,l,T,r,h,c,f)}v={Directory:i,Workbook:h,Props:d,Custprops:p,Deps:g,Sheets:l,SheetNames:d.SheetNames,Strings:Rs,Styles:f,Themes:c,SSF:Cr(Z)};if(r&&r.bookFiles){if(e.files){v.keys=n;v.files=e.files}else{v.keys=[];v.files={};e.FullPaths.forEach(function(r,t){r=r.replace(/^Root Entry[\/]/,"");v.keys.push(r);v.files[r]=e.FileIndex[t]})}}if(r&&r.bookVBA){if(i.vba.length>0)v.vbaraw=Lr(e,Fo(i.vba[0]),true);else if(i.defaults&&i.defaults.bin===gs)v.vbaraw=Lr(e,"xl/vbaProject.bin",true)}v.bookType=s?"xlsb":"xlsx";return v}function Do(e,r){var t=r||{};var a="Workbook",n=rr.find(e,a);try{a="/!DataSpaces/Version";n=rr.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);parse_DataSpaceVersionInfo(n.content);a="/!DataSpaces/DataSpaceMap";n=rr.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=parse_DataSpaceMap(n.content);if(i.length!==1||i[0].comps.length!==1||i[0].comps[0].t!==0||i[0].name!=="StrongEncryptionDataSpace"||i[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+a);a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace";n=rr.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=parse_DataSpaceDefinition(n.content);if(s.length!=1||s[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+a);a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary";n=rr.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);parse_Primary(n.content)}catch(l){}a="/EncryptionInfo";n=rr.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var o=parse_EncryptionInfo(n.content);a="/EncryptedPackage";n=rr.find(e,a);if(!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(o[0]==4&&typeof decrypt_agile!=="undefined")return decrypt_agile(o[1],n.content,t.password||"",t);if(o[0]==2&&typeof decrypt_std76!=="undefined")return decrypt_std76(o[1],n.content,t.password||"",t);throw new Error("File is password-protected")}function Oo(e,r){if(e&&!e.SSF){e.SSF=Cr(Z)}if(e&&e.SSF){Xe();Ve(e.SSF);r.revssf=fr(e.SSF);r.revssf[e.SSF[65535]]=0;r.ssf=e.SSF}r.rels={};r.wbrels={};r.Strings=[];r.Strings.Count=0;r.Strings.Unique=0;if(Bs)r.revStrings=new Map;else{r.revStrings={};r.revStrings.foo=[];delete r.revStrings.foo}var t="bin";var a=true;var n=yn();Co(r=r||{});var i=Ur();var s="",l=0;r.cellXfs=[];Us(r.cellXfs,{},{revssf:{General:0}});if(!e.Props)e.Props={};s="docProps/core.xml";Wr(i,s,$n(e.Props,r));n.coreprops.push(s);Fn(r.rels,2,s,Cn.CORE_PROPS);s="docProps/app.xml";if(e.Props&&e.Props.SheetNames){}else if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{var o=[];for(var c=0;c<e.SheetNames.length;++c)if((e.Workbook.Sheets[c]||{}).Hidden!=2)o.push(e.SheetNames[c]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length;Wr(i,s,Vn(e.Props,r));n.extprops.push(s);Fn(r.rels,3,s,Cn.EXT_PROPS);if(e.Custprops!==e.Props&&lr(e.Custprops||{}).length>0){s="docProps/custom.xml";Wr(i,s,Yn(e.Custprops,r));n.custprops.push(s);Fn(r.rels,4,s,Cn.CUST_PROPS)}for(l=1;l<=e.SheetNames.length;++l){var f={"!id":{}};var u=e.Sheets[e.SheetNames[l-1]];var h=(u||{})["!type"]||"sheet";switch(h){case"chart":;default:s="xl/worksheets/sheet"+l+"."+t;Wr(i,s,write_ws_bin(l-1,r,e,f));n.sheets.push(s);Fn(r.wbrels,-1,"worksheets/sheet"+l+"."+t,Cn.WS[0]);}if(u){var d=u["!comments"];var m=false;var p="";if(d&&d.length>0){p="xl/comments"+l+"."+t;Wr(i,p,write_comments_bin(d,r));n.comments.push(p);Fn(f,-1,"../comments"+l+"."+t,Cn.CMNT);m=true}if(u["!legacy"]){if(m)Wr(i,"xl/drawings/vmlDrawing"+l+".vml",os(l,u["!comments"]))}delete u["!comments"];delete u["!legacy"]}if(f["!id"].rId1)Wr(i,_n(s),En(f))}if(r.Strings!=null&&r.Strings.length>0){s="xl/sharedStrings."+t;Wr(i,s,write_sst_bin(r.Strings,r));n.strs.push(s);Fn(r.wbrels,-1,"sharedStrings."+t,Cn.SST)}s="xl/workbook."+t;Wr(i,s,write_wb_bin(e,r));n.workbooks.push(s);Fn(r.rels,1,s,Cn.WB);s="xl/theme/theme1.xml";var v=ts(e.Themes,r);Wr(i,s,v);n.themes.push(s);Fn(r.wbrels,-1,"theme/theme1.xml",Cn.THEME);s="xl/styles."+t;Wr(i,s,write_sty_bin(e,r));n.styles.push(s);Fn(r.wbrels,-1,"styles."+t,Cn.STY);if(e.vbaraw&&a){s="xl/vbaProject.bin";Wr(i,s,e.vbaraw);n.vba.push(s);Fn(r.wbrels,-1,"vbaProject.bin",Cn.VBA)}s="xl/metadata."+t;Wr(i,s,write_xlmeta_bin());n.metadata.push(s);Fn(r.wbrels,-1,"metadata."+t,Cn.XLMETA);Wr(i,"[Content_Types].xml",Sn(n,r));Wr(i,"_rels/.rels",En(r.rels));Wr(i,"xl/_rels/workbook."+t+".rels",En(r.wbrels));delete r.revssf;delete r.ssf;return i}function Mo(e,r){if(e&&!e.SSF){e.SSF=Cr(Z)}if(e&&e.SSF){Xe();Ve(e.SSF);r.revssf=fr(e.SSF);r.revssf[e.SSF[65535]]=0;r.ssf=e.SSF}r.rels={};r.wbrels={};r.Strings=[];r.Strings.Count=0;r.Strings.Unique=0;if(Bs)r.revStrings=new Map;else{r.revStrings={};r.revStrings.foo=[];delete r.revStrings.foo}var t="xml";var a=ks.indexOf(r.bookType)>-1;var n=yn();Co(r=r||{});var i=Ur();var s="",l=0;r.cellXfs=[];Us(r.cellXfs,{},{revssf:{General:0}});if(!e.Props)e.Props={};s="docProps/core.xml";Wr(i,s,$n(e.Props,r));n.coreprops.push(s);Fn(r.rels,2,s,Cn.CORE_PROPS);s="docProps/app.xml";if(e.Props&&e.Props.SheetNames){}else if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{var o=[];for(var c=0;c<e.SheetNames.length;++c)if((e.Workbook.Sheets[c]||{}).Hidden!=2)o.push(e.SheetNames[c]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length;Wr(i,s,Vn(e.Props,r));n.extprops.push(s);Fn(r.rels,3,s,Cn.EXT_PROPS);if(e.Custprops!==e.Props&&lr(e.Custprops||{}).length>0){s="docProps/custom.xml";Wr(i,s,Yn(e.Custprops,r));n.custprops.push(s);Fn(r.rels,4,s,Cn.CUST_PROPS)}var f=["SheetJ5"];r.tcid=0;for(l=1;l<=e.SheetNames.length;++l){var u={"!id":{}};var h=e.Sheets[e.SheetNames[l-1]];var d=(h||{})["!type"]||"sheet";switch(d){case"chart":;default:s="xl/worksheets/sheet"+l+"."+t;Wr(i,s,Sl(l-1,r,e,u));n.sheets.push(s);Fn(r.wbrels,-1,"worksheets/sheet"+l+"."+t,Cn.WS[0]);}if(h){var m=h["!comments"];var p=false;var v="";if(m&&m.length>0){var g=false;m.forEach(function(e){e[1].forEach(function(e){if(e.T==true)g=true})});if(g){v="xl/threadedComments/threadedComment"+l+".xml";Wr(i,v,ms(m,f,r));n.threadedcomments.push(v);Fn(u,-1,"../threadedComments/threadedComment"+l+".xml",Cn.TCMNT)}v="xl/comments"+l+"."+t;Wr(i,v,hs(m,r));n.comments.push(v);Fn(u,-1,"../comments"+l+"."+t,Cn.CMNT);p=true}if(h["!legacy"]){if(p)Wr(i,"xl/drawings/vmlDrawing"+l+".vml",os(l,h["!comments"]))}delete h["!comments"];delete h["!legacy"]}if(u["!id"].rId1)Wr(i,_n(s),En(u))}if(r.Strings!=null&&r.Strings.length>0){s="xl/sharedStrings."+t;Wr(i,s,mi(r.Strings,r));n.strs.push(s);Fn(r.wbrels,-1,"sharedStrings."+t,Cn.SST)}s="xl/workbook."+t;Wr(i,s,Wl(e,r));n.workbooks.push(s);Fn(r.rels,1,s,Cn.WB);s="xl/theme/theme1.xml";Wr(i,s,ts(e.Themes,r));n.themes.push(s);Fn(r.wbrels,-1,"theme/theme1.xml",Cn.THEME);s="xl/styles."+t;Wr(i,s,Vi(e,r));n.styles.push(s);Fn(r.wbrels,-1,"styles."+t,Cn.STY);if(e.vbaraw&&a){s="xl/vbaProject.bin";Wr(i,s,e.vbaraw);n.vba.push(s);Fn(r.wbrels,-1,"vbaProject.bin",Cn.VBA)}s="xl/metadata."+t;Wr(i,s,ns());n.metadata.push(s);Fn(r.wbrels,-1,"metadata."+t,Cn.XLMETA);if(f.length>1){s="xl/persons/person.xml";Wr(i,s,vs(f,r));n.people.push(s);Fn(r.wbrels,-1,"persons/person.xml",Cn.PEOPLE)}Wr(i,"[Content_Types].xml",Sn(n,r));Wr(i,"_rels/.rels",En(r.rels));Wr(i,"xl/_rels/workbook."+t+".rels",En(r.wbrels));delete r.revssf;delete r.ssf;return i}function No(e,r){var t="";switch((r||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":t=S(e.slice(0,12));break;case"binary":t=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(r&&r.type||"undefined"));}return[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3),t.charCodeAt(4),t.charCodeAt(5),t.charCodeAt(6),t.charCodeAt(7)]}function Po(e,r){if(rr.find(e,"EncryptedPackage"))return Do(e,r);return parse_xlscfb(e,r)}function Io(e,r){var t,a=e;var n=r||{};if(!n.type)n.type=C&&Buffer.isBuffer(e)?"buffer":"base64";t=jr(a,n);return To(t,n)}function Ro(e,r){var t=0;e:while(t<e.length)switch(e.charCodeAt(t)){case 10:;case 13:;case 32:++t;break;case 60:return parse_xlml(e.slice(t),r);default:break e;}return ei.to_workbook(e,r)}function Lo(e,r){var t="",a=No(e,r);switch(r.type){case"base64":t=S(e);break;case"binary":t=e;break;case"buffer":t=e.toString("binary");break;case"array":t=Sr(e);break;default:throw new Error("Unrecognized type "+r.type);}if(a[0]==239&&a[1]==187&&a[2]==191)t=gt(t);r.type="binary";return Ro(t,r)}function Bo(e,r){var t=e;if(r.type=="base64")t=S(t);if(typeof ArrayBuffer!=="undefined"&&e instanceof ArrayBuffer)t=new Uint8Array(e);t=typeof a!=="undefined"?a.utils.decode(1200,t.slice(2),"str"):C&&Buffer.isBuffer(e)?e.slice(2).toString("utf16le"):typeof Uint8Array!=="undefined"&&t instanceof Uint8Array?typeof TextDecoder!=="undefined"?new TextDecoder("utf-16le").decode(t.slice(2)):h(t.slice(2)):u(t.slice(2));r.type="binary";return Ro(t,r)}function zo(e){return!e.match(/[^\x00-\x7F]/)?e:bt(e)}function $o(e,r,t,a){if(a){t.type="string";return ei.to_workbook(e,t)}return ei.to_workbook(r,t)}function Wo(e,r){c();var t=r||{};if(t.codepage&&typeof a==="undefined")console.error("Codepage tables are not loaded.  Non-ASCII characters may not give expected results");if(typeof ArrayBuffer!=="undefined"&&e instanceof ArrayBuffer)return Wo(new Uint8Array(e),(t=Cr(t),t.type="array",t));if(typeof Uint8Array!=="undefined"&&e instanceof Uint8Array&&!t.type)t.type=typeof Deno!=="undefined"?"buffer":"array";var n=e,i=[0,0,0,0],s=false;if(t.cellStyles){t.cellNF=true;t.sheetStubs=true}Ls={};if(t.dateNF)Ls.dateNF=t.dateNF;if(!t.type)t.type=C&&Buffer.isBuffer(e)?"buffer":"base64";if(t.type=="file"){t.type=C?"buffer":"binary";n=sr(e);if(typeof Uint8Array!=="undefined"&&!C)t.type="array"}if(t.type=="string"){s=true;t.type="binary";t.codepage=65001;n=zo(e)}if(t.type=="array"&&typeof Uint8Array!=="undefined"&&e instanceof Uint8Array&&typeof ArrayBuffer!=="undefined"){var l=new ArrayBuffer(3),o=new Uint8Array(l);o.foo="bar";if(!o.foo){t=Cr(t);t.type="array";return Wo(N(n),t)}}switch((i=No(n,t))[0]){case 208:if(i[1]===207&&i[2]===17&&i[3]===224&&i[4]===161&&i[5]===177&&i[6]===26&&i[7]===225)return Po(rr.read(n,t),t);break;case 9:if(i[1]<=8)return parse_xlscfb(n,t);break;case 60:return parse_xlml(n,t);case 73:if(i[1]===73&&i[2]===42&&i[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(i[1]===68)return ri(n,t);break;case 84:if(i[1]===65&&i[2]===66&&i[3]===76)return qn.to_workbook(n,t);break;case 80:return i[1]===75&&i[2]<9&&i[3]<9?Io(n,t):$o(e,n,t,s);case 239:return i[3]===60?parse_xlml(n,t):$o(e,n,t,s);case 255:if(i[1]===254){return Bo(n,t)}else if(i[1]===0&&i[2]===2&&i[3]===0)return WK_.to_workbook(n,t);break;case 0:if(i[1]===0){if(i[2]>=2&&i[3]===0)return WK_.to_workbook(n,t);if(i[2]===0&&(i[3]===8||i[3]===9))return WK_.to_workbook(n,t)}break;case 3:;case 131:;case 139:;case 140:return Kn.to_workbook(n,t);case 123:if(i[1]===92&&i[2]===114&&i[3]===116)return rtf_to_workbook(n,t);break;case 10:;case 13:;case 32:return Lo(n,t);
;case 137:if(i[1]===80&&i[2]===78&&i[3]===71)throw new Error("PNG Image File is not a spreadsheet");break;case 8:if(i[1]===231)throw new Error("Unsupported Multiplan 1.x file!");break;case 12:if(i[1]===236)throw new Error("Unsupported Multiplan 2.x file!");if(i[1]===237)throw new Error("Unsupported Multiplan 3.x file!");break;}if(Jn.indexOf(i[0])>-1&&i[2]<=12&&i[3]<=31)return Kn.to_workbook(n,t);return $o(e,n,t,s)}function Uo(e,r){var t=r||{};t.type="file";return Wo(e,t)}function jo(e,r){switch(r.type){case"base64":;case"binary":break;case"buffer":;case"array":r.type="";break;case"file":return ir(r.file,rr.write(e,{type:C?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+r.bookType+"' files");default:throw new Error("Unrecognized type "+r.type);}return rr.write(e,r)}function Ho(e,r){switch(r.bookType){case"ods":return yo(e,r);case"numbers":return write_numbers_iwa(e,r);case"xlsb":return Oo(e,r);default:return Mo(e,r);}}function Vo(e,r){var t=Cr(r||{});var a=Ho(e,t);return Go(a,t)}function Xo(e,r){var t=Cr(r||{});var a=Mo(e,t);return Go(a,t)}function Go(e,r){var t={};var a=C?"nodebuffer":typeof Uint8Array!=="undefined"?"array":"string";if(r.compression)t.compression="DEFLATE";if(r.password)t.type=a;else switch(r.type){case"base64":t.type="base64";break;case"binary":t.type="string";break;case"string":throw new Error("'string' output type invalid for '"+r.bookType+"' files");case"buffer":;case"file":t.type=a;break;default:throw new Error("Unrecognized type "+r.type);}var n=e.FullPaths?rr.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[t.type]||t.type,compression:!!r.compression}):e.generate(t);if(typeof Deno!=="undefined"){if(typeof n=="string"){if(r.type=="binary"||r.type=="base64")return n;n=new Uint8Array(D(n))}}if(r.password&&typeof encrypt_agile!=="undefined")return jo(encrypt_agile(n,r.password),r);if(r.type==="file")return ir(r.file,n);return r.type=="string"?gt(n):n}function Yo(e,r){var t=r||{};var a=write_xlscfb(e,t);return jo(a,t)}function Jo(e,r,t){if(!t)t="";var a=t+e;switch(r.type){case"base64":return y(bt(a));case"binary":return bt(a);case"string":return e;case"file":return ir(r.file,a,"utf8");case"buffer":{if(C)return _(a,"utf8");else if(typeof TextEncoder!=="undefined")return(new TextEncoder).encode(a);else return Jo(a,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})};}throw new Error("Unrecognized type "+r.type)}function Ko(e,r){switch(r.type){case"base64":return x(e);case"binary":return e;case"string":return e;case"file":return ir(r.file,e,"binary");case"buffer":{if(C)return _(e,"binary");else return e.split("").map(function(e){return e.charCodeAt(0)})};}throw new Error("Unrecognized type "+r.type)}function Zo(e,r){switch(r.type){case"string":;case"base64":;case"binary":var t="";for(var a=0;a<e.length;++a)t+=String.fromCharCode(e[a]);return r.type=="base64"?y(t):r.type=="string"?gt(t):t;case"file":return ir(r.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+r.type);}}function qo(e,r){c();Bl(e);var t=Cr(r||{});if(t.cellStyles){t.cellNF=true;t.sheetStubs=true}if(t.type=="array"){t.type="binary";var a=qo(e,t);t.type="array";return D(a)}return Xo(e,t)}function Qo(e,r){c();Bl(e);var t=Cr(r||{});if(t.cellStyles){t.cellNF=true;t.sheetStubs=true}if(t.type=="array"){t.type="binary";var a=Qo(e,t);t.type="array";return D(a)}var n=0;if(t.sheet){if(typeof t.sheet=="number")n=t.sheet;else n=e.SheetNames.indexOf(t.sheet);if(!e.SheetNames[n])throw new Error("Sheet not found: "+t.sheet+" : "+typeof t.sheet)}switch(t.bookType||"xlsb"){case"xml":;case"xlml":return Jo(write_xlml(e,t),t);case"slk":;case"sylk":return Jo(Zn.from_sheet(e.Sheets[e.SheetNames[n]],t,e),t);case"htm":;case"html":return Jo(io(e.Sheets[e.SheetNames[n]],t),t);case"txt":return Ko(cc(e.Sheets[e.SheetNames[n]],t),t);case"csv":return Jo(oc(e.Sheets[e.SheetNames[n]],t),t,"\ufeff");case"dif":return Jo(qn.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"dbf":return Zo(Kn.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"prn":return Jo(ei.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"rtf":return Jo(sheet_to_rtf(e.Sheets[e.SheetNames[n]],t),t);case"eth":return Jo(Qn.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"fods":return Jo(yo(e,t),t);case"wk1":return Zo(WK_.sheet_to_wk1(e.Sheets[e.SheetNames[n]],t),t);case"wk3":return Zo(WK_.book_to_wk3(e,t),t);case"biff2":if(!t.biff)t.biff=2;case"biff3":if(!t.biff)t.biff=3;case"biff4":if(!t.biff)t.biff=4;return Zo(write_biff_buf(e,t),t);case"biff5":if(!t.biff)t.biff=5;case"biff8":;case"xla":;case"xls":if(!t.biff)t.biff=8;return Yo(e,t);case"xlsx":;case"xlsm":;case"xlam":;case"xlsb":;case"numbers":;case"ods":return Vo(e,t);default:throw new Error("Unrecognized bookType |"+t.bookType+"|");}}function ec(e){if(e.bookType)return;var r={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"};var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();if(t.match(/^\.[a-z]+$/))e.bookType=t.slice(1);e.bookType=r[e.bookType]||e.bookType}function rc(e,r,t){var a=t||{};a.type="file";a.file=r;ec(a);return Qo(e,a)}function tc(e,r,t){var a=t||{};a.type="file";a.file=r;ec(a);return qo(e,a)}function ac(e,r,t,a){var n=t||{};n.type="file";n.file=e;ec(n);n.type="buffer";var i=a;if(!(i instanceof Function))i=t;return tr.writeFile(e,Qo(r,n),i)}function nc(e,r,t,a,n,i,s){var l=Ta(t);var o=s.defval,c=s.raw||!Object.prototype.hasOwnProperty.call(s,"raw");var f=true,u=e["!data"]!=null;var h=n===1?[]:{};if(n!==1){if(Object.defineProperty)try{Object.defineProperty(h,"__rowNum__",{value:t,enumerable:false})}catch(d){h.__rowNum__=t}else h.__rowNum__=t}if(!u||e["!data"][t])for(var m=r.s.c;m<=r.e.c;++m){var p=u?(e["!data"][t]||[])[m]:e[a[m]+l];if(p===undefined||p.t===undefined){if(o===undefined)continue;if(i[m]!=null){h[i[m]]=o}continue}var v=p.v;switch(p.t){case"z":if(v==null)break;continue;case"e":v=v==0?null:void 0;break;case"s":;case"d":;case"b":;case"n":break;default:throw new Error("unrecognized type "+p.t);}if(i[m]!=null){if(v==null){if(p.t=="e"&&v===null)h[i[m]]=null;else if(o!==undefined)h[i[m]]=o;else if(c&&v===null)h[i[m]]=null;else continue}else{h[i[m]]=c&&(p.t!=="n"||p.t==="n"&&s.rawNumbers!==false)?v:Va(p,v,s)}if(v!=null)f=false}}return{row:h,isempty:f}}function ic(e,r){if(e==null||e["!ref"]==null)return[];var t={t:"n",v:0},a=0,n=1,i=[],s=0,l="";var o={s:{r:0,c:0},e:{r:0,c:0}};var c=r||{};var f=c.range!=null?c.range:e["!ref"];if(c.header===1)a=1;else if(c.header==="A")a=2;else if(Array.isArray(c.header))a=3;else if(c.header==null)a=0;switch(typeof f){case"string":o=ja(f);break;case"number":o=ja(e["!ref"]);o.s.r=f;break;default:o=f;}if(a>0)n=0;var u=Ta(o.s.r);var h=[];var d=[];var m=0,p=0;var v=e["!data"]!=null;var g=o.s.r,b=0;var w={};if(v&&!e["!data"][g])e["!data"][g]=[];var k=c.skipHidden&&e["!cols"]||[];var y=c.skipHidden&&e["!rows"]||[];for(b=o.s.c;b<=o.e.c;++b){if((k[b]||{}).hidden)continue;h[b]=Na(b);t=v?e["!data"][g][b]:e[h[b]+u];switch(a){case 1:i[b]=b-o.s.c;break;case 2:i[b]=h[b];break;case 3:i[b]=c.header[b-o.s.c];break;default:if(t==null)t={w:"__EMPTY",t:"s"};l=s=Va(t,null,c);p=w[s]||0;if(!p)w[s]=1;else{do{l=s+"_"+p++}while(w[l]);w[s]=p;w[l]=1}i[b]=l;}}for(g=o.s.r+n;g<=o.e.r;++g){if((y[g]||{}).hidden)continue;var x=nc(e,o,g,h,a,i,c);if(x.isempty===false||(a===1?c.blankrows!==false:!!c.blankrows))d[m++]=x.row}d.length=m;return d}var sc=/"/g;function lc(e,r,t,a,n,i,s,l){var o=true;var c=[],f="",u=Ta(t);var h=e["!data"]!=null;var d=h&&e["!data"][t]||[];for(var m=r.s.c;m<=r.e.c;++m){if(!a[m])continue;var p=h?d[m]:e[a[m]+u];if(p==null)f="";else if(p.v!=null){o=false;f=""+(l.rawNumbers&&p.t=="n"?p.v:Va(p,null,l));for(var v=0,g=0;v!==f.length;++v)if((g=f.charCodeAt(v))===n||g===i||g===34||l.forceQuotes){f='"'+f.replace(sc,'""')+'"';break}if(f=="ID")f='"ID"'}else if(p.f!=null&&!p.F){o=false;f="="+p.f;if(f.indexOf(",")>=0)f='"'+f.replace(sc,'""')+'"'}else f="";c.push(f)}if(l.blankrows===false&&o)return null;return c.join(s)}function oc(e,r){var t=[];var a=r==null?{}:r;if(e==null||e["!ref"]==null)return"";var n=ja(e["!ref"]);var i=a.FS!==undefined?a.FS:",",s=i.charCodeAt(0);var l=a.RS!==undefined?a.RS:"\n",o=l.charCodeAt(0);var c=new RegExp((i=="|"?"\\|":i)+"+$");var f="",u=[];var h=a.skipHidden&&e["!cols"]||[];var d=a.skipHidden&&e["!rows"]||[];for(var m=n.s.c;m<=n.e.c;++m)if(!(h[m]||{}).hidden)u[m]=Na(m);var p=0;for(var v=n.s.r;v<=n.e.r;++v){if((d[v]||{}).hidden)continue;f=lc(e,n,v,u,s,o,i,a);if(f==null){continue}if(a.strip)f=f.replace(c,"");if(f||a.blankrows!==false)t.push((p++?l:"")+f)}return t.join("")}function cc(e,r){if(!r)r={};r.FS="\t";r.RS="\n";var t=oc(e,r);if(typeof a=="undefined"||r.type=="string")return t;var n=a.utils.encode(1200,t,"str");return String.fromCharCode(255)+String.fromCharCode(254)+n}function fc(e){var r="",t,a="";if(e==null||e["!ref"]==null)return[];var n=ja(e["!ref"]),i="",s=[],l;var o=[];var c=e["!data"]!=null;for(l=n.s.c;l<=n.e.c;++l)s[l]=Na(l);for(var f=n.s.r;f<=n.e.r;++f){i=Ta(f);for(l=n.s.c;l<=n.e.c;++l){r=s[l]+i;t=c?(e["!data"][f]||[])[l]:e[r];a="";if(t===undefined)continue;else if(t.F!=null){r=t.F;if(!t.f)continue;a=t.f;if(r.indexOf(":")==-1)r=r+":"+r}if(t.f!=null)a=t.f;else if(t.t=="z")continue;else if(t.t=="n"&&t.v!=null)a=""+t.v;else if(t.t=="b")a=t.v?"TRUE":"FALSE";else if(t.w!==undefined)a="'"+t.w;else if(t.v===undefined)continue;else if(t.t=="s")a="'"+t.v;else a=""+t.v;o[o.length]=r+"="+a}}return o}function uc(e,r,t){var a=t||{};var n=e?e["!data"]!=null:a.dense;if(b!=null&&n==null)n=b;var i=+!a.skipHeader;var s=e||{};if(!e&&n)s["!data"]=[];var l=0,o=0;if(s&&a.origin!=null){if(typeof a.origin=="number")l=a.origin;else{var c=typeof a.origin=="string"?La(a.origin):a.origin;l=c.r;o=c.c}}var f={s:{c:0,r:0},e:{c:o,r:l+r.length-1+i}};if(s["!ref"]){var u=ja(s["!ref"]);f.e.c=Math.max(f.e.c,u.e.c);f.e.r=Math.max(f.e.r,u.e.r);if(l==-1){l=u.e.r+1;f.e.r=l+r.length-1+i}}else{if(l==-1){l=0;f.e.r=r.length-1+i}}var h=a.header||[],d=0;var m=[];r.forEach(function(e,r){if(n&&!s["!data"][l+r+i])s["!data"][l+r+i]=[];if(n)m=s["!data"][l+r+i];lr(e).forEach(function(t){if((d=h.indexOf(t))==-1)h[d=h.length]=t;var c=e[t];var f="z";var u="";var p=n?"":Na(o+d)+Ta(l+r+i);var v=n?m[o+d]:s[p];if(c&&typeof c==="object"&&!(c instanceof Date)){if(n)m[o+d]=c;else s[p]=c}else{if(typeof c=="number")f="n";else if(typeof c=="boolean")f="b";else if(typeof c=="string")f="s";else if(c instanceof Date){f="d";if(!a.cellDates){f="n";c=dr(c)}u=v!=null&&v.z&&Be(v.z)?v.z:a.dateNF||Z[14]}else if(c===null&&a.nullError){f="e";c=0}if(!v){if(!n)s[p]=v={t:f,v:c};else m[o+d]=v={t:f,v:c}}else{v.t=f;v.v=c;delete v.w;delete v.R;if(u)v.z=u}if(u)v.z=u}})});f.e.c=Math.max(f.e.c,o+h.length-1);var p=Ta(l);if(n&&!s["!data"][l])s["!data"][l]=[];if(i)for(d=0;d<h.length;++d){if(n)s["!data"][l][d+o]={t:"s",v:h[d]};else s[Na(d+o)+p]={t:"s",v:h[d]}}s["!ref"]=$a(f);return s}function hc(e,r){return uc(null,e,r)}function dc(e,r,t){if(typeof r=="string"){if(e["!data"]!=null){var a=La(r);if(!e["!data"][a.r])e["!data"][a.r]=[];return e["!data"][a.r][a.c]||(e["!data"][a.r][a.c]={t:"z"})}return e[r]||(e[r]={t:"z"})}if(typeof r!="number")return dc(e,Ba(r));return dc(e,Na(t||0)+Ta(r))}function mc(e,r){if(typeof r=="number"){if(r>=0&&e.SheetNames.length>r)return r;throw new Error("Cannot find sheet # "+r)}else if(typeof r=="string"){var t=e.SheetNames.indexOf(r);if(t>-1)return t;throw new Error("Cannot find sheet name |"+r+"|")}else throw new Error("Cannot find sheet |"+r+"|")}function pc(){return{SheetNames:[],Sheets:{}}}function vc(e,r,t,a){var n=1;if(!t)for(;n<=65535;++n,t=undefined)if(e.SheetNames.indexOf(t="Sheet"+n)==-1)break;if(!t||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(t)>=0){var i=t.match(/(^.*?)(\d+)$/);n=i&&+i[2]||0;var s=i&&i[1]||t;for(++n;n<=65535;++n)if(e.SheetNames.indexOf(t=s+n)==-1)break}Rl(t);if(e.SheetNames.indexOf(t)>=0)throw new Error("Worksheet with name |"+t+"| already exists!");e.SheetNames.push(t);e.Sheets[t]=r;return t}function gc(e,r,t){if(!e.Workbook)e.Workbook={};if(!e.Workbook.Sheets)e.Workbook.Sheets=[];var a=mc(e,r);if(!e.Workbook.Sheets[a])e.Workbook.Sheets[a]={};switch(t){case 0:;case 1:;case 2:break;default:throw new Error("Bad sheet visibility setting "+t);}e.Workbook.Sheets[a].Hidden=t}function bc(e,r){e.z=r;return e}function wc(e,r,t){if(!r){delete e.l}else{e.l={Target:r};if(t)e.l.Tooltip=t}return e}function kc(e,r,t){return wc(e,"#"+r,t)}function yc(e,r,t){if(!e.c)e.c=[];e.c.push({t:r,a:t||"SheetJS"})}function xc(e,r,t,a){var n=typeof r!="string"?r:ja(r);var i=typeof r=="string"?r:$a(r);for(var s=n.s.r;s<=n.e.r;++s)for(var l=n.s.c;l<=n.e.c;++l){var o=dc(e,s,l);o.t="n";o.F=i;delete o.v;if(s==n.s.r&&l==n.s.c){o.f=t;if(a)o.D=true}}var c=za(e["!ref"]);if(c.s.r>n.s.r)c.s.r=n.s.r;if(c.s.c>n.s.c)c.s.c=n.s.c;if(c.e.r<n.e.r)c.e.r=n.e.r;if(c.e.c<n.e.c)c.e.c=n.e.c;e["!ref"]=$a(c);return e}var Sc={encode_col:Na,encode_row:Ta,encode_cell:Ba,encode_range:$a,decode_col:Ma,decode_row:Fa,split_cell:Ra,decode_cell:La,decode_range:za,format_cell:Va,sheet_add_aoa:Ga,sheet_add_json:uc,sheet_add_dom:so,aoa_to_sheet:Ya,json_to_sheet:hc,table_to_sheet:lo,table_to_book:oo,sheet_to_csv:oc,sheet_to_txt:cc,sheet_to_json:ic,sheet_to_html:io,sheet_to_formulae:fc,sheet_to_row_object_array:ic,sheet_get_cell:dc,book_new:pc,book_append_sheet:vc,book_set_sheet_visibility:gc,cell_set_number_format:bc,cell_set_hyperlink:wc,cell_set_internal_link:kc,cell_add_comment:yc,sheet_set_array_formula:xc,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};if(typeof parse_xlscfb!=="undefined")e.parse_xlscfb=parse_xlscfb;e.parse_zip=To;e.read=Wo;e.readFile=Uo;e.readFileSync=Uo;e.write=Qo;e.writeFile=rc;e.writeFileSync=rc;e.writeFileAsync=ac;e.utils=Sc;e.writeXLSX=qo;e.writeFileXLSX=tc;e.set_fs=ar;e.set_cptable=g;e.SSF=Ge;if(typeof __stream!=="undefined")e.stream=__stream;if(typeof rr!=="undefined")e.CFB=rr;if(typeof require!=="undefined"){var Cc=undefined;if((Cc||{}).Readable)set_readable(Cc.Readable);try{tr=undefined}catch(_c){}}}if(typeof exports!=="undefined")make_xlsx_lib(exports);else if(typeof module!=="undefined"&&module.exports)make_xlsx_lib(module.exports);else if(typeof define==="function"&&define.amd)define("xlsx",function(){if(!XLSX.version)make_xlsx_lib(XLSX);return XLSX});else make_xlsx_lib(XLSX);if(typeof window!=="undefined"&&!window.XLSX)try{window.XLSX=XLSX}catch(e){}
