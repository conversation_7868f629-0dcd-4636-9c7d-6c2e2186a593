# [SheetJS](https://sheetjs.com)

The SheetJS Community Edition offers battle-tested open-source solutions for
extracting useful data from almost any complex spreadsheet and generating new
spreadsheets that will work with legacy and modern software alike.

[SheetJS Pro](https://sheetjs.com/pro) offers solutions beyond data processing:
Edit complex templates with ease; let out your inner Picasso with styling; make
custom sheets with images/graphs/PivotTables; evaluate formula expressions and
port calculations to web apps; automate common spreadsheet tasks, and much more!

[![Analytics](https://ga-beacon.appspot.com/UA-36810333-1/SheetJS/sheetjs?pixel)](https://github.com/SheetJS/sheetjs)

[![Build Status](https://saucelabs.com/browser-matrix/sheetjs.svg)](https://saucelabs.com/u/sheetjs)

## Documentation

- [API and Usage Documentation](https://docs.sheetjs.com)

- [Downloadable Scripts and Modules](https://cdn.sheetjs.com)

## Related Projects

- <https://oss.sheetjs.com/notes/>: File Format Notes

- [`ssf`](packages/ssf): Format data using ECMA-376 spreadsheet format codes

- [`xlsx-cli`](packages/xlsx-cli/): NodeJS command-line tool for processing files

- [`test_files`](https://github.com/SheetJS/test_files): Sample spreadsheets

- [`cfb`](https://github.com/SheetJS/js-cfb): Container (OLE/ZIP) format library

- [`codepage`](https://github.com/SheetJS/js-codepage): Legacy text encodings

## License

Please consult the attached LICENSE file for details.  All rights not explicitly
granted by the Apache 2.0 License are reserved by the Original Author.

