export declare enum LogLevel {
    none = 0,
    error = 1,
    warn = 2,
    info = 3,
    debug = 4
}
export declare class Logger {
    private level_;
    constructor();
    get level(): "debug" | "info" | "warn" | "error" | "none";
    set level(level: "debug" | "info" | "warn" | "error" | "none");
    write(message: string, ...args: any[]): void;
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
}
