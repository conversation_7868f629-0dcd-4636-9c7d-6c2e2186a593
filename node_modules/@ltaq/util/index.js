"use strict";
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
    result["default"] = mod;
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const os = __importStar(require("os"));
const module_1 = __importDefault(require("module"));
const Logger_1 = require("./Logger");
var AutoReconnect_1 = require("./AutoReconnect");
exports.AutoReconnectBase = AutoReconnect_1.AutoReconnectBase;
var AutoReconnect_2 = require("./AutoReconnect");
exports.AutoReconnectSocket = AutoReconnect_2.AutoReconnectSocket;
exports.AutoReconnectZmqPub = AutoReconnect_2.AutoReconnectZmqPub;
exports.AutoReconnectZmqSub = AutoReconnect_2.AutoReconnectZmqSub;
var AutoReconnect_3 = require("./AutoReconnect");
exports.AutoReconnectWebSocket = AutoReconnect_3.AutoReconnectWebSocket;
var zmq_1 = require("./zmq");
exports.ZmqPub = zmq_1.ZmqPub;
exports.ZmqSub = zmq_1.ZmqSub;
var MultiplexMessageStreamClientPool_1 = require("./streams/MultiplexMessageStreamClientPool");
exports.MultiplexMessageStreamClientPool = MultiplexMessageStreamClientPool_1.MultiplexMessageStreamClientPool;
var MultiplexMessageStreamServer_1 = require("./streams/MultiplexMessageStreamServer");
exports.MultiplexMessageStreamServer = MultiplexMessageStreamServer_1.MultiplexMessageStreamServer;
var MultiplexMessageStream_1 = require("./streams/MultiplexMessageStream");
exports.MultiplexClientMessageStream = MultiplexMessageStream_1.MultiplexClientMessageStream;
var MultiplexMessageStream_2 = require("./streams/MultiplexMessageStream");
exports.MultiplexServerMessageStream = MultiplexMessageStream_2.MultiplexServerMessageStream;
var helpers_1 = require("./streams/helpers");
exports.StreamHelper = helpers_1.StreamHelper;
var TcpMessageStream_1 = require("./streams/TcpMessageStream");
exports.TcpMessageStream = TcpMessageStream_1.TcpMessageStream;
var WebSocketMessageStream_1 = require("./streams/WebSocketMessageStream");
exports.WebSocketMessageStream = WebSocketMessageStream_1.WebSocketMessageStream;
const createRequire = module_1.default.createRequire || module_1.default['createRequireFromPath'];
// noinspection JSUnusedGlobalSymbols
function loadAddon(base, file) {
    let build = path.resolve(base, 'pre-build');
    if (fs.existsSync(build))
        return require(path.resolve(build, file));
    build = path.resolve(base, 'build');
    let version = process.versions.node;
    let folderBuild1 = os.platform() + '-' + os.arch();
    let folderBuild2 = os.platform() + '-' + os.arch() + '-v' + version;
    let filePaths = [];
    for (const folderBuild of [folderBuild1, folderBuild2]) {
        for (let config of ['Debug', 'Release']) {
            let filePath;
            if (os.platform() === "win32") {
                filePath = path.resolve(build, folderBuild, path.dirname(file), config, path.basename(file));
            }
            else if (os.platform() === "darwin") {
                throw new Error('Darwin is not supported yet.');
            }
            else {
                filePath = path.resolve(build, folderBuild + '-' + config, file);
            }
            filePaths.push(filePath);
        }
    }
    for (let i = 0; i < filePaths.length - 2; i++) {
        if (fs.existsSync(filePaths[i]))
            return require(filePaths[i]);
    }
    return require(filePaths[filePaths.length - 1]);
}
exports.loadAddon = loadAddon;
// noinspection JSUnusedGlobalSymbols
function loadConfig(root, file) {
    const possibilities = [];
    if (process.env.LITONG_CONFIG_DIR) {
        const pkg = createRequire(path.join(root, 'foo'))('./package.json');
        const root2 = path.resolve(process.env.LITONG_CONFIG_DIR, pkg.name);
        loadConfigPossibilities(possibilities, root2, false, file);
    }
    loadConfigPossibilities(possibilities, root, true, file);
    for (const p of possibilities) {
        try {
            return loadConfigTryFile(p);
        }
        catch (error) {
            if (error.code !== 'MODULE_NOT_FOUND')
                throw error;
        }
    }
    const error = new class extends Error {
        constructor() {
            super(...arguments);
            this.code = "CONFIG_FILE_NOT_FOUND";
            // noinspection JSUnusedGlobalSymbols
            this.errno = -1;
            // noinspection JSUnusedGlobalSymbols
            this.message = `Can not load config at : ${possibilities.join(' or ')}.`;
            // noinspection JSUnusedGlobalSymbols
            this.name = "Error";
        }
    };
    Error.captureStackTrace(error);
    throw error;
}
exports.loadConfig = loadConfig;
function loadConfigPossibilities(result, root, rootIsDir, file) {
    if (!file) {
        result.push(path.join(root, "config.json"));
        result.push(path.join(root, "config.js"));
        if (!rootIsDir) {
            result.push(root + ".json");
            result.push(root + ".js");
        }
    }
    else if (path.extname(file)) {
        result.push(path.resolve(root, file));
    }
    else {
        result.push(path.join(root, file, "config.json"));
        result.push(path.join(root, file, "config.js"));
        result.push(path.join(root, file + ".json"));
        result.push(path.join(root, file + ".js"));
    }
}
function loadConfigTryFile(file) {
    const basename = path.basename(file);
    return createRequire(file)('./' + basename);
}
// noinspection JSUnusedGlobalSymbols
exports.log = new Logger_1.Logger();
