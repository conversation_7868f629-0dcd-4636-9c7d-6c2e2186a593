# 说明

## 1. `log`
* `write(message: string):void`

  `console.log(<时间> message)`
  
* `info(message: string):void`

  `console.info(<时间> 'INFO' message)`

* `warn(message: string):void`

  `console.warn(<时间> 'WARN' message)`
  
* `error(message: string):void`
  
  `console.error(<时间> 'ERROR' message)`

注意 info/warn/error 都是输出到 stderr，而 write 是输出到 stdout.


##  2. `loadAddon(base: string, file: string):any`

加载 NAPI 插件。会尝试在以下目录中查找 {file}：
 
* {base}/pre-build
* {base}/build/{platform-arch}/Debug
* {base}/build/{platform-arch}/Release
* {base}/build/{platform-arch-{version}}/Debug
* {base}/build/{platform-arch-{version}}/Release

其中 {version} 为 node 的版本。
比如 `const addon = loadAddon(__dirname, "my-addon.node");`。

## 3. `loadConfig(root: string, file?: string):any;`

加载配置文件。如果 file 为空，则尝试以下位置：
* {LITONG_CONFIG_DIR}/{package-name}/config.json
* {LITONG_CONFIG_DIR}/{package-name}/config.js
* {LITONG_CONFIG_DIR}/{package-name}.json
* {LITONG_CONFIG_DIR}/{package-name}.js
* {root}/config.json
* {root}/config.js

加载配置文件。如果 file 不为空且无扩展名，则尝试以下位置：
* {LITONG_CONFIG_DIR}/{package-name}/{file}/config.json
* {LITONG_CONFIG_DIR}/{package-name}/{file}/config.js
* {LITONG_CONFIG_DIR}/{package-name}/{file}.json
* {LITONG_CONFIG_DIR}/{package-name}/{file}.js
* {root}/{file}/config.json
* {root}/{file}/config.js
* {root}/{file}.json
* {root}/{file}.js

如果 file 有扩展名，则会尝试一下位置： 
* {LITONG_CONFIG_DIR}/{package-name}/{file}
* {root}/{file}

其中 {LITONG_CONFIG_DIR} 为环境变量 LITONG_CONFIG_DIR 的值
（即 `process.env.LITONG_CONFIG_DIR`)。


## 3. `class ZmqPub`

### 3.1 `ZmqPub.new(options: { port: number, host: string, topic: string } | string): ZmqPub`

构造函数。字符串参数格式为: tcp://{ip}:{port}/{topic}

### 3.2 `ZmqPub.send(...parts: Buffer[]): void`

发送消息， 参数为消息片段数组。

### 3.3 `ZmqPub.on(type: 'connect', listener: () => void): this`

连接建立事件

### 3.4 `ZmqPub.on(type: 'disconnect', listener: () => void): this`

连接断开事件，仅在实例的 `connect` 事件发生后才可能触发

### 3.5 `ZmqPub.on(type: 'error', listener: (error: Error) => void): this`

连接错误事件。参数为错误对象。

### 3.6 `ZmqPub.on(type: 'close', listener: () => void): this`

连接关闭事件



## 4. `class ZmqSub

### 4.1 `ZmqSub.new(options: { port: number, host: string, topic: string } | string): ZmqPub`

构造函数。字符串参数格式为: tcp://{ip}:{port}/{topic}

### 4.2 `ZmqSub.on(type: 'message', listener: (...parts: Buffer[]) => void): this`

消息事件。参数为消息片段数组

### 4.3 `ZmqSub.on(type: 'connect', listener: () => void): this`

连接建立事件

### 4.4 `ZmqSub.on(type: 'disconnect', listener: () => void): this`

连接断开事件，仅在实例的 `connect` 事件发生后才可能触发

### 4.5 `ZmqSub.on(type: 'error', listener: (error: Error) => void): this`

连接错误事件。参数为错误对象。

### 4.6 `ZmqSub.on(type: 'close', listener: () => void): this`

连接关闭事件



## 5. `class AutoReconnectBase<T>`

断线自动重连基类

### 5.1 `AutoReconnectBase<T>.on(type: 'connect', listener: (instance: T) => void): this`

连接建立事件。回调参数为连接实列

### 5.2 `AutoReconnectBase<T>.on(type: 'disconnect', listener: (instance: T) => void): this`

连接断开事件，仅在相同实例的 `connect` 事件发生后才可能触发。回调参数为连接实列

### 5.3 `AutoReconnectBase<T>.on(type: 'error', listener: (instance: T, error: Error) => void): this`

连接错误事件。参数 `instance` 为连接实例；参数 `error` 为错误对象。

### 5.4 `AutoReconnectBase<T>.on(type: 'create', listener: (instance: T) => void): this`

连接创建事件。回调参数为连接实列

### 5.5 `AutoReconnectBase<T>.on(type: 'close', listener: (instance: T) => void): this`

连接关闭事件。回调参数为连接实列

### 5.6 `AutoReconnectBase<T>.close(): void`

关闭当前连接实例（如果已创建），并停止重连尝试。

### 5.7 `protected abstract AutoReconnectBase<T>.create_(options: AutoReconnectCallbacks): T`

创建连接实例。 非抽象派生类应实现该方法，创建连接实例，并设定回调 options 中的回调方法。
需要注意不要在未调用options.onConnect的情况下调用options.onDisconnect。

### 5.8 `protected abstract AutoReconnectBase<T>.close_(instance: T): void`

关闭连接实例。非抽象派生类应实现该方法。

 
## 6. `class AutoReconnectSocket`
 
 对 net.Socket 的自动重连实现
 
 
## 7. `class AutoReconnectZmqPub`
 
 对 ZmqPub 的自动重连实现
 
  
## 8. `class AutoReconnectZmqSub`
 
对 ZmqSub 的自动重连实现
 
## 8. `流分复用`

Server side example:
```typescript
import * as net from "net";
import {StreamHelper} from "@ltaq/util";
import {MultiplexMessageStreamServer, IncomingSubMessageStream} from "@ltaq/util";

const server = new MultiplexMessageStreamServer();
server.route((s: IncomingSubMessageStream) => {
    if (s.url === '/ltaq') {
        const upstream = net.connect(80, "ltaq.com");
        upstream.on('connect', () => s.connect());
        upstream.on('error', (error: Error) => s.close(error));
        upstream.on('close', () => s.close());
        s.on('close', () => upstream.end());
        s.on('message', (message: Buffer) => upstream.write(message));
        upstream.on('data', (data: Buffer) => s.write(data));
        return true;
    }
    return false;
});
server.route((s: IncomingSubMessageStream) => {
    s.on('connect', function () {
        console.log('connect');
    });
    s.on('close', function (error) {
        console.log('close', error && error.message || false);
    });
    s.on('message', function (message) {
        console.log('message', message.toString('utf8'));
        s.write(Buffer.from("Hello world, too!"));
    });
    s.connect();
    return true;
});
const tcpServer = net.createServer(function (connection) {
    server.accept(StreamHelper.wrap(connection));
});
tcpServer.listen(12348);
```

Client side example:
```typescript
import {MultiplexMessageStreamClientPool} from "@ltaq/util";

const pool = new MultiplexMessageStreamClientPool();
const c1 = pool.connect("tcp://127.0.0.1:12348/ltaq");
c1.on('connect', function () {
    console.log(1, 'client:connect');
    c1.write(Buffer.from("GET / HTTP/1.1\r\n" +
        "User-Agent: Mozilla/4.0 (compatible; MSIE5.01; Windows NT)\r\n" +
        "Host: www.ltaq.com\r\n" +
        "Accept-Encoding: gzip, deflate\r\n" +
        "Connection: close\r\n\n", "utf8"));
});
c1.on('close', function (error) {
    console.log(1, 'client:close', error && error.message || false);
});
c1.on('message', function (message) {
    console.log(1, 'client:message', message.toString('utf8'));
    c1.close();
});

const c2 = pool.connect("tcp://127.0.0.1:12348/");
c2.on('connect', function () {
    console.log(2, 'client:connect');
    c2.write(Buffer.from("GET / HTTP/1.1\r\n" +
        "User-Agent: Mozilla/4.0 (compatible; MSIE5.01; Windows NT)\r\n" +
        "Host: www.ltaq.com\r\n" +
        "Accept-Encoding: gzip, deflate\r\n" +
        "Connection: close\r\n\n", "utf8"));
});
c2.on('close', function (error) {
    console.log(2, 'client:close', error && error.message || false);
});
c2.on('message', function (message) {
    console.log(2, 'client:message', message.toString('utf8'));
    c2.close();
});
```
