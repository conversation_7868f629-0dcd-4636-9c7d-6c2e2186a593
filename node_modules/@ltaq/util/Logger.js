"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const util_1 = __importDefault(require("util"));
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["none"] = 0] = "none";
    LogLevel[LogLevel["error"] = 1] = "error";
    LogLevel[LogLevel["warn"] = 2] = "warn";
    LogLevel[LogLevel["info"] = 3] = "info";
    LogLevel[LogLevel["debug"] = 4] = "debug";
})(LogLevel = exports.LogLevel || (exports.LogLevel = {}));
class Logger {
    constructor() {
        this.level_ = LogLevel.debug;
        this.level = process.env.LITONG_LOG_LEVEL;
    }
    get level() {
        return LogLevel[this.level_];
    }
    set level(level) {
        if (["debug", "info", "warn", "error", "none"].indexOf(level) >= 0)
            this.level_ = LogLevel[level];
    }
    write(message, ...args) {
        process.stdout.write(formatDate(new Date()));
        process.stdout.write(' ');
        process.stdout.write(util_1.default.format(message, ...args));
        process.stdout.write('\n');
    }
    debug(message, ...args) {
        if (this.level_ >= LogLevel.debug) {
            process.stderr.write(formatDate(new Date()));
            process.stderr.write(' D ');
            process.stderr.write(util_1.default.format(message, ...args));
            process.stderr.write('\n');
        }
    }
    info(message, ...args) {
        if (this.level_ >= LogLevel.info) {
            process.stderr.write(formatDate(new Date()));
            process.stderr.write(' I ');
            process.stderr.write(util_1.default.format(message, ...args));
            process.stderr.write('\n');
        }
    }
    warn(message, ...args) {
        if (this.level_ >= LogLevel.warn) {
            process.stderr.write(formatDate(new Date()));
            process.stderr.write(' W ');
            process.stderr.write(util_1.default.format(message, ...args));
            process.stderr.write('\n');
        }
    }
    error(message, ...args) {
        if (this.level_ >= LogLevel.error) {
            process.stderr.write(formatDate(new Date()));
            process.stderr.write(' E ');
            process.stderr.write(util_1.default.format(message, ...args));
            process.stderr.write('\n');
        }
    }
}
exports.Logger = Logger;
function formatDate(date) {
    return date.getFullYear()
        + '-' + ("00" + (date.getMonth() + 1)).substr(-2)
        + '-' + ("00" + date.getDate()).substr(-2)
        + ' ' + ("00" + date.getHours()).substr(-2)
        + ':' + ("00" + date.getMinutes()).substr(-2)
        + ':' + ("00" + date.getSeconds()).substr(-2)
        + '.' + ("000" + date.getMilliseconds()).substr(-3);
}
