/// <reference types="node" />
import { EventEmitter } from "events";
import * as net from "net";
import { ZmqPub, ZmqSub } from "./zmq";
import WebSocket from "ws";
export interface AutoReconnectCallbacks {
    onConnect: () => void;
    onClose: (error?: Error) => void;
}
export declare abstract class AutoReconnectBase<T> extends EventEmitter {
    private instance_;
    private failures_;
    private retryTimer_;
    private closing_;
    private connected_;
    private defaultReconnectDelay_;
    private reconnectDelay_;
    get connected(): boolean;
    setReconnectDelay(value: number | ((failures: number) => number) | null): void;
    protected constructor();
    on(type: 'connect', listener: (instance: T) => void): this;
    on(type: 'connection', listener: (instance: T) => void): this;
    on(type: 'disconnect', listener: (instance: T) => void): this;
    on(type: 'create', listener: (instance: T) => void): this;
    on(type: 'close', listener: (instance: T, error?: Error) => void): this;
    close(): void;
    protected abstract create_(options: AutoReconnectCallbacks): T;
    protected abstract close_(instance: T): void;
    private connect_;
    private instanceConnect_;
    private instanceClose_;
}
export declare class AutoReconnectSocket extends AutoReconnectBase<net.Socket> {
    private options;
    constructor(options: net.NetConnectOpts);
    protected close_(instance: net.Socket): void;
    protected create_(options: AutoReconnectCallbacks): net.Socket;
}
export declare class AutoReconnectZmqSub extends AutoReconnectBase<ZmqSub> {
    private options;
    constructor(options: {
        port: number;
        host: string;
        topic: string;
    } | string);
    protected close_(instance: ZmqSub): void;
    protected create_(options: AutoReconnectCallbacks): ZmqSub;
}
export declare class AutoReconnectZmqPub extends AutoReconnectBase<ZmqPub> {
    private options;
    constructor(options: {
        port: number;
        host: string;
        topic: string;
    } | string);
    protected close_(instance: ZmqPub): void;
    protected create_(options: AutoReconnectCallbacks): ZmqPub;
}
export declare class AutoReconnectWebSocket extends AutoReconnectBase<WebSocket> {
    private url;
    constructor(url: string);
    protected close_(instance: WebSocket): void;
    protected create_(options: AutoReconnectCallbacks): WebSocket;
}
