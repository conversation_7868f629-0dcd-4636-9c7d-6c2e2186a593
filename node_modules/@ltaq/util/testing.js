"use strict";
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
    result["default"] = mod;
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const net = __importStar(require("net"));
const index_1 = require("./index");
const index_2 = require("./index");
const MultiplexMessageStreamClientPool_1 = require("./streams/MultiplexMessageStreamClientPool");
/*************************************************************************/
function testServer() {
    const server = new index_2.MultiplexMessageStreamServer();
    server.route((s) => {
        if (s.url === '/ltaq') {
            const upstream = net.connect(80, "ltaq.com");
            upstream.on('connect', () => s.connect());
            upstream.on('error', (error) => s.close(error));
            upstream.on('close', () => s.close());
            s.on('close', () => upstream.end());
            s.on('message', (message) => upstream.write(message));
            upstream.on('data', (data) => s.write(data));
            return true;
        }
        return false;
    });
    server.route((s) => {
        s.on('connect', function () {
            console.log('connect');
        });
        s.on('close', function (error) {
            console.log('close', error && error.message || false);
        });
        s.on('message', function (message) {
            console.log('message', message.toString('utf8'));
            s.write(Buffer.from("Hello world, too!"));
        });
        s.connect();
        return true;
    });
    const tcpServer = net.createServer(function (connection) {
        server.accept(index_1.StreamHelper.wrap(connection));
    });
    tcpServer.listen(12348, "0.0.0.0");
}
/*************************************************************************/
function testClient() {
    const pool = new MultiplexMessageStreamClientPool_1.MultiplexMessageStreamClientPool();
    const c1 = pool.connect("tcp://127.0.0.1:12348/ltaq");
    c1.on('connect', function () {
        console.log(1, 'client:connect');
        c1.write(Buffer.from("GET / HTTP/1.1\r\n" +
            "User-Agent: Mozilla/4.0 (compatible; MSIE5.01; Windows NT)\r\n" +
            "Host: www.ltaq.com\r\n" +
            "Accept-Encoding: gzip, deflate\r\n" +
            "Connection: close\r\n\r\n", "utf8"));
    });
    c1.on('close', function (error) {
        console.log(1, 'client:close', error && error.message || false);
    });
    c1.on('message', function (message) {
        console.log(1, 'client:message', message.toString('utf8'));
        c1.close();
    });
    const c2 = pool.connect("tcp://127.0.0.1:12348/");
    c2.on('connect', function () {
        console.log(2, 'client:connect');
        c2.write(Buffer.from("GET / HTTP/1.1\r\n" +
            "User-Agent: Mozilla/4.0 (compatible; MSIE5.01; Windows NT)\r\n" +
            "Host: www.ltaq.com\r\n" +
            "Accept-Encoding: gzip, deflate\r\n" +
            "Connection: close\r\n\r\n", "utf8"));
    });
    c2.on('close', function (error) {
        console.log(2, 'client:close', error && error.message || false);
    });
    c2.on('message', function (message) {
        console.log(2, 'client:message', message.toString('utf8'));
        c2.close();
    });
}
/*************************************************************************/
testServer();
testClient();
