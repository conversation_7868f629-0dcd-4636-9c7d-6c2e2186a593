"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var Command;
(function (Command) {
    Command[Command["connect"] = 1] = "connect";
    Command[Command["close"] = 2] = "close";
    Command[Command["connect2"] = 17] = "connect2";
    Command[Command["close2"] = 18] = "close2";
})(Command = exports.Command || (exports.Command = {}));
class EncoderHelper {
    static encodeString(buffer, offset, value) {
        const urlSize = Buffer.byteLength(value);
        offset = buffer.writeUInt16LE(urlSize, offset);
        buffer.write(value, offset, "utf8");
        return offset + urlSize;
    }
    static encodeStringLength(value) {
        return 2 + Buffer.byteLength(value);
    }
    static decodeString(buffer, offset) {
        let size = buffer.readUInt16LE(offset);
        offset += 2;
        const end = offset + size;
        const value = buffer.toString("utf8", offset, end);
        return [value, end];
    }
}
exports.EncoderHelper = EncoderHelper;
class LengthPrefixedBuffer {
    constructor(callback) {
        this.callback = callback;
        this.buffer_ = Buffer.allocUnsafe(1024 * 1024);
        this.bufferSize_ = 0;
    }
    append(data) {
        const total = this.bufferSize_ + data.length;
        let capacity = this.buffer_.length;
        while (capacity < total) {
            capacity *= 2;
        }
        if (capacity != this.buffer_.length) {
            const buffer = Buffer.allocUnsafe(capacity);
            this.buffer_.copy(buffer, 0, 0, this.bufferSize_);
            this.buffer_ = buffer;
        }
        data.copy(this.buffer_, this.bufferSize_, 0, data.length);
        this.bufferSize_ += data.length;
        let offset = 0;
        while (true) {
            if (this.bufferSize_ - offset < 4)
                break;
            const len = this.buffer_.readUInt32LE(offset);
            if (this.bufferSize_ - offset < 4 + len)
                break;
            offset += 4;
            this.callback(this.buffer_.slice(offset, offset + len));
            offset += len;
        }
        this.buffer_.copy(this.buffer_, 0, offset, this.bufferSize_);
        this.bufferSize_ = this.bufferSize_ - offset;
    }
}
exports.LengthPrefixedBuffer = LengthPrefixedBuffer;
