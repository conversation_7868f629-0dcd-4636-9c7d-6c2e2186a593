/// <reference types="node" />
import { MessageStream, SubMessageStream } from "./MessageStream";
import { EventEmitter } from "events";
export interface IncomingSubMessageStream extends SubMessageStream {
    connect(): void;
}
declare class MultiplexBaseMessageStream extends EventEmitter {
    private main;
    private mainConnected;
    private handler;
    private outStreams_;
    private inStreams_;
    private heartbeatTimer_;
    private nextOutSubStreamId_;
    constructor(main: MessageStream, mainConnected: boolean, handler: (s: IncomingSubMessageStream) => boolean);
    close(error?: Error): void;
    connect(url: string, header?: Buffer): SubMessageStream;
    private onMainConnect_;
    private onMainClose_;
    private onMainMessage_;
    private onCommandMessage_;
    /******************************************/
    private onOutStreamMessage_;
    private connectOutSubStream_;
    private closeOutSubStream_;
    private writeOutSubStream_;
    /***********************************/
    private onInStreamMessage_;
    private closeInSubStream_;
    private inSubStreamConnected_;
    private writeInSubStream_;
    private static readError_;
    private InSubStream;
    private OutSubStream;
}
export declare class MultiplexClientMessageStream extends MultiplexBaseMessageStream {
    constructor(main: MessageStream, handler: (s: IncomingSubMessageStream) => boolean);
}
export declare class MultiplexServerMessageStream extends MultiplexBaseMessageStream {
    constructor(main: MessageStream, handler: (s: IncomingSubMessageStream) => boolean);
}
export {};
