/// <reference types="node" />
import { EventEmitter } from "events";
import { MessageStream } from "./MessageStream";
import { IncomingSubMessageStream } from "./MultiplexMessageStream";
export declare class MultiplexMessageStreamServer extends EventEmitter {
    private connections_;
    private routers_;
    close(): void;
    accept(c: MessageStream): void;
    route(handler: (s: IncomingSubMessageStream) => boolean): void;
    route(urlPrefix: string, handler: (s: IncomingSubMessageStream) => boolean): void;
    private routeSubStream_;
}
