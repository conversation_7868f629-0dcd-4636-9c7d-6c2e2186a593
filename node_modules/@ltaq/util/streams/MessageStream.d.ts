/// <reference types="node" />
import { EventEmitter } from "events";
export interface MessageStream extends EventEmitter {
    on(event: 'connect', handler: () => void): this;
    on(event: 'close', handler: (error?: Error) => void): this;
    on(event: 'message', handler: (message: Buffer) => void): this;
    write(...message: Buffer[]): void;
    close(error?: Error): void;
}
export interface SubMessageStream extends MessageStream {
    readonly id: number;
    readonly url: string;
    readonly header: Buffer;
}
