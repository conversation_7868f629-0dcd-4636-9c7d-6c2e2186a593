/// <reference types="node" />
import * as net from "net";
import { MessageStream } from "./MessageStream";
import WebSocket from "ws";
import { IncomingSubMessageStream } from "./MultiplexMessageStream";
export declare class StreamHelper {
    static wrap(socket: net.Socket): MessageStream;
    static wrap(socket: WebSocket): MessageStream;
    static bind(downstream: IncomingSubMessageStream, upstream: MessageStream, dataTransformer?: {
        up: (data: Buffer) => Buffer;
        down: (data: Buffer) => Buffer;
    } | false): void;
}
