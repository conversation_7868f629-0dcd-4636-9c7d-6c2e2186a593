"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const events_1 = require("events");
const MultiplexMessageStream_1 = require("./MultiplexMessageStream");
class MultiplexMessageStreamServer extends events_1.EventEmitter {
    constructor() {
        super(...arguments);
        this.connections_ = new Set();
        this.routers_ = [];
    }
    close() {
        for (const s of this.connections_)
            s.close();
    }
    // noinspection JSUnusedGlobalSymbols
    accept(c) {
        const s = new MultiplexMessageStream_1.MultiplexServerMessageStream(c, x => this.routeSubStream_(x));
        s.on('close', () => {
            this.connections_.delete(s);
        });
        this.connections_.add(s);
    }
    // noinspection JSUnusedGlobalSymbols
    route(urlPrefix, handler) {
        let h;
        if (typeof (urlPrefix) === "string") {
            h = (s) => {
                if (s.url.startsWith(urlPrefix) && handler)
                    return handler(s);
                return false;
            };
        }
        else {
            h = urlPrefix;
        }
        this.routers_.push(h);
    }
    routeSubStream_(s) {
        // noinspection JSUnusedAssignment
        let handled = false;
        for (const r of this.routers_) {
            if ((handled = r(s)))
                break;
        }
        return handled;
    }
}
exports.MultiplexMessageStreamServer = MultiplexMessageStreamServer;
