"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const events_1 = require("events");
class WebSocketMessageStream extends events_1.EventEmitter {
    constructor(socket) {
        super();
        this.socket = socket;
        this.closing_ = false;
        socket.addEventListener('open', () => {
            if (!this.closing_)
                this.emit('connect');
        });
        socket.addEventListener('error', (event) => {
            if (!this.closing_) {
                this.closing_ = true;
                this.emit('close', event.error);
            }
        });
        socket.addEventListener('close', () => {
            if (!this.closing_) {
                this.closing_ = true;
                this.emit('close');
            }
        });
        socket.addEventListener('message', (event) => {
            if (!this.closing_)
                this.emit('message', event.data);
        });
        socket.addEventListener('ping', () => {
            if (!this.closing_)
                this.socket.pong();
        });
    }
    close(error) {
        if (!this.closing_) {
            this.closing_ = true;
            this.socket.close();
            this.emit('close', error);
        }
    }
    write(...message) {
        if (!this.closing_) {
            this.socket.send(Buffer.concat(message));
        }
    }
}
exports.WebSocketMessageStream = WebSocketMessageStream;
