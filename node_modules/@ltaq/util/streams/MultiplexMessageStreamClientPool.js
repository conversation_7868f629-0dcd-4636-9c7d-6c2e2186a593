"use strict";
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
    result["default"] = mod;
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const events_1 = require("events");
const net = __importStar(require("net"));
const helpers_1 = require("./helpers");
const assert = __importStar(require("assert"));
const ws_1 = __importDefault(require("ws"));
const MultiplexMessageStream_1 = require("./MultiplexMessageStream");
class MultiplexMessageStreamClientPool extends events_1.EventEmitter {
    constructor() {
        super(...arguments);
        this.clients_ = new Map();
        this.providers_ = [];
        this.closing_ = false;
    }
    connect(url) {
        assert.ok(!this.closing_, "MultiplexMessageStreamClientPool has been closed.");
        const address = new URL(url);
        const info = this.tryGetFactoryInfo_(address);
        let c = this.clients_.get(info.key);
        if (!c) {
            c = new MultiplexMessageStream_1.MultiplexClientMessageStream(info.factory(), () => false);
            c.on('close', this.onClientClose_.bind(this, info.key, c));
            this.clients_.set(info.key, c);
        }
        return c.connect(info.path);
    }
    // noinspection JSUnusedGlobalSymbols
    addProvider(provider) {
        this.providers_.push(provider);
    }
    // noinspection JSUnusedGlobalSymbols
    removeProvider(provider) {
        const idx = this.providers_.indexOf(provider);
        if (idx >= 0)
            this.providers_.splice(idx, 1);
    }
    tryGetFactoryInfo_(url) {
        let s;
        for (const f of this.providers_) {
            s = f(url);
            if (s)
                break;
        }
        if (!s && url.protocol === "tcp:") {
            const credential = (url.username && url.password) ? (url.username + ':' + url.password)
                : (url.username || url.password || '');
            const key = url.protocol + (credential ? credential + '@' : '') + url.host;
            s = {
                key: key,
                path: url.pathname + url.search,
                factory: () => helpers_1.StreamHelper.wrap(net.connect(parseInt(url.port), url.hostname))
            };
        }
        if (!s && url.protocol === "ws:") {
            const credential = (url.username && url.password) ? (url.username + ':' + url.password)
                : (url.username || url.password || '');
            const key = url.protocol + (credential ? credential + '@' : '') + url.host;
            s = {
                key: key,
                path: url.pathname + url.search,
                factory: () => helpers_1.StreamHelper.wrap(new ws_1.default(key))
            };
        }
        if (!s)
            throw new URIError(`Can not connect to ${url.href}.`);
        return s;
    }
    onClientClose_(key) {
        this.clients_.delete(key);
        if (this.closing_ && !this.clients_.size)
            this.emit('close');
    }
    close() {
        if (!this.closing_) {
            this.closing_ = true;
            if (!this.clients_.size)
                this.emit('close');
            else
                for (let [, c] of this.clients_)
                    c.close();
        }
    }
}
exports.MultiplexMessageStreamClientPool = MultiplexMessageStreamClientPool;
