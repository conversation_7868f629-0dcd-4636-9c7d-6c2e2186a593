/// <reference types="node" />
import { EventEmitter } from "events";
import { MessageStream } from "./MessageStream";
export interface MultiplexMessageStreamClientPoolProvider {
    (url: URL): {
        key: string;
        path: string;
        factory: () => MessageStream;
    } | null;
}
export declare class MultiplexMessageStreamClientPool extends EventEmitter {
    private clients_;
    private providers_;
    private closing_;
    connect(url: string): import("./MessageStream").SubMessageStream;
    addProvider(provider: MultiplexMessageStreamClientPoolProvider): void;
    removeProvider(provider: MultiplexMessageStreamClientPoolProvider): void;
    private tryGetFactoryInfo_;
    private onClientClose_;
    close(): void;
}
