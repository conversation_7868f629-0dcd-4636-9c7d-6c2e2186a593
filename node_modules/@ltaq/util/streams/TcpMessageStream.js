"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const events_1 = require("events");
const utils_1 = require("./utils");
class TcpMessageStream extends events_1.EventEmitter {
    constructor(socket) {
        super();
        this.socket = socket;
        this.closing_ = false;
        this.buffer_ = new utils_1.LengthPrefixedBuffer((message) => this.emit('message', message));
        socket.on('connect', () => {
            if (!this.closing_)
                this.emit('connect');
        });
        socket.on('error', (error) => {
            if (!this.closing_) {
                this.closing_ = true;
                this.emit('close', error);
            }
        });
        socket.on('close', () => {
            if (!this.closing_) {
                this.closing_ = true;
                this.emit('close');
            }
        });
        socket.on('data', (data) => {
            if (!this.closing_)
                this.buffer_.append(data);
        });
    }
    close(error) {
        if (!this.closing_) {
            this.closing_ = true;
            this.socket.end();
            this.emit('close', error);
        }
    }
    write(...message) {
        if (!this.closing_) {
            let size = 0;
            for (let i = 0; i < message.length; i++)
                size += message[i].length;
            const head = Buffer.allocUnsafe(4);
            head.writeUInt32LE(size, 0);
            this.socket.write(head);
            for (let i = 0; i < message.length; i++)
                this.socket.write(message[i]);
        }
    }
}
exports.TcpMessageStream = TcpMessageStream;
