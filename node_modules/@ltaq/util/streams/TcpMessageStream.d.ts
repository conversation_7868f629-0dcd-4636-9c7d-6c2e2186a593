/// <reference types="node" />
import { EventEmitter } from "events";
import { MessageStream } from "./MessageStream";
import * as net from "net";
export declare class TcpMessageStream extends EventEmitter implements MessageStream {
    private socket;
    private buffer_;
    private closing_;
    constructor(socket: net.Socket);
    close(error?: Error): void;
    write(...message: Buffer[]): void;
}
