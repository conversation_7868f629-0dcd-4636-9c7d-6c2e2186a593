/// <reference types="node" />
export declare enum Command {
    connect = 1,
    close = 2,
    connect2 = 17,
    close2 = 18
}
export declare class EncoderHelper {
    static encodeString(buffer: Buffer, offset: number, value: string): number;
    static encodeStringLength(value: string): number;
    static decodeString(buffer: Buffer, offset: number): [string, number];
}
export declare class LengthPrefixedBuffer {
    private callback;
    private buffer_;
    private bufferSize_;
    constructor(callback: (message: Buffer) => void);
    append(data: Buffer): void;
}
