"use strict";
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
    result["default"] = mod;
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const events_1 = require("events");
const assert = __importStar(require("assert"));
const utils_1 = require("./utils");
// noinspection JSUnusedGlobalSymbols
class MultiplexBaseMessageStream extends events_1.EventEmitter {
    // noinspection JSUnusedGlobalSymbols
    constructor(main, mainConnected, handler) {
        super();
        this.main = main;
        this.mainConnected = mainConnected;
        this.handler = handler;
        this.outStreams_ = [];
        this.inStreams_ = [];
        this.heartbeatTimer_ = null;
        this.nextOutSubStreamId_ = 1;
        this.onMainMessage_ = (message) => {
            if (message.length < 4)
                return;
            let stream = message.readUInt32LE(0);
            if (stream === 0) {
                this.onCommandMessage_(message);
            }
            else {
                const flag = stream & (1 << 31);
                stream = stream & ~(1 << 31);
                if (flag) {
                    this.onOutStreamMessage_(stream, message);
                }
                else {
                    this.onInStreamMessage_(stream, message);
                }
            }
        };
        this.InSubStream = class extends events_1.EventEmitter {
            // noinspection JSUnusedLocalSymbols, JSUnusedGlobalSymbols
            constructor(owner, id, url, header) {
                super();
                this.owner = owner;
                this.id = id;
                this.url = url;
                this.header = header;
                this.state = ConnectionState.initial;
                this.connect = owner.inSubStreamConnected_.bind(this.owner, this);
                this.close = owner.closeInSubStream_.bind(this.owner, this);
                this.write = owner.writeInSubStream_.bind(this.owner, this);
            }
        };
        this.OutSubStream = class extends events_1.EventEmitter {
            // noinspection JSUnusedLocalSymbols, JSUnusedGlobalSymbols
            constructor(owner, id, url, header) {
                super();
                this.owner = owner;
                this.id = id;
                this.url = url;
                this.header = header;
                this.state = ConnectionState.initial;
                this.close = owner.closeOutSubStream_.bind(this.owner, this);
                this.write = owner.writeOutSubStream_.bind(this.owner, this);
            }
        };
        this.main.on('message', this.onMainMessage_.bind(this));
        this.main.on('close', this.onMainClose_.bind(this));
        if (!this.mainConnected)
            this.main.on('connect', this.onMainConnect_.bind(this));
        else
            this.onMainConnect_();
    }
    close(error) {
        this.main.close(error);
    }
    connect(url, header) {
        const stream = new this.OutSubStream(this, this.nextOutSubStreamId_++, url, header || Buffer.allocUnsafe(0));
        this.outStreams_.push(stream);
        if (this.mainConnected)
            this.connectOutSubStream_(stream);
        return stream;
    }
    onMainConnect_() {
        this.mainConnected = true;
        this.heartbeatTimer_ = setInterval(() => this.main.write(), 1000);
        for (let p of this.outStreams_) {
            this.connectOutSubStream_(p);
        }
    }
    onMainClose_(error) {
        for (const s of this.outStreams_) {
            s.state = ConnectionState.close;
            s.emit('close', error);
        }
        this.outStreams_.splice(0, this.outStreams_.length);
        for (const s of this.inStreams_) {
            s.state = ConnectionState.close;
            s.emit('close', error);
        }
        this.inStreams_.splice(0, this.inStreams_.length);
        this.emit('close', error);
    }
    onCommandMessage_(message) {
        const id = message.readUInt32LE(4);
        const cmd = message.readUInt8(8);
        switch (cmd) {
            case utils_1.Command.connect2: {
                const s = this.outStreams_.find(x => x.id === id);
                if (s && s.state === ConnectionState.connecting) {
                    s.state = ConnectionState.connect;
                    s.emit('connect');
                }
                break;
            }
            case utils_1.Command.close2: {
                const idx = this.outStreams_.findIndex(x => x.id === id);
                if (idx >= 0) {
                    const s = this.outStreams_[idx];
                    this.outStreams_.splice(idx, 1);
                    s.state = ConnectionState.close;
                    const error = MultiplexBaseMessageStream.readError_(message, 9);
                    s.emit('close', error);
                }
                break;
            }
            case utils_1.Command.connect: {
                const [url, offset] = utils_1.EncoderHelper.decodeString(message, 9);
                const header = message.slice(offset);
                const s = new this.InSubStream(this, id, url, header);
                this.inStreams_.push(s);
                if (!this.handler(s))
                    s.close();
                break;
            }
            case utils_1.Command.close: {
                const idx = this.inStreams_.findIndex(x => x.id === id);
                if (idx >= 0) {
                    const s = this.inStreams_[idx];
                    this.inStreams_.splice(idx, 1);
                    s.state = ConnectionState.close;
                    s.emit('close');
                }
                break;
            }
        }
    }
    /******************************************/
    onOutStreamMessage_(stream, message) {
        const s = this.outStreams_.find(x => x.id === stream);
        if (s && s.state === ConnectionState.connect) {
            s.emit('message', message.slice(4));
        }
    }
    connectOutSubStream_(subStream) {
        assert.ok(subStream.state === ConnectionState.initial);
        const urlSize = utils_1.EncoderHelper.encodeStringLength(subStream.url);
        const headerSize = subStream.header ? subStream.header.length : 0;
        const buffer = Buffer.allocUnsafe(4 + 4 + 1 + urlSize + headerSize);
        buffer.writeUInt32LE(0, 0);
        buffer.writeUInt32LE(subStream.id, 4);
        buffer.writeUInt8(utils_1.Command.connect, 8);
        utils_1.EncoderHelper.encodeString(buffer, 9, subStream.url);
        if (subStream.header)
            subStream.header.copy(buffer, 9 + urlSize);
        this.main.write(buffer);
        subStream.state = ConnectionState.connecting;
    }
    closeOutSubStream_(s, error) {
        if (s.state === ConnectionState.close)
            return;
        if (this.mainConnected) {
            let payloadSize = 4 + 4 + 2;
            const buffer = Buffer.allocUnsafe(payloadSize);
            buffer.writeUInt32LE(0, 0);
            buffer.writeUInt32LE(s.id, 4);
            buffer.writeUInt8(utils_1.Command.close, 8);
            buffer.writeUInt8(0, 9);
            this.main.write(buffer);
        }
        const idx = this.outStreams_.findIndex(x => x === s);
        this.outStreams_.splice(idx, 1);
        s.state = ConnectionState.close;
        s.emit('close', error);
    }
    writeOutSubStream_(s, message) {
        if (s.state === ConnectionState.connect) {
            const buffer = Buffer.allocUnsafe(4);
            buffer.writeUInt32LE(s.id, 0);
            this.main.write(buffer, message);
        }
    }
    /***********************************/
    onInStreamMessage_(stream, message) {
        const s = this.inStreams_.find(x => x.id === stream);
        if (s && s.state === ConnectionState.connect)
            s.emit('message', message.slice(4));
    }
    closeInSubStream_(s, error) {
        if (s.state === ConnectionState.close)
            return;
        assert.ok(this.mainConnected);
        let payloadSize = 4 + 4 + 2, code, msg;
        if (error) {
            code = error.code || error.name || '';
            msg = error.message || '';
            const codeLength = utils_1.EncoderHelper.encodeStringLength(code);
            const msgLength = utils_1.EncoderHelper.encodeStringLength(msg);
            payloadSize += codeLength + msgLength;
        }
        const buffer = Buffer.allocUnsafe(payloadSize);
        buffer.writeUInt32LE(0, 0);
        buffer.writeUInt32LE(s.id, 4);
        buffer.writeUInt8(utils_1.Command.close2, 8);
        buffer.writeUInt8(error ? 1 : 0, 9);
        if (error) {
            let offset = utils_1.EncoderHelper.encodeString(buffer, 10, code);
            utils_1.EncoderHelper.encodeString(buffer, offset, msg);
        }
        this.main.write(buffer);
        const idx = this.inStreams_.findIndex(x => x === s);
        this.inStreams_.splice(idx, 1);
        s.state = ConnectionState.close;
        s.emit('close', error);
    }
    inSubStreamConnected_(s) {
        if (s.state === ConnectionState.initial) {
            const buffer = Buffer.allocUnsafe(4 + 4 + 1);
            buffer.writeUInt32LE(0, 0);
            buffer.writeUInt32LE(s.id, 4);
            buffer.writeUInt8(utils_1.Command.connect2, 8);
            this.main.write(buffer);
            s.state = ConnectionState.connect;
            s.emit('connect');
        }
    }
    writeInSubStream_(s, message) {
        if (s.state === ConnectionState.connect) {
            const buffer = Buffer.allocUnsafe(4);
            buffer.writeUInt32LE((s.id | (1 << 31)) >>> 0, 0);
            this.main.write(buffer, message);
        }
    }
    static readError_(data, offset) {
        const hasError = data.readUInt8(offset++);
        let error, errorCode;
        if (hasError) {
            [errorCode, offset] = utils_1.EncoderHelper.decodeString(data, offset);
            const [errorMessage,] = utils_1.EncoderHelper.decodeString(data, offset);
            error = new Error(errorMessage);
            error.code = errorCode;
            return error;
        }
        return undefined;
    }
}
var ConnectionState;
(function (ConnectionState) {
    ConnectionState[ConnectionState["initial"] = 0] = "initial";
    ConnectionState[ConnectionState["connecting"] = 1] = "connecting";
    ConnectionState[ConnectionState["connect"] = 2] = "connect";
    ConnectionState[ConnectionState["close"] = 4] = "close";
})(ConnectionState || (ConnectionState = {}));
class MultiplexClientMessageStream extends MultiplexBaseMessageStream {
    constructor(main, handler) {
        super(main, false, handler);
    }
}
exports.MultiplexClientMessageStream = MultiplexClientMessageStream;
class MultiplexServerMessageStream extends MultiplexBaseMessageStream {
    constructor(main, handler) {
        super(main, true, handler);
    }
}
exports.MultiplexServerMessageStream = MultiplexServerMessageStream;
