"use strict";
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
    result["default"] = mod;
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const net = __importStar(require("net"));
const ws_1 = __importDefault(require("ws"));
const TcpMessageStream_1 = require("./TcpMessageStream");
const WebSocketMessageStream_1 = require("./WebSocketMessageStream");
// noinspection JSUnusedGlobalSymbols
class StreamHelper {
    static wrap(socket) {
        if (socket instanceof net.Socket) {
            return new TcpMessageStream_1.TcpMessageStream(socket);
        }
        else if (socket instanceof ws_1.default) {
            return new WebSocketMessageStream_1.WebSocketMessageStream(socket);
        }
        throw new TypeError();
    }
    // noinspection JSUnusedGlobalSymbols
    static bind(downstream, upstream, dataTransformer) {
        upstream.on('connect', () => downstream.connect());
        upstream.on('close', (error) => downstream.close(error));
        downstream.on('close', (error) => upstream.close(error));
        if (dataTransformer) {
            upstream.on('message', (data) => {
                downstream.write(dataTransformer.down(data));
            });
            downstream.on('message', (data) => {
                upstream.write(dataTransformer.up(data));
            });
        }
        else if (dataTransformer !== false) {
            upstream.on('message', (data) => downstream.write(data));
            downstream.on('message', (data) => upstream.write(data));
        }
    }
}
exports.StreamHelper = StreamHelper;
