/// <reference types="node" />
import { EventEmitter } from "events";
import * as net from "net";
declare class ZmqPubSub extends EventEmitter {
    private readonly type_;
    private state_;
    private connection_;
    private buffer_;
    private buffered_;
    private connected_;
    private pendingParts_;
    constructor(type: string, port: number, host: string);
    close(): void;
    setKeepAlive(enable: boolean, initialDelaySeconds?: number): net.Socket;
    get remote(): string | null;
    write(data: Buffer): void;
    send(...parts: Buffer[]): void;
    private onConnect_;
    private onError_;
    private onClose_;
    private onData_;
    private read_;
    private sendReady_;
    private readOnce_;
    protected emitMessage(parts: Buffer[]): void;
}
export declare class ZmqPub extends ZmqPubSub {
    topic_: Buffer;
    constructor(options: {
        port: number;
        host: string;
        topic: string;
    } | string);
    send(...parts: Buffer[]): void;
}
export declare class ZmqSub extends ZmqPubSub {
    constructor(options: {
        port: number;
        host: string;
        topic: string;
    } | string);
    protected emitMessage(parts: Buffer[]): void;
}
export {};
