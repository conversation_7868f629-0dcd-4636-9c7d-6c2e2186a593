"use strict";
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
    result["default"] = mod;
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const events_1 = require("events");
const net = __importStar(require("net"));
const zmq_1 = require("./zmq");
const ws_1 = __importDefault(require("ws"));
class AutoReconnectBase extends events_1.EventEmitter {
    constructor() {
        super();
        this.instance_ = null;
        this.failures_ = 0;
        this.retryTimer_ = null;
        this.closing_ = false;
        this.connected_ = false;
        this.defaultReconnectDelay_ = (x) => (1 << Math.max(Math.min(x + 5, 12), 7)) * 30;
        this.reconnectDelay_ = this.defaultReconnectDelay_;
        // this.on('error', (error) => void (error));
        this.retryTimer_ = setTimeout(this.connect_.bind(this), 0);
    }
    // noinspection JSUnusedGlobalSymbols
    get connected() {
        return this.connected_;
    }
    // noinspection JSUnusedGlobalSymbols
    setReconnectDelay(value) {
        if (value == null)
            this.reconnectDelay_ = this.defaultReconnectDelay_;
        else if (typeof (value) === 'number')
            this.reconnectDelay_ = () => value;
        else
            this.reconnectDelay_ = value;
    }
    on(type, listener) {
        return super.on(type, listener);
    }
    close() {
        this.closing_ = true;
        if (this.retryTimer_) {
            clearTimeout(this.retryTimer_);
            this.retryTimer_ = null;
        }
        if (this.instance_)
            this.close_(this.instance_);
    }
    connect_() {
        this.retryTimer_ = null;
        const callbacks = {
            that: this,
            closed: false,
            connected: false,
            onConnect() {
                if (!callbacks.connected) {
                    callbacks.connected = true;
                    callbacks.that.instanceConnect_();
                }
            },
            onClose(error) {
                if (!callbacks.closed) {
                    callbacks.closed = true;
                    callbacks.that.instanceClose_(error);
                }
            }
        };
        this.instance_ = this.create_(callbacks);
        this.emit('create', this.instance_);
    }
    instanceConnect_() {
        this.failures_ = 0;
        this.connected_ = true;
        this.emit("connection", this.instance_);
        this.emit("connect", this.instance_);
    }
    instanceClose_(error) {
        if (this.connected_) {
            this.connected_ = false;
            this.emit("disconnect", this.instance_);
        }
        if (error instanceof Error) {
            if (this.listenerCount('error') > 0)
                this.emit("error", this.instance_, error);
        }
        else {
            error = undefined;
        }
        this.emit('close', this.instance_, error);
        this.instance_ = null;
        if (!this.closing_) {
            this.failures_++;
            let delay = this.reconnectDelay_(this.failures_);
            this.retryTimer_ = setTimeout(this.connect_.bind(this), delay);
        }
    }
}
exports.AutoReconnectBase = AutoReconnectBase;
class AutoReconnectSocket extends AutoReconnectBase {
    constructor(options) {
        super();
        this.options = options;
    }
    close_(instance) {
        instance.end();
    }
    create_(options) {
        const instance = net.connect(this.options);
        instance.on('connect', options.onConnect);
        instance.on('close', options.onClose);
        instance.on('error', options.onClose);
        return instance;
    }
}
exports.AutoReconnectSocket = AutoReconnectSocket;
class AutoReconnectZmqSub extends AutoReconnectBase {
    constructor(options) {
        super();
        this.options = options;
    }
    close_(instance) {
        instance.close();
    }
    create_(options) {
        const instance = new zmq_1.ZmqSub(this.options);
        instance.on('connect', options.onConnect);
        instance.on('close', options.onClose);
        instance.on('error', options.onClose);
        return instance;
    }
}
exports.AutoReconnectZmqSub = AutoReconnectZmqSub;
class AutoReconnectZmqPub extends AutoReconnectBase {
    constructor(options) {
        super();
        this.options = options;
    }
    close_(instance) {
        instance.close();
    }
    create_(options) {
        const instance = new zmq_1.ZmqPub(this.options);
        instance.on('connect', options.onConnect);
        instance.on('close', options.onClose);
        instance.on('error', options.onClose);
        return instance;
    }
}
exports.AutoReconnectZmqPub = AutoReconnectZmqPub;
class AutoReconnectWebSocket extends AutoReconnectBase {
    constructor(url) {
        super();
        this.url = url;
    }
    close_(instance) {
        instance.close();
    }
    create_(options) {
        const instance = new ws_1.default(this.url);
        instance.on('open', options.onConnect);
        instance.on('close', options.onClose);
        instance.on('error', options.onClose);
        return instance;
    }
}
exports.AutoReconnectWebSocket = AutoReconnectWebSocket;
