"use strict";
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];
    result["default"] = mod;
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const events_1 = require("events");
const net = __importStar(require("net"));
const URL = __importStar(require("url"));
var ZmqProtocolState;
(function (ZmqProtocolState) {
    ZmqProtocolState[ZmqProtocolState["STATE_INITIAL"] = 0] = "STATE_INITIAL";
    ZmqProtocolState[ZmqProtocolState["STATE_GREETING"] = 1] = "STATE_GREETING";
    ZmqProtocolState[ZmqProtocolState["STATE_HANDSHAKE"] = 2] = "STATE_HANDSHAKE";
    ZmqProtocolState[ZmqProtocolState["STATE_TRAFFIC"] = 3] = "STATE_TRAFFIC";
})(ZmqProtocolState || (ZmqProtocolState = {}));
class ZmqPubSub extends events_1.EventEmitter {
    constructor(type, port, host) {
        super();
        this.buffer_ = Buffer.alloc(4096);
        this.buffered_ = 0;
        this.connected_ = false;
        this.pendingParts_ = [];
        this.type_ = Buffer.from(type, 'ascii');
        this.state_ = ZmqProtocolState.STATE_INITIAL;
        this.connection_ = net.connect(port, host);
        this.connection_.on('connect', this.onConnect_.bind(this));
        this.connection_.on('error', this.onError_.bind(this));
        this.connection_.on('data', this.onData_.bind(this));
        this.connection_.on('close', this.onClose_.bind(this));
        this.connection_.setKeepAlive(true);
    }
    close() {
        this.connection_.end();
        // this.connection_.destroy();
    }
    setKeepAlive(enable, initialDelaySeconds) {
        return this.connection_.setKeepAlive(enable, initialDelaySeconds);
    }
    get remote() {
        if (this.connection_)
            return this.connection_.remoteFamily
                + "://" + this.connection_.remoteAddress
                + ":" + this.connection_.remotePort;
        return null;
    }
    write(data) {
        this.connection_.write(data);
    }
    send(...parts) {
        for (let idx = 0; idx < parts.length; idx++) {
            let data = parts[idx];
            let more = idx !== (parts.length - 1);
            let sz = Buffer.alloc(9);
            let size = data.length;
            if (size < 256) {
                sz[0] = more ? 1 : 0;
                sz[1] = size;
                this.write(sz.slice(0, 2));
            }
            else {
                sz[0] = more ? 3 : 2;
                for (let i = 8, j; i > 0; i--) {
                    j = size % 256;
                    sz[i] = j;
                    size -= j;
                    size /= 256;
                }
                this.write(sz);
            }
            this.write(data);
        }
    }
    onConnect_() {
        this.state_ = ZmqProtocolState.STATE_GREETING;
        this.write(GREETING);
    }
    onError_(error) {
        this.emit('error', error);
    }
    onClose_() {
        if (this.connected_)
            this.emit('disconnect');
        this.emit('close');
    }
    onData_(data) {
        if (this.buffered_ + data.length > this.buffer_.length) {
            let l = this.buffered_ + data.length + 4095;
            l = l - (l % 4096);
            let buffer = Buffer.allocUnsafe(l);
            this.buffer_.copy(buffer, 0, 0, this.buffered_);
            this.buffer_ = buffer;
        }
        data.copy(this.buffer_, this.buffered_, 0, data.length);
        this.buffered_ += data.length;
        let read = this.read_(this.buffer_, this.buffered_);
        if (this.buffered_ > read)
            this.buffer_.copy(this.buffer_, 0, read, this.buffered_);
        this.buffered_ = this.buffered_ - read;
    }
    read_(data, length) {
        let offset = 0;
        while (offset < length) {
            let offset2 = this.readOnce_(data, length, offset);
            if (offset2 === offset)
                return offset;
            offset = offset2;
        }
        return offset;
    }
    sendReady_() {
        let ready = Buffer.from("\x04\x19" + "\x05READY"
            + "\x0bSocket-Type" + "\x00\x00\x00\x03", 'ascii');
        ready[1] = ready.length - 2 + this.type_.length;
        ready[ready.length - 1] = this.type_.length;
        this.write(ready);
        this.write(this.type_);
    }
    readOnce_(data, length, offset) {
        if (this.state_ == ZmqProtocolState.STATE_GREETING) {
            let offset2 = offset + 64;
            if (length >= offset2) {
                this.state_ = ZmqProtocolState.STATE_HANDSHAKE;
                this.sendReady_();
                offset = offset2;
            }
            return offset;
        }
        else if (this.state_ == ZmqProtocolState.STATE_HANDSHAKE) {
            let [head, offset2, offset3] = readCommandOrMessage(data, length, offset);
            if (head >= 0 && (head & 4)) {
                if (READY.compare(data, offset2, offset2 + 6) === 0) {
                    this.state_ = ZmqProtocolState.STATE_TRAFFIC;
                    this.connected_ = true;
                    this.emit('connect');
                }
            }
            return offset3;
        }
        else {
            let [head, offset2, offset3] = readCommandOrMessage(data, length, offset);
            if (head >= 0 && (head & 4) == 0) {
                this.pendingParts_.push(data.slice(offset2, offset3));
                if (!(head & 1)) {
                    this.emitMessage(this.pendingParts_);
                    this.pendingParts_.splice(0, this.pendingParts_.length);
                }
            }
            return offset3;
        }
    }
    emitMessage(parts) {
        this.emit('message', ...this.pendingParts_);
    }
}
class ZmqPub extends ZmqPubSub {
    constructor(options) {
        if (typeof options === 'string') {
            const obj = URL.parse(options);
            options = { port: parseInt(obj.port), host: obj.hostname, topic: obj.pathname.substr(1) };
        }
        super("PUB", options.port, options.host);
        this.topic_ = Buffer.allocUnsafe(Buffer.byteLength(options.topic, "utf8") + 1);
        this.topic_.write(options.topic, "utf8");
        this.topic_.writeUInt8(0, this.topic_.length - 1);
    }
    send(...parts) {
        parts.unshift(this.topic_);
        super.send(...parts);
    }
}
exports.ZmqPub = ZmqPub;
class ZmqSub extends ZmqPubSub {
    constructor(options) {
        if (typeof options === 'string') {
            const obj = URL.parse(options);
            options = { port: parseInt(obj.port), host: obj.hostname, topic: obj.pathname.substr(1) };
        }
        super("SUB", options.port, options.host);
        let topic = Buffer.allocUnsafe(Buffer.byteLength(options.topic, "utf8") + 2);
        topic.writeUInt8(1, 0);
        topic.write(options.topic, 1, "utf8");
        topic.writeUInt8(0, topic.length - 1);
        this.on('connect', () => this.send(topic));
    }
    emitMessage(parts) {
        this.emit('message', ...parts.slice(1));
    }
}
exports.ZmqSub = ZmqSub;
function readCommandOrMessage(data, length, offset) {
    const offset0 = offset;
    let offset2 = offset + 2;
    if (offset2 > length)
        return [-1, offset0, offset0];
    let head = data[offset++];
    if ((head & 2) && offset + 8 > length)
        return [-1, offset0, offset0];
    let size;
    if (head & 2) {
        size = readUInt64(data, offset);
        offset += 8;
    }
    else
        size = data[offset++];
    if (offset + size > length)
        return [-1, offset0, offset0];
    return [head, offset, offset + size];
}
function readUInt64(data, offset) {
    let h = data.readUInt32BE(offset);
    let l = data.readUInt32BE(offset + 4);
    return h * 0xffffffff + l;
}
const GREETING = Buffer.from("\xff\x00\x00\x00\x00\x00\x00\x00\x00\x7f" + "\x03\x00"
    + "NULL\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00" + "\x00"
    + "\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"
    + "\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00", 'ascii');
const READY = Buffer.from("\x05READY", "ascii");
