{"name": "@ltaq/mysqlaa", "version": "3.3.1", "description": "Async/Await mysql client libraray.", "main": "index.js", "repository": "git@************:utilities/node-mysqlaa.git", "author": "wyj", "license": "UNLICENSED", "files": ["index.js", "definitions/mysqlaa.d.ts"], "typings": "./definitions/mysqlaa.d.ts", "dependencies": {"mysql": "^2.18.1"}, "devDependencies": {"@types/mysql": "^2.15.21", "@types/node": "^17.0.25"}}