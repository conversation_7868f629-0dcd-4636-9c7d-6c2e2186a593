"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createConnection = exports.createPool = void 0;
const mysql = require("mysql");
const packets_1 = require("mysql/lib/protocol/packets");
const Pool = require("mysql/lib/Pool");
class MysqlPromiseQueryable {
    constructor(inner) {
        this.inner_ = inner;
    }
    query(sql, values) {
        let inner = this.inner_;
        return new Promise(function (resolve, reject) {
            inner.query(sql, values, function (error, rows, fields) {
                if (error)
                    reject(error);
                else
                    resolve(convertResult(rows, fields));
            });
        });
    }
    async single(sql, params) {
        let r = await this.query(sql, params);
        if (r instanceof Array)
            return (r[r.length - 1]);
        return r;
    }
    ;
    async loadRow(sql, params) {
        let rows = await this.loadRows(sql, params);
        return rows.length ? rows[0] : void (0);
    }
    async loadRows(sql, params) {
        let result = (await this.single(sql, params));
        return result.rows;
    }
    async loadScalar(sql, params) {
        let result = (await this.single(sql, params));
        if (!result.rows.length)
            return void (0);
        let row = result.rows[0];
        return row[result.fields[0].name];
    }
    async loadScalars(sql, params) {
        let result = (await this.single(sql, params));
        if (!result.rows.length)
            return [];
        let fieldName = result.fields[0].name;
        return result.rows.map((x) => x[fieldName]);
    }
}
class MysqlPromiseConnection extends MysqlPromiseQueryable {
    constructor(inner, fromPool) {
        super(inner);
        this.fromPool_ = fromPool;
    }
    release() {
        console.warn("MysqlPromiseConnection: release() is deprecated, use end() instead.");
        this.end();
    }
    end() {
        if (this.fromPool_)
            this.inner_.release();
        else
            this.inner_.end();
    }
    beginTransaction() {
        let inner = this.inner_;
        return new Promise(function (resolve, reject) {
            inner.beginTransaction(function (error) {
                if (error)
                    reject(error);
                else
                    resolve();
            });
        });
    }
    commit() {
        let inner = this.inner_;
        return new Promise(function (resolve, reject) {
            inner.commit(function (error) {
                if (error)
                    reject(error);
                else
                    resolve();
            });
        });
    }
    rollback() {
        let inner = this.inner_;
        return new Promise(function (resolve, reject) {
            inner.rollback(function () {
                resolve();
            });
        });
    }
}
function defaultTypeCast(field, next) {
    if (field.type === "TINY" && field.length === 1) {
        let str = field.string();
        return str === null ? null : str.charCodeAt(0) !== 48;
    }
    return next();
}
function convertResult(rows, fields) {
    if (rows instanceof packets_1.OkPacket) {
        return rows;
    }
    else if (rows.length === 0 || rows[0] instanceof packets_1.RowDataPacket) {
        return { rows: rows, fields: fields };
    }
    else {
        let result = [];
        for (let i = 0; i < rows.length; i++) {
            if (rows[i] instanceof packets_1.OkPacket)
                result.push(rows[i]);
            else
                result.push({ rows: rows[i], fields: fields[i] });
        }
        return result;
    }
}
class MysqlPromisePool extends MysqlPromiseQueryable {
    constructor(options) {
        let pool;
        if (options instanceof Pool) {
            pool = options;
        }
        else {
            let cfg = options;
            if (("name" in cfg) && !("database" in cfg)) {
                cfg.database = cfg["name"];
            }
            cfg.typeCast = cfg.typeCast === void (0) ? true : cfg.typeCast;
            if (cfg.typeCast && typeof (cfg.typeCast) !== "function") {
                cfg.typeCast = defaultTypeCast;
            }
            pool = mysql.createPool(cfg);
        }
        super(pool);
    }
    getConnection() {
        let inner = this.inner_;
        return new Promise(function (resolve, reject) {
            inner.getConnection(function (error, connection) {
                if (error)
                    reject(error);
                else {
                    resolve(new MysqlPromiseConnection(connection, true));
                }
            });
        });
    }
    ;
    end(ignoreError = true) {
        let inner = this.inner_;
        return new Promise(function (resolve, reject) {
            inner.end(function (error) {
                if (!error)
                    return void resolve();
                if (!ignoreError)
                    return void reject(error);
                console.log(error);
                return void resolve();
            });
        });
    }
    ;
    async transaction(sql, params, retryOnDeadLock) {
        let ret, error, retry = 0;
        if (typeof (sql) === "function")
            retryOnDeadLock = params;
        if (Number.isInteger(retryOnDeadLock) && retryOnDeadLock > 0)
            retry = retryOnDeadLock;
        else
            retry = retryOnDeadLock ? 1 : 0;
        let c = await this.getConnection();
        try {
            await c.beginTransaction();
            while (true) {
                try {
                    if (typeof (sql) === "function") {
                        ret = await sql(c, (x) => void (error = x));
                    }
                    else {
                        ret = await c.query(sql, params);
                    }
                    await c.commit();
                }
                catch (e) {
                    if (--retry > 0 && e.code === "ER_LOCK_DEADLOCK")
                        continue;
                    await c.rollback();
                    throw e;
                }
                break;
            }
        }
        finally {
            c.release();
        }
        if (error)
            throw error;
        return ret;
    }
    ;
}
function createPool(options) {
    return new MysqlPromisePool(options);
}
exports.createPool = createPool;
function createConnection(options) {
    let connection = mysql.createConnection(options);
    return new MysqlPromiseConnection(connection, false);
}
exports.createConnection = createConnection;
