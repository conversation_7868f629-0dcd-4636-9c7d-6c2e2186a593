import * as mysql from "mysql";

export interface QueryResult {
    rows:any[];
    fields:any[];
}
export interface NonQueryResult {
    insertId:number;
    affectedRows:number;
    changedRows:number;
}

export type StatementResult = (QueryResult|NonQueryResult);

export interface PromiseConnection {
    beginTransaction():Promise<void>;
    commit():Promise<void>;
    rollback():Promise<void>;
    release():void;
    end():void;

    query(sql:string, values?:any|any[]):Promise<StatementResult|StatementResult[]>;
    single(sql:string, values?:any|any[]):Promise<StatementResult>;
    loadRow(sql: string, params?: any | any[]):Promise<any>;
    loadRows(sql: string, params?: any | any[]):Promise<any[]>;
    loadScalar(sql: string, params?: any | any[]):Promise<any>;
    loadScalars(sql: string, params?: any | any[]):Promise<any[]>;
}

export interface PromisePool {
    getConnection():Promise<PromiseConnection>;
    end():void;
    transaction(sql: string, params?: any | any[], retryOnDeadLock?:number|boolean):Promise<any|StatementResult|StatementResult[]>;
    transaction(fn:(c:PromiseConnection, setError:(error:any)=>void)=>Promise<any>, retryOnDeadLock?:number|boolean):Promise<any>;

    query(sql:string, values?:any|any[]):Promise<StatementResult|StatementResult[]>;
    single(sql:string, values?:any|any[]):Promise<StatementResult>;
    loadRow(sql: string, params?: any | any[]):Promise<any>;
    loadRows(sql: string, params?: any | any[]):Promise<any[]>;
    loadScalar(sql: string, params?: any | any[]):Promise<any>;
    loadScalars(sql: string, params?: any | any[]):Promise<any[]>;
}

export function createPool(options:mysql.PoolConfig):PromisePool;
export function createConnection(options:mysql.ConnectionConfig):PromiseConnection;
