{"name": "pg-pool", "version": "3.8.0", "description": "Connection pool for node-postgres", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": " node_modules/.bin/mocha"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg-pool"}, "keywords": ["pg", "postgres", "pool", "database"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/brianc/node-pg-pool/issues"}, "homepage": "https://github.com/brianc/node-pg-pool#readme", "devDependencies": {"bluebird": "3.7.2", "co": "4.6.0", "expect.js": "0.3.1", "lodash": "^4.17.11", "mocha": "^10.5.2", "pg-cursor": "^1.3.0"}, "peerDependencies": {"pg": ">=8.0"}, "gitHead": "f7c92e487c6a9c9600585f9de14cb17e7a65e76e"}