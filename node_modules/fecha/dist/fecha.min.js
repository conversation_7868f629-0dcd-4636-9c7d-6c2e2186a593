!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):n.fecha=t()}(this,function(){"use strict";var n={},t=/d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|([HhMsDm])\1?|[aA]|"[^"]*"|'[^']*'/g,e="[^\\s]+",r=/\[([^]*?)\]/gm,u=function(){};function o(n,t){for(var e=[],r=0,u=n.length;r<u;r++)e.push(n[r].substr(0,t));return e}function a(n){return function(t,e,r){var u=r[n].indexOf(e.charAt(0).toUpperCase()+e.substr(1).toLowerCase());~u&&(t.month=u)}}function i(n,t){for(n=String(n),t=t||2;n.length<t;)n="0"+n;return n}var s=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],d=["January","February","March","April","May","June","July","August","September","October","November","December"],f=o(d,3),m=o(s,3);n.i18n={dayNamesShort:m,dayNames:s,monthNamesShort:f,monthNames:d,amPm:["am","pm"],DoFn:function(n){return n+["th","st","nd","rd"][n%10>3?0:(n-n%10!=10)*n%10]}};var c={D:function(n){return n.getDate()},DD:function(n){return i(n.getDate())},Do:function(n,t){return t.DoFn(n.getDate())},d:function(n){return n.getDay()},dd:function(n){return i(n.getDay())},ddd:function(n,t){return t.dayNamesShort[n.getDay()]},dddd:function(n,t){return t.dayNames[n.getDay()]},M:function(n){return n.getMonth()+1},MM:function(n){return i(n.getMonth()+1)},MMM:function(n,t){return t.monthNamesShort[n.getMonth()]},MMMM:function(n,t){return t.monthNames[n.getMonth()]},YY:function(n){return i(String(n.getFullYear()),4).substr(2)},YYYY:function(n){return i(n.getFullYear(),4)},h:function(n){return n.getHours()%12||12},hh:function(n){return i(n.getHours()%12||12)},H:function(n){return n.getHours()},HH:function(n){return i(n.getHours())},m:function(n){return n.getMinutes()},mm:function(n){return i(n.getMinutes())},s:function(n){return n.getSeconds()},ss:function(n){return i(n.getSeconds())},S:function(n){return Math.round(n.getMilliseconds()/100)},SS:function(n){return i(Math.round(n.getMilliseconds()/10),2)},SSS:function(n){return i(n.getMilliseconds(),3)},a:function(n,t){return n.getHours()<12?t.amPm[0]:t.amPm[1]},A:function(n,t){return n.getHours()<12?t.amPm[0].toUpperCase():t.amPm[1].toUpperCase()},ZZ:function(n){var t=n.getTimezoneOffset();return(t>0?"-":"+")+i(100*Math.floor(Math.abs(t)/60)+Math.abs(t)%60,4)}},h={D:["\\d\\d?",function(n,t){n.day=t}],Do:["\\d\\d?"+e,function(n,t){n.day=parseInt(t,10)}],M:["\\d\\d?",function(n,t){n.month=t-1}],YY:["\\d\\d?",function(n,t){var e=+(""+(new Date).getFullYear()).substr(0,2);n.year=""+(t>68?e-1:e)+t}],h:["\\d\\d?",function(n,t){n.hour=t}],m:["\\d\\d?",function(n,t){n.minute=t}],s:["\\d\\d?",function(n,t){n.second=t}],YYYY:["\\d{4}",function(n,t){n.year=t}],S:["\\d",function(n,t){n.millisecond=100*t}],SS:["\\d{2}",function(n,t){n.millisecond=10*t}],SSS:["\\d{3}",function(n,t){n.millisecond=t}],d:["\\d\\d?",u],ddd:[e,u],MMM:[e,a("monthNamesShort")],MMMM:[e,a("monthNames")],a:[e,function(n,t,e){var r=t.toLowerCase();r===e.amPm[0]?n.isPm=!1:r===e.amPm[1]&&(n.isPm=!0)}],ZZ:["[^\\s]*?[\\+\\-]\\d\\d:?\\d\\d|[^\\s]*?Z",function(n,t){var e,r=(t+"").match(/([+-]|\d\d)/gi);r&&(e=60*r[1]+parseInt(r[2],10),n.timezoneOffset="+"===r[0]?e:-e)}]};return h.dd=h.d,h.dddd=h.ddd,h.DD=h.D,h.mm=h.m,h.hh=h.H=h.HH=h.h,h.MM=h.M,h.ss=h.s,h.A=h.a,n.masks={default:"ddd MMM DD YYYY HH:mm:ss",shortDate:"M/D/YY",mediumDate:"MMM D, YYYY",longDate:"MMMM D, YYYY",fullDate:"dddd, MMMM D, YYYY",shortTime:"HH:mm",mediumTime:"HH:mm:ss",longTime:"HH:mm:ss.SSS"},n.format=function(e,u,o){var a=o||n.i18n;if("number"==typeof e&&(e=new Date(e)),"[object Date]"!==Object.prototype.toString.call(e)||isNaN(e.getTime()))throw new Error("Invalid Date in fecha.format");var i=[];return(u=(u=(u=n.masks[u]||u||n.masks.default).replace(r,function(n,t){return i.push(t),"@@@"})).replace(t,function(n){return n in c?c[n](e,a):n.slice(1,n.length-1)})).replace(/@@@/g,function(){return i.shift()})},n.parse=function(e,u,o){var a=o||n.i18n;if("string"!=typeof u)throw new Error("Invalid format in fecha.parse");if(u=n.masks[u]||u,e.length>1e3)return null;var i={},s=[],d=[];u=u.replace(r,function(n,t){return d.push(t),"@@@"});var f,m=(f=u,f.replace(/[|\\{()[^$+*?.-]/g,"\\$&")).replace(t,function(n){if(h[n]){var t=h[n];return s.push(t[1]),"("+t[0]+")"}return n});m=m.replace(/@@@/g,function(){return d.shift()});var c=e.match(new RegExp(m,"i"));if(!c)return null;for(var l=1;l<c.length;l++)s[l-1](i,c[l],a);var M,g=new Date;return!0===i.isPm&&null!=i.hour&&12!=+i.hour?i.hour=+i.hour+12:!1===i.isPm&&12==+i.hour&&(i.hour=0),null!=i.timezoneOffset?(i.minute=+(i.minute||0)-+i.timezoneOffset,M=new Date(Date.UTC(i.year||g.getFullYear(),i.month||0,i.day||1,i.hour||0,i.minute||0,i.second||0,i.millisecond||0))):M=new Date(i.year||g.getFullYear(),i.month||0,i.day||1,i.hour||0,i.minute||0,i.second||0,i.millisecond||0),M},n});
