{"name": "fecha", "version": "3.0.3", "description": "Date formatting and parsing", "main": "lib/fecha.umd.js", "module": "src/fecha.js", "scripts": {"test": "eslint src && nyc --cache --reporter=text painless test.*", "build": "NODE_ENV=production rollup -c"}, "repository": {"type": "git", "url": "https://<EMAIL>/taylorhakes/fecha.git"}, "keywords": ["date", "parse", "moment", "format", "fecha", "formatting"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/taylorhakes/fecha/issues"}, "homepage": "https://github.com/taylorhakes/fecha", "devDependencies": {"eslint": "^5.8.0", "nyc": "^13.1.0", "painless": "^0.9.7", "rollup": "^0.59.0", "rollup-plugin-uglify": "^3.0.0"}, "files": ["src/fecha.js", "src/fecha.d.ts", "lib/fecha.umd.js", "dist/fecha.min.js"], "types": "src/fecha.d.ts"}